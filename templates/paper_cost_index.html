<!DOCTYPE html>
<html>
<head>
    <title>用纸计算器</title>
</head>
<body>
    <h1>用纸计算器</h1>

    <label for="equipment_length">设备长度 (mm):</label>
    <input type="number" id="equipment_length" value="1000"><br><br>

    <label for="equipment_width">设备宽度 (mm):</label>
    <input type="number" id="equipment_width" value="700"><br><br>

    <label for="product_length">产品长度 (mm):</label>
    <input type="number" id="product_length" value="456"><br><br>

    <label for="product_width">产品宽度 (mm):</label>
    <input type="number" id="product_width" value="290"><br><br>

    <label for="bleed">出血 (mm):</label>
    <input type="number" id="bleed" value="3"><br><br>

    <label for="order_quantity">订单数量:</label>
    <input type="number" id="order_quantity" value="1000"><br><br>

    <label for="unit_price_per_square_meter">纸张单价 (元/平方米):</label>
    <input type="number" id="unit_price_per_square_meter" value="1.75"><br><br>

    <h2>损耗规则</h2>
    <label for="base_quantity">基础数量:</label>
    <input type="number" id="base_quantity" value="10000"><br><br>

    <label for="base_spoilage">基础损耗:</label>
    <input type="number" id="base_spoilage" value="300"><br><br>

    <label for="increment_quantity">递增数量:</label>
    <input type="number" id="increment_quantity" value="1000"><br><br>

    <label for="increment_spoilage">递增损耗:</label>
    <input type="number" id="increment_spoilage" value="15"><br><br>

    <label for="margin">余量 (mm):</label>
    <input type="number" id="margin" value="10"><br><br>

    <button onclick="calculatePaperCost()">计算</button>

    <div id="result"></div>

    <script>
        async function calculatePaperCost() {
            const equipment_length = document.getElementById("equipment_length").value;
            const equipment_width = document.getElementById("equipment_width").value;
            const product_length = document.getElementById("product_length").value;
            const product_width = document.getElementById("product_width").value;
            const bleed = document.getElementById("bleed").value;
            const order_quantity = document.getElementById("order_quantity").value;
            const unit_price_per_square_meter = document.getElementById("unit_price_per_square_meter").value;
            const base_quantity = document.getElementById("base_quantity").value;
            const base_spoilage = document.getElementById("base_spoilage").value;
            const increment_quantity = document.getElementById("increment_quantity").value;
            const increment_spoilage = document.getElementById("increment_spoilage").value;
            const margin = document.getElementById("margin").value;

            const data = {
                equipment_length: parseFloat(equipment_length),
                equipment_width: parseFloat(equipment_width),
                product_length: parseFloat(product_length),
                product_width: parseFloat(product_width),
                bleed: parseFloat(bleed),
                order_quantity: parseInt(order_quantity),
                unit_price_per_square_meter: parseFloat(unit_price_per_square_meter),
                spoilage_rules: {
                    base_quantity: parseInt(base_quantity),
                    base_spoilage: parseInt(base_spoilage),
                    increment_quantity: parseInt(increment_quantity),
                    increment_spoilage: parseInt(increment_spoilage)
                },
                margin: parseFloat(margin)
            };

            const response = await fetch("/paper_cost/calculate_paper_cost/", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            document.getElementById("result").innerText = result.result;
        }
    </script>
</body>
</html>
