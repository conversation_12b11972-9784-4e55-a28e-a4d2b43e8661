<!DOCTYPE html>
<html>
<head>
    <title>模数计算</title>
    <style>
        body { font-family: sans-serif; line-height: 1.6; margin: 20px; }
        label { display: block; margin-bottom: 5px; }
        input { margin-bottom: 10px; padding: 8px; }
        button { padding: 10px 15px; background-color: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        #results { margin-top: 20px; border-top: 1px solid #ccc; padding-top: 15px; }
    </style>
</head>
<body>
    <h1>模数计算</h1>

    <form id="calculationForm">
        <label for="eqp_len">设备长 (mm):</label>
        <input type="number" id="eqp_len" name="eqp_len" required><br>

        <label for="eqp_wid">设备宽 (mm):</label>
        <input type="number" id="eqp_wid" name="eqp_wid" required><br>

        <label for="prod_len">成品长 (mm):</label>
        <input type="number" id="prod_len" name="prod_len" required><br>

        <label for="prod_wid">成品宽 (mm):</label>
        <input type="number" id="prod_wid" name="prod_wid" required><br>

        <label for="bleed">出血宽度 (mm):</label>
        <input type="number" id="bleed" name="bleed" value="3" required><br>

        <label for="margin">余量 (mm):</label>
        <input type="number" id="margin" name="margin" value="10" required><br>

        <button type="submit">计算</button>
    </form>

    <div id="results">
        <h2>计算结果:</h2>
        <p id="max_num"></p>
        <p id="best_method"></p>
        <p id="horizontal_layout"></p>
        <p id="vertical_layout"></p>
        <p id="paper_size"></p>
        <pre id="detailed_result"></pre>
    </div>

    <script>
        document.getElementById('calculationForm').addEventListener('submit', async function(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());

            // Convert string values to numbers
            for (const key in data) {
                data[key] = parseFloat(data[key]);
            }

            try {
                const response = await fetch('/modulus/calculate/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                document.getElementById('max_num').textContent = `最大模数: ${result.max_num}`;
                document.getElementById('best_method').textContent = `方式: ${result.best_method}`;
                document.getElementById('horizontal_layout').textContent = `横向排版: ${result.horizontal_layout} 个`;
                document.getElementById('vertical_layout').textContent = `纵向排版: ${result.vertical_layout} 个`;
                document.getElementById('paper_size').textContent = `所需纸张尺寸(含余量): ${result.paper_length} x ${result.paper_width}`;
                document.getElementById('detailed_result').textContent = result.detailed_result;

            } catch (error) {
                console.error('Error:', error);
                document.getElementById('results').innerHTML = `<h2>计算结果:</h2><p style="color: red;">计算出错: ${error.message}</p>`;
            }
        });
    </script>
</body>
</html>
