# 包装印刷算法计算工具

## 项目概述
本项目是一个基于 FastAPI 的 Web 应用，整合了包装印刷行业常用的多种算法计算工具。这些工具帮助用户快速计算包装印刷过程中的各项成本和参数，包括局部UV、模切、烫金、贴窗等多种后加工工艺的成本计算。

## 包含的计算功能
1. **局部UV计算**：计算局部UV加工费用
2. **模切计算**：计算模切加工费用
3. **烫金工艺计算**：计算烫金加工费用
4. **贴窗计算**：计算贴窗加工费用
5. **击凸计算**：计算击凸加工费用
6. **压凹计算**：计算压凹加工费用
7. **模数计算**：计算最佳排版模数和纸张尺寸
8. **印刷计价**：计算印刷成本
9. **用纸计算**：计算纸张用量和成本

## 技术栈
- Python 3.12
- FastAPI (Web框架)
- Uvicorn (ASGI服务器)
- Jinja2 (模板引擎)
- Pydantic (数据验证)

## 运行方式
1. 确保已安装 `uv` (Python包管理工具)
2. 在项目根目录执行以下命令：
```bash
uv venv
uv pip install -r requirements.txt
uv run uvicorn main_app:app --host 0.0.0.0 --port 8000
```

## 访问应用
1. 应用启动后，访问：http://localhost:8000
2. 主页将显示所有可用计算功能的链接
3. 每个功能都有独立的计算页面

## 使用说明
1. 访问主页 (http://localhost:8000) 查看所有可用算法
2. 点击所需算法的链接进入计算页面
3. 输入相关参数并点击"计算"按钮获取结果
4. 结果将直接显示在页面上

## 项目结构
```
.
├── main_app.py         # 主应用文件
├── requirements.txt    # 依赖列表
├── README.md           # 项目说明
├── static/             # 静态文件目录
└── templates/          # HTML模板
    ├── main_index.html # 主页模板
    └── ...             # 各算法的模板文件
```

## 贡献
欢迎通过 issue 或 pull request 贡献代码和改进建议。
