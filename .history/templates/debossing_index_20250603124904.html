<!DOCTYPE html>
<html>
<head>
    <title>压凹加工费计算</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="number"], input[type="text"] { margin-bottom: 10px; padding: 8px; width: 200px; }
        button { padding: 10px 15px; background-color: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        #results { margin-top: 20px; border-top: 1px solid #ccc; padding-top: 15px; }
        #results p { margin-bottom: 5px; }
        .tier-input { margin-left: 20px; border: 1px solid #eee; padding: 10px; margin-bottom: 10px; }
    </style>
</head>
<body>
    <h1>压凹加工费计算</h1>

    <form id="calculationForm">
        <h2>基本参数</h2>
        <label for="sheet_count">印张数量:</label>
        <input type="number" id="sheet_count" name="sheet_count" required><br>

        <label for="hit_count">压凹次数:</label>
        <input type="number" id="hit_count" name="hit_count" required><br>

        <label for="area_list_cm2">每个压凹位的面积 (cm², 逗号分隔):</label>
        <input type="text" id="area_list_cm2" name="area_list_cm2" required><br>

        <h2>加工费参数 (pricing_parameters)</h2>
        <label for="startup_fee">启动费用:</label>
        <input type="number" id="startup_fee" name="startup_fee" step="0.01" required><br>

        <label for="hit_price">每次压凹费用:</label>
        <input type="number" id="hit_price" name="hit_price" step="0.01" required><br>

        <h3>数量分段计价 (quantity_ranges)</h3>
        <div id="quantityRanges">
            <div class="tier-input">
                <label for="tier1_upper_bound">上限 (Tier 1):</label>
                <input type="number" class="upper-bound" value="1000" step="1"><br>
                <label for="tier1_unit_price">单价 (Tier 1):</label>
                <input type="number" class="unit-price" value="0.05" step="0.01">
            </div>
             <div class="tier-input">
                <label for="tier2_upper_bound">上限 (Tier 2):</label>
                <input type="number" class="upper-bound" value="10000" step="1"><br>
                <label for="tier2_unit_price">单价 (Tier 2):</label>
                <input type="number" class="unit-price" value="0.05" step="0.01">
            </div>
             <div class="tier-input">
                <label for="tier3_upper_bound">上限 (Tier 3, 留空表示无上限):</label>
                <input type="text" class="upper-bound" value=""><br>
                <label for="tier3_unit_price">单价 (Tier 3):</label>
                <input type="number" class="unit-price" value="0.04" step="0.01">
            </div>
        </div>
        <!-- 可以添加按钮来动态添加更多分段 -->

        <h2>版费参数 (plate_cost_parameters)</h2>
        <label for="min_cost">最低版费:</label>
        <input type="number" id="min_cost" name="min_cost" step="0.01" required><br>

        <label for="unit_price_per_cm2">每平方厘米单价:</label>
        <input type="number" id="unit_price_per_cm2" name="unit_price_per_cm2" step="0.01" required><br>

        <button type="submit">计算</button>
    </form>

    <div id="results">
        <h2>计算结果</h2>
        <p><strong>加工费:</strong> <span id="processingFee">-</span></p>
        <p><strong>压凹版费:</strong> <span id="plateCost">-</span></p>
        <p><strong>总价:</strong> <span id="totalCost">-</span></p>
        <p style="color: red;" id="error">-</p>
    </div>

    <script>
        document.getElementById('calculationForm').addEventListener('submit', async function(event) {
            event.preventDefault();

            const sheetCount = parseInt(document.getElementById('sheet_count').value);
            const hitCount = parseInt(document.getElementById('hit_count').value);
            const areaListCm2 = document.getElementById('area_list_cm2').value.split(',').map(area => parseFloat(area.trim())).filter(area => !isNaN(area));

            const startupFee = parseFloat(document.getElementById('startup_fee').value);
            const hitPrice = parseFloat(document.getElementById('hit_price').value);

            const quantityRanges = [];
            document.querySelectorAll('#quantityRanges .tier-input').forEach(tierDiv => {
                const upperBoundInput = tierDiv.querySelector('.upper-bound');
                const unitPriceInput = tierDiv.querySelector('.unit-price');

                const upperBound = upperBoundInput.value === '' ? null : parseFloat(upperBoundInput.value);
                const unitPrice = parseFloat(unitPriceInput.value);

                if (!isNaN(unitPrice)) {
                    quantityRanges.push({
                        upper_bound: upperBound,
                        unit_price: unitPrice
                    });
                }
            });

            const minCost = parseFloat(document.getElementById('min_cost').value);
            const unitPricePerCm2 = parseFloat(document.getElementById('unit_price_per_cm2').value);

            const requestData = {
                sheet_count: sheetCount,
                hit_count: hitCount,
                area_list_cm2: areaListCm2,
                pricing_parameters: {
                    startup_fee: startupFee,
                    hit_price: hitPrice,
                    quantity_ranges: quantityRanges
                },
                plate_cost_parameters: {
                    min_cost: minCost,
                    unit_price_per_cm2: unitPricePerCm2
                }
            };

            try {
                const response = await fetch('/debossing/calculate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();

                if (response.ok) {
                    document.getElementById('processingFee').textContent = result.processing_fee.toFixed(2);
                    document.getElementById('plateCost').textContent = result.plate_cost.toFixed(2);
                    document.getElementById('totalCost').textContent = result.total_cost.toFixed(2);
                    document.getElementById('error').textContent = '-';
                } else {
                    document.getElementById('processingFee').textContent = '-';
                    document.getElementById('plateCost').textContent = '-';
                    document.getElementById('totalCost').textContent = '-';
                    document.getElementById('error').textContent = result.error || '计算出错';
                }

            } catch (error) {
                document.getElementById('processingFee').textContent = '-';
                document.getElementById('plateCost').textContent = '-';
                document.getElementById('totalCost').textContent = '-';
                document.getElementById('error').textContent = '请求失败: ' + error.message;
            }
        });
    </script>
</body>
</html>
