<!DOCTYPE html>
<html>
<head>
    <title>模切费用计算</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        label { display: block; margin-bottom: 5px; }
        input { margin-bottom: 10px; padding: 8px; }
        button { padding: 10px 15px; background-color: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background-color: #45a049; }
        #result { margin-top: 20px; border: 1px solid #ccc; padding: 15px; }
        .detail-item { margin-left: 20px; font-size: 0.9em; color: #555; }
    </style>
</head>
<body>
    <h1>模切费用计算器</h1>

    <form id="calculationForm">
        <label for="quantity">印张数量:</label>
        <input type="number" id="quantity" name="quantity" required><br>

        <label for="blade_length_per_die">每模刀线长度 (米):</label>
        <input type="number" id="blade_length_per_die" name="blade_length_per_die" step="0.01" required><br>

        <label for="num_of_dies_per_sheet">每个印张模切位数:</label>
        <input type="number" id="num_of_dies_per_sheet" name="num_of_dies_per_sheet" required><br>

        <button type="submit">计算</button>
    </form>

    <div id="result">
        <h2>计算结果:</h2>
        <p><strong>总费用:</strong> <span id="total_cost">-</span></p>
        <p><strong>模切加工费:</strong> <span id="processing_cost">-</span></p>
        <p><strong>模切刀材料费:</strong> <span id="blade_cost">-</span></p>
        <p><strong>未考虑最低消费的加工费:</strong> <span id="base_processing_cost">-</span></p>
        <h3>加工费计算明细:</h3>
        <div id="details"></div>
    </div>

    <script>
        document.getElementById('calculationForm').addEventListener('submit', async function(event) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);

            try {
                const response = await fetch('/calculate/', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
                }

                const result = await response.json();

                document.getElementById('total_cost').textContent = `¥${result.total_cost.toFixed(2)}`;
                document.getElementById('processing_cost').textContent = `¥${result.processing_cost.toFixed(2)}`;
                document.getElementById('blade_cost').textContent = `¥${result.blade_cost.toFixed(2)}`;
                document.getElementById('base_processing_cost').textContent = `¥${result.base_processing_cost.toFixed(2)}`;

                const detailsDiv = document.getElementById('details');
                detailsDiv.innerHTML = ''; // Clear previous details

                if (result.details && result.details.length > 0) {
                    result.details.forEach(detail => {
                        const p = document.createElement('p');
                        p.classList.add('detail-item');
                        p.textContent = `数量小于等于 ${detail.upper_bound === Infinity ? '无限' : detail.upper_bound} 的 ${detail.quantity_in_this_tier} 张，单价 ¥${detail.price.toFixed(2)}/张，本区间费用 ¥${detail.tier_cost.toFixed(2)}，剩余 ${detail.remaining_quantity} 张`;
                        detailsDiv.appendChild(p);
                    });
                } else {
                     const p = document.createElement('p');
                     p.classList.add('detail-item');
                     p.textContent = '无详细计算明细（可能数量为零或输入无效）。';
                     detailsDiv.appendChild(p);
                }


            } catch (error) {
                console.error('计算出错:', error);
                document.getElementById('result').innerHTML = `<h2>计算结果:</h2><p style="color: red;">计算出错: ${error.message}</p>`;
            }
        });
    </script>
</body>
</html>
