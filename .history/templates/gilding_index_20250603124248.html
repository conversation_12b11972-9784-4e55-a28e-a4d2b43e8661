<!DOCTYPE html>
<html>
<head>
    <title>烫金费用计算</title>
    <style>
        body { font-family: sans-serif; margin: 20px; }
        .gilding-position { border: 1px solid #ccc; padding: 10px; margin-bottom: 10px; }
        .gilding-position label { display: inline-block; width: 80px; }
        .gilding-position input { width: 100px; margin-right: 10px; }
        #results { margin-top: 20px; font-weight: bold; }
    </style>
</head>
<body>
    <h1>烫金费用计算</h1>

    <label for="print_quantity">印张数量:</label>
    <input type="number" id="print_quantity" value="1000" required><br><br>

    <h2>烫金工艺位</h2>
    <div id="gilding_positions_container">
        <div class="gilding-position">
            <label>宽度 (cm):</label><input type="number" class="width" value="5" step="0.1" required>
            <label>高度 (cm):</label><input type="number" class="height" value="10" step="0.1" required>
            <label>模数:</label><input type="number" class="mold_quantity" value="1" required>
        </div>
    </div>
    <button id="add_position">添加工艺位</button><br><br>

    <button id="calculate_button">计算费用</button>

    <div id="results"></div>

    <script>
        document.getElementById('add_position').addEventListener('click', function() {
            const container = document.getElementById('gilding_positions_container');
            const newPosition = document.createElement('div');
            newPosition.classList.add('gilding-position');
            newPosition.innerHTML = `
                <label>宽度 (cm):</label><input type="number" class="width" value="5" step="0.1" required>
                <label>高度 (cm):</label><input type="number" class="height" value="10" step="0.1" required>
                <label>模数:</label><input type="number" class="mold_quantity" value="1" required>
            `;
            container.appendChild(newPosition);
        });

        document.getElementById('calculate_button').addEventListener('click', async function() {
            const printQuantity = parseInt(document.getElementById('print_quantity').value);
            const positionElements = document.querySelectorAll('.gilding-position');
            const gildingPositions = [];

            positionElements.forEach(posElement => {
                const width = parseFloat(posElement.querySelector('.width').value);
                const height = parseFloat(posElement.querySelector('.height').value);
                const moldQuantity = parseInt(posElement.querySelector('.mold_quantity').value);
                gildingPositions.push({ width, height, mold_quantity: moldQuantity });
            });

            const response = await fetch('/gilding/calculate_gilding/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    print_quantity: printQuantity,
                    gilding_positions: gildingPositions
                }),
            });

            const result = await response.json();
            const resultsDiv = document.getElementById('results');

            if (result.error) {
                resultsDiv.innerText = '计算出错: ' + result.error;
                resultsDiv.style.color = 'red';
            } else {
                resultsDiv.innerText = '总费用: ￥' + result.total_cost;
                resultsDiv.style.color = 'green';
            }
        });
    </script>
</body>
</html>
