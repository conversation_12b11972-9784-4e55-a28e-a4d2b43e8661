<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>算法整合平台</title>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
            color: var(--primary-color);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
        }
        
        h1 {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 10px;
            position: relative;
            display: inline-block;
        }
        
        h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--secondary-color);
            border-radius: 2px;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #7f8c8d;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .algorithm-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }
        
        .algorithm-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        
        .algorithm-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
        }
        
        .card-header {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .card-body {
            padding: 25px 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .algorithm-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: var(--secondary-color);
        }
        
        .algorithm-title {
            font-size: 1.4rem;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .algorithm-description {
            color: #7f8c8d;
            font-size: 0.95rem;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .algorithm-link {
            display: block;
            background: var(--secondary-color);
            color: white;
            text-align: center;
            padding: 12px 0;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            transition: background 0.3s;
            margin-top: auto;
        }
        
        .algorithm-link:hover {
            background: #2980b9;
            text-decoration: none;
        }
        
        footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px;
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .algorithm-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }
            
            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>包装印刷算法平台</h1>
            <p class="subtitle">专业算法工具助力包装印刷设计与成本优化</p>
        </header>
        
        <div class="algorithm-grid">
            <div class="algorithm-card">
                <div class="card-header">
                    <div class="algorithm-icon">✨</div>
                    <h3 class="algorithm-title">局部UV计算</h3>
                </div>
                <div class="card-body">
                    <p class="algorithm-description">精确计算局部UV工艺的成本与面积，优化特殊效果应用方案</p>
                    <a href="/uv" class="algorithm-link">开始计算</a>
                </div>
            </div>
            
            <div class="algorithm-card">
                <div class="card-header">
                    <div class="algorithm-icon">✂️</div>
                    <h3 class="algorithm-title">模切计算</h3>
                </div>
                <div class="card-body">
                    <p class="algorithm-description">模切工艺成本与效率计算，优化刀模使用和材料利用率</p>
                    <a href="/die_cutting" class="algorithm-link">开始计算</a>
                </div>
            </div>
            
            <div class="algorithm-card">
                <div class="card-header">
                    <div class="algorithm-icon">🌟</div>
                    <h3 class="algorithm-title">烫金工艺</h3>
                </div>
                <div class="card-body">
                    <p class="algorithm-description">烫金面积、材料与成本计算，提升金属质感效果性价比</p>
                    <a href="/gilding" class="algorithm-link">开始计算</a>
                </div>
            </div>
            
            <div class="algorithm-card">
                <div class="card-header">
                    <div class="algorithm-icon">🪟</div>
                    <h3 class="algorithm-title">贴窗计算</h3>
                </div>
                <div class="card-body">
                    <p class="algorithm-description">开窗工艺的材料与加工成本计算，优化透明窗口设计</p>
                    <a href="/window_patching" class="algorithm-link">开始计算</a>
                </div>
            </div>
            
            <div class="algorithm-card">
                <div class="card-header">
                    <div class="algorithm-icon">⬆️</div>
                    <h3 class="algorithm-title">击凸计算</h3>
                </div>
                <div class="card-body">
                    <p class="algorithm-description">凸版工艺成本计算，实现立体效果的最优实施方案</p>
                    <a href="/embossing" class="algorithm-link">开始计算</a>
                </div>
            </div>
            
            <div class="algorithm-card">
                <div class="card-header">
                    <div class="algorithm-icon">⬇️</div>
                    <h3 class="algorithm-title">压凹计算</h3>
                </div>
                <div class="card-body">
                    <p class="algorithm-description">凹版工艺成本优化，精确计算凹陷效果的制作成本</p>
                    <a href="/debossing" class="algorithm-link">开始计算</a>
                </div>
            </div>
            
            <div class="algorithm-card">
                <div class="card-header">
                    <div class="algorithm-icon">📐</div>
                    <h3 class="algorithm-title">模数计算</h3>
                </div>
                <div class="card-body">
                    <p class="algorithm-description">包装模数优化算法，最大化材料利用率和生产效率</p>
                    <a href="/modulus" class="algorithm-link">开始计算</a>
                </div>
            </div>
            
            <div class="algorithm-card">
                <div class="card-header">
                    <div class="algorithm-icon">🖨️</div>
                    <h3 class="algorithm-title">印刷计价</h3>
                </div>
                <div class="card-body">
                    <p class="algorithm-description">多色印刷成本计算，支持四色/专色/白墨等复杂工艺</p>
                    <a href="/printing" class="algorithm-link">开始计算</a>
                </div>
            </div>
            
            <div class="algorithm-card">
                <div class="card-header">
                    <div class="algorithm-icon">📦</div>
                    <h3 class="algorithm-title">用纸计算</h3>
                </div>
                <div class="card-body">
                    <p class="algorithm-description">纸张用量与成本精确计算，支持多种纸张规格和克重</p>
                    <a href="/paper_cost" class="algorithm-link">开始计算</a>
                </div>
            </div>
        </div>
        
        <footer>
            <p>包装印刷算法平台 &copy; 2023 | 专业 · 精确 · 高效</p>
        </footer>
    </div>
</body>
</html>
