from fastapi import FastAPI
from pydantic import BaseModel
from typing import Tuple
import math

app = FastAPI()

def calc_layout(eqp_len: float, eqp_wid: float, prod_len: float, prod_wid: float, bleed: float, margin: float) -> Tuple[int, str, float, float]:
    """
    计算排版，输出最大模数、方式、详细排版信息和所需纸张尺寸，考虑出血和余量。

    Args:
        eqp_len: 设备长.
        eqp_wid: 设备宽.
        prod_len: 成品长.
        prod_wid: 成品宽.
        bleed: 出血宽度 (mm).
        margin: 余量 (mm).

    Returns:
        一个包含最大模数值、排版方式、详细排版信息和所需纸张尺寸的元组.
    """

    # 考虑出血，更新成品尺寸
    prod_len += 2 * bleed
    prod_wid += 2 * bleed

    # 确保成品的长 >= 宽
    if prod_len < prod_wid:
        prod_len, prod_wid = prod_wid, prod_len

    num_hor = math.floor(eqp_len / prod_len) * math.floor(eqp_wid / prod_wid)
    num_ver = math.floor(eqp_len / prod_wid) * math.floor(eqp_wid / prod_len)

    if num_hor > num_ver:
        best = "横向"
        max_num = num_hor
        paper_len = prod_len * math.floor(eqp_len / prod_len)
        paper_wid = prod_wid * math.floor(eqp_wid / prod_wid)
    elif num_hor < num_ver:
        best = "纵向"
        max_num = num_ver
        paper_len = prod_wid * math.floor(eqp_len / prod_wid)
        paper_wid = prod_len * math.floor(eqp_wid / prod_len)
    else:
        best = "横向或纵向"
        max_num = num_hor
        paper_len = prod_len * math.floor(eqp_len / prod_len)
        paper_wid = prod_wid * math.floor(eqp_wid / prod_wid)

    # 确保纸张尺寸的长 >= 宽
    if paper_len < paper_wid:
        paper_len, paper_wid = paper_wid, paper_len

    # 添加余量
    paper_len += 2 * margin
    paper_wid += 2 * margin

    result_str = f"最大模数: {max_num}, 方式: {best}\\n"
    result_str += f"横向排版: {num_hor} 个\\n"
    result_str += f"纵向排版: {num_ver} 个\\n"
    result_str += f"所需纸张尺寸(含{margin}mm余量): {paper_len} x {paper_wid}"

    return max_num, result_str, paper_len, paper_wid

class InputParams(BaseModel):
    eqp_len: float
    eqp_wid: float
    prod_len: float
    prod_wid: float
    bleed: float
    margin: float

class OutputParams(BaseModel):
    max_num: int
    result_str: str
    paper_len: float
    paper_wid: float

@app.post("/calc_layout", response_model=OutputParams)
async def calculate_layout(params: InputParams):
    max_num, result_str, paper_len, paper_wid = calc_layout(
        params.eqp_len, params.eqp_wid, params.prod_len, params.prod_wid, params.bleed, params.margin
    )
    return OutputParams(max_num=max_num, result_str=result_str, paper_len=paper_len, paper_wid=paper_wid)
