from fastapi import <PERSON>AP<PERSON>, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel

app = FastAPI()

app.mount("/static", StaticFiles(directory="模数计算/fastapi/static"), name="static")

templates = Jinja2Templates(directory="模数计算/fastapi/templates")

class CalculationInput(BaseModel):
    value: float

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/calculate")
async def calculate_value(input_data: CalculationInput):
    max_num, result_str, paper_len, paper_wid = calc_layout(
        input_data.eqp_len,
        input_data.eqp_wid,
        input_data.prod_len,
        input_data.prod_wid,
        input_data.bleed,
        input_data.margin
    )
    return {
        "max_num": max_num,
        "result_str": result_str,
        "paper_len": paper_len,
        "paper_wid": paper_wid
    }
