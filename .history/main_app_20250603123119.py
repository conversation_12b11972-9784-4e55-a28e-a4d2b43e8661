from fastapi import FastAPI, Request, Form, APIRouter
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
from typing import List, Dict, Union, Tuple, Annotated
import math
from collections import OrderedDict

app = FastAPI()

# 配置模板目录
templates = Jinja2Templates(directory="templates")

# 主页路由
@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    return templates.TemplateResponse("main_index.html", {"request": request})

# 局部UV计算 (算法/后加工工艺/局部UV/fastapi/main.py)
uv_router = APIRouter(prefix="/uv")

UV_CONFIG = {
    "startup_fee": 100.00,
    "minimum_cost": 260.00,
    "material_price_per_sqm": 4.50,
    "price_tiers": {
        (1, 1000): 0.20,
        (1001, 10000): 0.18,
        (10001, float('inf')): 0.16
    }
}

def calculate_uv_cost(print_quantity, uv_area=0, config=UV_CONFIG):
    try:
        print_quantity = int(print_quantity)
        uv_area = float(uv_area)

        if print_quantity <= 0 or uv_area < 0:
            return {"error": "输入参数必须为正数（印张数量）或非负数（UV面积）"}

        startup_fee = config["startup_fee"]
        minimum_cost = config["minimum_cost"]
        material_price = config["material_price_per_sqm"]
        price_tiers = config["price_tiers"]

        processing_cost = 0
        remaining_quantity = print_quantity

        pre_min_cost = startup_fee
        processing_details = []
        for (lower, upper), price in price_tiers.items():
            quantity_in_tier = min(remaining_quantity, upper - lower + 1)
            if quantity_in_tier > 0:
                tier_cost = quantity_in_tier * price
                processing_details.append(f"{lower}-{upper}张: {quantity_in_tier}*{price:.2f}={tier_cost:.2f}")
                pre_min_cost += tier_cost
                remaining_quantity -= quantity_in_tier

        processing_cost_before_min = pre_min_cost
        processing_cost = max(processing_cost_before_min, minimum_cost)
        processing_details.append(f"开机费+{processing_cost_before_min - startup_fee:.2f}={processing_cost_before_min:.2f}")
        processing_details.append(f"取大值({processing_cost_before_min:.2f},{minimum_cost:.2f})={processing_cost:.2f}")


        uv_material_cost = uv_area * print_quantity * material_price if uv_area > 0 else 0
        uv_material_details = []
        if uv_material_cost > 0:
             uv_material_details.append(f"{uv_area:.2f}*{print_quantity}*{material_price:.2f}={uv_material_cost:.2f}")


        total_cost = processing_cost + uv_material_cost

        return {
            "加工费": processing_cost,
            "UV用料费": uv_material_cost,
            "总费用": total_cost,
            "加工费明细": processing_details,
            "UV用料费明细": uv_material_details
        }
    except ValueError:
        return {"error": "输入参数类型错误，请检查输入是否为数字"}
    except Exception as e:
        return {"error": f"计算过程中发生错误: {e}"}

class UVInput(BaseModel):
    print_quantity: int
    uv_area: float = 0.0

@uv_router.get("/", response_class=HTMLResponse)
async def uv_root(request: Request):
    return templates.TemplateResponse("uv_index.html", {"request": request})

@uv_router.post("/calculate_uv/")
async def calculate_uv_api(input: UVInput):
    result = calculate_uv_cost(input.print_quantity, input.uv_area)
    return result

app.include_router(uv_router)

# 模切计算 (算法/后加工工艺/模切/fastapi/main.py)
die_cutting_router = APIRouter(prefix="/die_cutting")

# 配置信息（全局常量）
DC_SETUP_FEE = 90.00  # 开机费
DC_MINIMUM_CHARGE = 150.00  # 最低消费
DC_BLADE_PRICE_PER_METER = 12.00  # 刀片每米单价

# 不同数量区间的单价（使用有序字典，保持区间顺序）
DC_UNIT_PRICES = OrderedDict([
    (1000, 0.06),      # 一级单价
    (10000, 0.06),   # 二级单价
    (float('inf'), 0.05)  # 三级单价
])

def calculate_die_cutting_processing_cost(quantity):
    """使用分段叠加方式计算模切加工费。"""
    if not isinstance(quantity, (int, float)) or quantity < 0:
        return "印张数量必须为非负数。"

    processing_cost = DC_SETUP_FEE
    remaining_quantity = quantity
    details = []  # 存储每个区间的计算明细

    for upper_bound, price in DC_UNIT_PRICES.items():
        if remaining_quantity > 0:
            quantity_in_this_tier = min(remaining_quantity, upper_bound)
            tier_cost = quantity_in_this_tier * price
            processing_cost += tier_cost
            remaining_quantity -= quantity_in_this_tier
            details.append({"upper_bound": upper_bound, "price": price, "quantity_in_this_tier": quantity_in_this_tier, "tier_cost": tier_cost, "remaining_quantity": remaining_quantity}) #添加计算详情
    
    base_processing_cost = processing_cost - DC_SETUP_FEE
    processing_cost = max(processing_cost, DC_MINIMUM_CHARGE)
    return processing_cost, details, base_processing_cost


def calculate_blade_material_cost(blade_length_per_die, num_of_dies_per_sheet):
    """计算模切刀材料费。"""
    if not all(isinstance(x, (int, float)) and x >= 0 for x in [blade_length_per_die, num_of_dies_per_sheet]):
        return "刀线长度和模数必须为非负数。"

    blade_cost = blade_length_per_die * num_of_dies_per_sheet * DC_BLADE_PRICE_PER_METER
    return blade_cost


def calculate_die_cutting_total_cost(quantity, blade_length_per_die, num_of_dies_per_sheet):
    """计算模切总费用。"""
    processing_cost_result = calculate_die_cutting_processing_cost(quantity)
    if isinstance(processing_cost_result, str):
        return processing_cost_result
    processing_cost, details, base_processing_cost = processing_cost_result

    blade_cost = calculate_blade_material_cost(blade_length_per_die, num_of_dies_per_sheet)
    if isinstance(blade_cost, str):
        return blade_cost

    total_cost = processing_cost + blade_cost
    return {
        "total_cost": round(total_cost, 2),
        "processing_cost": round(processing_cost, 2),
        "blade_cost": round(blade_cost, 2),
        "details": details,
        "base_processing_cost": round(base_processing_cost, 2)
    }

@die_cutting_router.get("/", response_class=HTMLResponse)
async def die_cutting_root(request: Request):
    return templates.TemplateResponse("die_cutting_index.html", {"request": request})

@die_cutting_router.post("/calculate")
async def calculate_die_cutting_api(
    quantity: float = Form(...),
    blade_length_per_die: float = Form(...),
    num_of_dies_per_sheet: float = Form(...)
):
    result = calculate_die_cutting_total_cost(quantity, blade_length_per_die, num_of_dies_per_sheet)
    return result

app.include_router(die_cutting_router)
