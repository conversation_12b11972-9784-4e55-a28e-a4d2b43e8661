# -*- coding: utf-8 -*-

def calculate_ste_complexity_adjustment(
    has_window=False, 
    window_perimeter=0.0, 
    has_hanger=False, 
    hanger_length=120.0,
    has_tear_strip=False,
    tear_strip_length=0.0
    ):
    """
    【辅助函数】
    专门计算双插盒 (STE) 的“复杂度补偿值”。
    这是一个可扩展的规则引擎，用于量化由附加结构特征带来的额外刀线长度。

    Args:
        has_window (bool): 是否有开窗。
        window_perimeter (float): 窗口的周长 (mm)。
        has_hanger (bool): 是否有挂口/飞机孔。
        hanger_length (float): 标准挂口的额外刀线长度 (mm)，这是一个经验值。
        has_tear_strip (bool): 是否有撕拉线。
        tear_strip_length (float): 撕拉线的总长度 (mm)。

    Returns:
        float: 附加特征的总刀线长度 (mm)。
    """
    adjustment = 0.0
    
    if has_window:
        adjustment += window_perimeter
        
    if has_hanger:
        adjustment += hanger_length
        
    if has_tear_strip:
        adjustment += tear_strip_length
        
    # 未来可在此处添加更多复杂结构的规则
    
    return adjustment

def calculate_die_line_ste_expert(
    L, W, H, CS, 
    base_constant=30.0,
    has_window=False, 
    window_perimeter=0.0, 
    has_hanger=False,
    # ... 您可以在这里继续添加其他复杂度的参数
    ):
    """
    【主函数】
    计算卡纸双插盒 (STE) 的刀线长度（专家版）。
    
    此版本集成了动态计算的“复杂度补偿值”，极大提高了估算的准确性和灵活性。

    Args:
        L (float): 包装盒长度 (mm)。
        W (float): 包装盒宽度 (mm)。
        H (float): 包装盒高度 (mm)。
        CS (float): 插入舌的长度 (mm)。
        base_constant (float): 基础修正项，用于补偿最简单盒型中的固有细节。
        has_window (bool): 是否有开窗。
        window_perimeter (float): 窗口的周长 (mm)。
        has_hanger (bool): 是否有挂口/飞机孔。

    Returns:
        float: 估算的刀线总长度 (mm)。
    """
    # 1. 计算基础刀线长度
    base_length = (10 * L + 12 * W) + (4 * H + 4 * CS) + base_constant
    
    # 2. 调用辅助函数，计算由附加特征带来的“复杂度补偿值”
    complexity_adj = calculate_ste_complexity_adjustment(
        has_window=has_window,
        window_perimeter=window_perimeter,
        has_hanger=has_hanger
    )
    
    # 3. 将基础长度与复杂度补偿值相加，得到最终总长
    total_die_line_length = base_length + complexity_adj
    
    return total_die_line_length

# --- 代码使用示例 ---

if __name__ == "__main__":
    # 设定一个包装盒的基础尺寸
    box_L = 100
    box_W = 50
    box_H = 100
    box_CS = 15

    print("========= 卡纸双插盒 (STE) 专家级刀线长度计算 =========")
    print(f"输入基础尺寸: L={box_L}, W={box_W}, H={box_H}, CS={box_CS}\n")

    # 案例A: 计算一个简单的、无任何附加特征的盒子
    simple_box_length = calculate_die_line_ste_expert(box_L, box_W, box_H, box_CS)
    print(f"【案例 A: 简单盒子】")
    print(f"  - 基础长度部分: {(10*box_L + 12*box_W) + (4*box_H + 4*box_CS) + 30:.2f} mm")
    print(f"  - 复杂度补偿值: 0.00 mm")
    print(f"  估算的刀线总长度为: {simple_box_length:.2f} mm\n")

    # 案例B: 计算一个复杂的盒子，带有一个60x40mm的窗口和标准飞机孔
    window_p = (60 + 40) * 2  # 窗口周长
    complex_box_length = calculate_die_line_ste_expert(
        box_L, box_W, box_H, box_CS,
        has_window=True,
        window_perimeter=window_p,
        has_hanger=True
    )
    base_part_complex = (10*box_L + 12*box_W) + (4*box_H + 4*box_CS) + 30
    complexity_part_complex = calculate_ste_complexity_adjustment(has_window=True, window_perimeter=window_p, has_hanger=True)
    
    print(f"【案例 B: 复杂盒子 (带开窗和挂口)】")
    print(f"  - 基础长度部分: {base_part_complex:.2f} mm")
    print(f"  - 复杂度补偿值 (窗口周长 + 挂口长度): {complexity_part_complex:.2f} mm")
    print(f"  估算的刀线总长度为: {complex_box_length:.2f} mm")