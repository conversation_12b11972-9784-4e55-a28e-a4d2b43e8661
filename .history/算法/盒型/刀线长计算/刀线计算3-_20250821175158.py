import math

class DielineLengthCalculator:
    """
    一个专门用于计算包装结构刀模总线长的工具。
    以同向插入式卡纸盒 (STE) 为例。
    """
    def __init__(self, L, W, H, T, params=None):
        self.L = L
        self.W = W
        self.H = H
        self.T = T
        
        default_params = {
            "glue_flap_width": 15,
            "glue_flap_taper_angle": 10,
            "tuck_flap_height_ratio": 0.25,
            "tuck_flap_tolerance": 1.0,
            "tuck_flap_corner_radius": 5.0,
            "dust_flap_ratio": 0.9,
            "dust_flap_taper_angle": 15,
        }
        if params:
            default_params.update(params)
        self.params = default_params

        self.lines = []
        self._is_generated = False

    def _generate_geometry(self):
        """
        在内存中生成所有线段的几何坐标。
        这个方法是内部使用的，用户不需要直接调用。
        """
        if self._is_generated:
            return

        # 1. 计算关键尺寸和坐标
        p = self.params
        L_comp = self.L + self.T / 2
        W_comp = self.W + self.T / 2
        tf_h = self.H * p['tuck_flap_height_ratio']
        df_h = self.W * p['dust_flap_ratio']

        x = [0] * 5
        x[0] = p['glue_flap_width']
        x[1] = x[0] + L_comp
        x[2] = x[1] + W_comp
        x[3] = x[2] + L_comp
        x[4] = x[3] + W_comp

        y_bottom = tf_h + self.W
        y_top = y_bottom + self.H

        # 2. 生成所有线段 (此处省略了详细的几何生成代码，因为它与上一个回答完全相同)
        # 为了简洁，我们直接调用一个虚拟的、代表所有几何生成步骤的方法
        self._create_all_lines(x, y_bottom, y_top, L_comp, W_comp, tf_h, df_h)
        
        self._is_generated = True

    def calculate_lengths(self):
        """
        计算并返回切割线、压痕线和总线长。
        这是用户需要调用的主要方法。
        """
        # 确保几何数据已生成
        self._generate_geometry()

        total_cut_length = 0.0
        total_crease_length = 0.0

        for line in self.lines:
            p1 = line['start']
            p2 = line['end']
            # 使用距离公式计算线段长度
            length = math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
            
            if line['type'] == 'CUT':
                total_cut_length += length
            elif line['type'] == 'CREASE':
                total_crease_length += length
        
        total_length = total_cut_length + total_crease_length

        return {
            "cut_length": total_cut_length,
            "crease_length": total_crease_length,
            "total_length": total_length,
        }

    # --- 内部辅助方法 ---
    def _add_line(self, p1, p2, line_type):
        self.lines.append({'start': p1, 'end': p2, 'type': line_type.upper()})

    def _add_arc_as_lines(self, center, radius, start_angle, angle_span, line_type, segments=8):
        """用多段线近似圆弧，并添加这些线段"""
        theta = math.radians(start_angle)
        delta_theta = math.radians(angle_span) / segments
        
        p_start = (
            center[0] + radius * math.cos(theta),
            center[1] + radius * math.sin(theta)
        )
        for i in range(1, segments + 1):
            theta += delta_theta
            p_end = (
                center[0] + radius * math.cos(theta),
                center[1] + radius * math.sin(theta)
            )
            self._add_line(p_start, p_end, line_type)
            p_start = p_end

    def _create_all_lines(self, x, y_bottom, y_top, L_comp, W_comp, tf_h, df_h):
        """
        这是一个集中的方法，包含了所有几何图形的生成逻辑。
        代码与上一版回答中的生成部分完全一致。
        """
        p = self.params
        
        # 主体压线
        for i in range(4): self._add_line((x[i], y_bottom), (x[i], y_top), 'CREASE')
        self._add_line((x[0], y_bottom), (x[4], y_bottom), 'CREASE')
        self._add_line((x[0], y_top), (x[4], y_top), 'CREASE')

        # 胶水边
        taper_h = self.H * math.tan(math.radians(p['glue_flap_taper_angle']))
        self._add_line((0, y_bottom + taper_h), (x[0], y_bottom), 'CUT')
        self._add_line((x[0], y_top), (0, y_top - taper_h), 'CUT')
        self._add_line((0, y_bottom + taper_h), (0, y_top - taper_h), 'CUT')

        # 顶部结构
        # 后面板主盖
        self._add_line((x[0], y_top), (x[0], y_top + self.W), 'CUT')
        self._add_line((x[1], y_top), (x[1], y_top + self.W), 'CUT')
        self._add_line((x[0], y_top + self.W), (x[1], y_top + self.W), 'CUT')
        # 侧面板防尘翼
        df_taper = df_h * math.tan(math.radians(p['dust_flap_taper_angle']))
        self._add_line((x[1], y_top), (x[1] + df_taper, y_top + df_h), 'CUT')
        self._add_line((x[2], y_top), (x[2] - df_taper, y_top + df_h), 'CUT')
        self._add_line((x[1] + df_taper, y_top + df_h), (x[2] - df_taper, y_top + df_h), 'CUT')
        # 前面板插入舌
        self._create_tuck_flap(x[2], y_top, L_comp, 1)
        # 另一侧防尘翼
        self._add_line((x[3], y_top), (x[3] + df_taper, y_top + df_h), 'CUT')
        self._add_line((x[4], y_top), (x[4] - df_taper, y_top + df_h), 'CUT')
        self._add_line((x[3] + df_taper, y_top + df_h), (x[4] - df_taper, y_top + df_h), 'CUT')
        self._add_line((x[4], y_top), (x[4], y_bottom), 'CUT')

        # 底部结构
        # 后面板插入舌
        self._create_tuck_flap(x[0], y_bottom, L_comp, -1)
        # 侧面板防尘翼
        self._add_line((x[1], y_bottom), (x[1] + df_taper, y_bottom - df_h), 'CUT')
        self._add_line((x[2], y_bottom), (x[2] - df_taper, y_bottom - df_h), 'CUT')
        self._add_line((x[1] + df_taper, y_bottom - df_h), (x[2] - df_taper, y_bottom - df_h), 'CUT')
        # 前面板主盖
        self._add_line((x[2], y_bottom), (x[2], y_bottom - self.W), 'CUT')
        self._add_line((x[3], y_bottom), (x[3], y_bottom - self.W), 'CUT')
        self._add_line((x[2], y_bottom - self.W), (x[3], y_bottom - self.W), 'CUT')
        # 另一侧防尘翼
        self._add_line((x[3], y_bottom), (x[3] + df_taper, y_bottom - df_h), 'CUT')
        self._add_line((x[4], y_bottom), (x[4] - df_taper, y_bottom - df_h), 'CUT')
        self._add_line((x[3] + df_taper, y_bottom - df_h), (x[4] - df_taper, y_bottom - df_h), 'CUT')

    def _create_tuck_flap(self, panel_x_start, panel_y_start, L_comp, direction=1):
        p = self.params
        tol = p['tuck_flap_tolerance']
        r = p['tuck_flap_corner_radius']
        tf_h = self.H * p['tuck_flap_height_ratio']

        mf_y_end = panel_y_start + direction * self.W
        self._add_line((panel_x_start, panel_y_start), (panel_x_start, mf_y_end), 'CUT')
        self._add_line((panel_x_start + L_comp, panel_y_start), (panel_x_start + L_comp, mf_y_end), 'CUT')
        self._add_line((panel_x_start, mf_y_end), (panel_x_start + L_comp, mf_y_end), 'CREASE')
        
        tf_y_end = mf_y_end + direction * tf_h
        self._add_line((panel_x_start + tol, mf_y_end + direction * r), (panel_x_start + tol, tf_y_end - direction * r), 'CUT')
        self._add_line((panel_x_start + L_comp - tol, mf_y_end + direction * r), (panel_x_start + L_comp - tol, tf_y_end - direction * r), 'CUT')
        self._add_line((panel_x_start + tol + r, tf_y_end), (panel_x_start + L_comp - tol - r, tf_y_end), 'CUT')
        
        # 圆角
        self._add_arc_as_lines((panel_x_start + tol + r, mf_y_end + direction * r), r, 180 if direction==1 else 270, 90, 'CUT')
        self._add_arc_as_lines((panel_x_start + L_comp - tol - r, mf_y_end + direction * r), r, 90 if direction==1 else 0, 90, 'CUT')
        self._add_arc_as_lines((panel_x_start + tol + r, tf_y_end - direction * r), r, 180, -90 if direction==1 else 90, 'CUT')
        self._add_arc_as_lines((panel_x_start + L_comp - tol - r, tf_y_end - direction * r), r, 0, 90 if direction==1 else -90, 'CUT')

# --- 主程序入口 ---
if __name__ == '__main__':
    # 1. 定义盒子尺寸 (单位: mm)
    box_L = 80
    box_W = 50
    box_H = 120
    paper_T = 0.5

    # 2. (可选) 自定义结构参数
    # custom_params = {
    #     "tuck_flap_corner_radius": 8.0,
    #     "glue_flap_width": 18
    # }

    # 3. 创建计算器实例
    calculator = DielineLengthCalculator(L=box_L, W=box_W, H=box_H, T=paper_T) # params=custom_params

    # 4. 执行计算
    lengths = calculator.calculate_lengths()

    # 5. 打印结果
    print("--- 包装结构刀模线长计算结果 ---")
    print(f"输入尺寸 (L x W x H): {box_L} x {box_W} x {box_H} mm")
    print("-" * 35)
    print(f"切割线 (刀线) 总长度: {lengths['cut_length']:.2f} mm")
    print(f"压痕线 (压线) 总长度: {lengths['crease_length']:.2f} mm")
    print("=" * 35)
    print(f"刀模总线长: {lengths['total_length']:.2f} mm")
    print("=" * 35)