# -*- coding: utf-8 -*-

def calculate_die_line_ste(L, W, H, CS, constant=30):
    """
    计算卡纸双插盒 (同向插入盒 - STE) 的刀线长度。

    该函数使用一个为STE盒型优化的参数化估算模型。
    此模型非常适用于项目前期的成本估算和报价。

    Args:
        L (float): 包装盒长度 (mm)
        W (float): 包装盒宽度 (mm)
        H (float): 包装盒高度 (mm)
        CS (float): 插入舌的长度 (mm)。在实际应用中，该值通常由L或W根据另一套规则计算得出。
        constant (float, optional): 用于补偿圆角、锁口等杂项的常数修正项。
                                 根据经验，对于标准STE盒型，30是一个合理的起始值。默认为 30。

    Returns:
        float: 估算的刀线总长度 (mm)
    """
    # 估算所有水平方向的刀线长度
    horizontal_lines_approx = 10 * L + 12 * W
    
    # 估算所有垂直方向的刀线长度
    vertical_lines_approx = 4 * H + 4 * CS
    
    # 将各部分相加得到总长
    total_die_line_length = horizontal_lines_approx + vertical_lines_approx + constant
    
    return total_die_line_length

# --- 代码使用示例 ---

if __name__ == "__main__":
    # 设定一个典型的化妆品包装盒尺寸
    box_L = 100   # 长度 (mm)
    box_W = 40   # 宽度 (mm)
    box_H = 120  # 高度 (mm)

    # 根据我们的历史讨论，CS的值通常依赖于W。
    # 假设对于W=40的盒子，计算规则给出的CS值为15。
    box_CS = 15  # 插入舌长度 (mm)

    print("========= 卡纸双插盒 (STE) 刀线长度计算 =========")
    print(f"输入尺寸: L={box_L}mm, W={box_W}mm, H={box_H}mm, CS={box_CS}mm\n")

    # 调用函数进行计算
    ste_length_mm = calculate_die_line_ste(box_L, box_W, box_H, box_CS)
    
    # 将结果转换为米，因为刀模费用通常按米计算
    ste_length_m = ste_length_mm / 1000

    print(f"估算的刀线总长度为: {ste_length_mm:.2f} mm")
    print(f"换算为米: {ste_length_m:.3f} m")