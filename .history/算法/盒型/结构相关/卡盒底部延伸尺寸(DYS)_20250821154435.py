import math

def calculate_dys(w: float) -> int:
    """
    根据包装盒宽度(W)计算卡盒底部延伸尺寸(DYS)。

    Args:
        w: 包装盒宽度 (mm)

    Returns:
        DYS尺寸(mm)。
    """
    if w <= 25:
        # 特殊基准值
        return 6
    elif w <= 100:
        # 第一线性区间，斜率 1/5
        return math.ceil(w / 5) + 1
    elif w <= 160:
        # 第二线性区间，斜率 1/20，常数项A
        return math.ceil(w / 20) + 16
    elif w <= 300:
        # 第三线性区间，斜率 1/20，常数项B
        return math.ceil(w / 20) + 17
    else:  # w > 300
        # 尺寸上限
        return 35

# --- 测试用例 ---
print(f"W=24, DYS={calculate_dys(25)}")     # 预期: 6
print(f"W=25.1, DYS={calculate_dys(25.1)}") # 预期: ceil(5.02)+1 = 7
print(f"W=60, DYS={calculate_dys(60)}")     # 预期: ceil(12)+1 = 13
print(f"W=70, DYS={calculate_dys(70)}")     # 预期: ceil(14)+1 = 15
print(f"W=100, DYS={calculate_dys(100)}")   # 预期: ceil(20)+1 = 21
print(f"W=120, DYS={calculate_dys(120)}")   # 预期: ceil(6)+16 = 22
print(f"W=160, DYS={calculate_dys(160)}")   # 预期: ceil(8)+16 = 24
print(f"W=180, DYS={calculate_dys(180)}")   # 预期: ceil(9)+17 = 26
print(f"W=300, DYS={calculate_dys(300)}")   # 预期: ceil(15)+17 = 32
print(f"W=400, DYS={calculate_dys(400)}")   # 预期: 35