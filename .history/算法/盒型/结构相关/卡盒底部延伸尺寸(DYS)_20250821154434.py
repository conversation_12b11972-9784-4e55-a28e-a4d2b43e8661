import math

def calculate_dys_optimized(w: float) -> int:
    """
    根据包装盒宽度(W)计算卡盒底部延伸尺寸(DYS)的优化算法。

    Args:
        w: 包装盒宽度 (mm)

    Returns:
        DYS尺寸(mm)。
    """
    if w <= 100:
        # 第一线性区间，斜率 1/5
        return math.ceil(w / 5) + 1
    elif w <= 160:
        # 第二线性区间，斜率 1/20，常数项A
        return math.ceil(w / 20) + 16
    elif w <= 300:
        # 第三线性区间，斜率 1/20，常数项B
        return math.ceil(w / 20) + 17
    else:  # w > 300
        # 尺寸上限
        return 35

# --- 测试用例 (覆盖所有边界和关键点) ---
print(f"W=25, DYS={calculate_dys_optimized(25)}")     # 预期: 6
print(f"W=70, DYS={calculate_dys_optimized(70)}")     # 预期: 15
print(f"W=100, DYS={calculate_dys_optimized(100)}")   # 预期: 21
print(f"W=100.1, DYS={calculate_dys_optimized(100.1)}") # 预期: ceil(5.005)+16 = 22
print(f"W=160, DYS={calculate_dys_optimized(160)}")   # 预期: ceil(8)+16 = 24
print(f"W=160.1, DYS={calculate_dys_optimized(160.1)}") # 预期: ceil(8.005)+17 = 26
print(f"W=300, DYS={calculate_dys_optimized(300)}")   # 预期: ceil(15)+17 = 32
print(f"W=400, DYS={calculate_dys_optimized(400)}")   # 预期: 35