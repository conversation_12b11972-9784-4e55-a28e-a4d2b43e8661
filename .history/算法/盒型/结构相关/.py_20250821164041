def calculate_dys2(l: float, w: float) -> str | float:
    """
    根据包装盒长度(L)和宽度(W)计算手动锁底部延伸尺寸(DYS2)。
    结果将被钳制在 [5, 30] 区间内。

    Args:
        l: 包装盒长度 (mm)
        w: 包装盒宽度 (mm)

    Returns:
        DYS2尺寸(mm)或表示未定义的字符串。
    """
    # 步骤 1: 输入有效性检查
    if w <= 0:
        return "[未定义]"

    # 步骤 2: 根据长宽比计算基础值
    aspect_ratio = l / w
    if aspect_ratio >= 3:
        raw_dys2 = l / 10
    else:
        raw_dys2 = 0.15 * l  # 3/20 = 0.15

    # 步骤 3: 将基础值钳制在 [5, 30] 区间内 (使用简洁的max/min函数)
    dys2 = max(5, min(30, raw_dys2))
    
    return dys2

# --- 测试用例 ---
# 边界情况
print(f"L=100, W=0 -> DYS2={calculate_dys2(100, 0)}")     # 预期: [未定义]

# L/W >= 3 的情况
print(f"L=40, W=10 -> raw=4, DYS2={calculate_dys2(40, 10)}")     # 预期: 5 (低于下限，被钳制)
print(f"L=200, W=50 -> raw=20, DYS2={calculate_dys2(200, 50)}")   # 预期: 20 (在范围内)
print(f"L=350, W=100 -> raw=35, DYS2={calculate_dys2(350, 100)}") # 预期: 30 (高于上限，被钳制)

# L/W < 3 的情况
print(f"L=30, W=15 -> raw=4.5, DYS2={calculate_dys2(30, 15)}")     # 预期: 5 (低于下限，被钳制)
print(f"L=100, W=50 -> raw=15, DYS2={calculate_dys2(100, 50)}")   # 预期: 15 (在范围内)
print(f"L=210, W=80 -> raw=31.5, DYS2={calculate_dys2(210, 80)}") # 预期: 30 (高于上限，被钳制)