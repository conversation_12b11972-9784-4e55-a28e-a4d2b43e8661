def calculate_cs1(l: float) -> int:
    """
    根据包装盒长度(L)计算坑盒插舌尺寸(CS1)。

    Args:
        l: 包装盒长度 (mm)

    Returns:
        CS1尺寸(mm)。
    """
    if l < 60:
        # 新增规则：为 L < 60 的情况提供一个合理的默认值
        return 15
    elif l < 150:   # 覆盖 [60, 150)
        return 18
    elif l <= 250:  # 覆盖 [150, 250]
        return 20
    elif l <= 350:  # 覆盖 (250, 350]
        return 22
    else:           # 覆盖 (350, ...)
        return 25

# --- 测试用例 ---
# 新增的边界情况
print(f"L=59, CS1={calculate_cs1(59)}")     # 预期: 15
# 原始边界和中间值
print(f"L=60, CS1={calculate_cs1(60)}")     # 预期: 18
print(f"L=149.9, CS1={calculate_cs1(149.9)}") # 预期: 18
print(f"L=150, CS1={calculate_cs1(150)}")   # 预期: 20
print(f"L=250, CS1={calculate_cs1(250)}")   # 预期: 20
print(f"L=250.1, CS1={calculate_cs1(250.1)}") # 预期: 22
print(f"L=350, CS1={calculate_cs1(350)}")   # 预期: 22
print(f"L=351, CS1={calculate_cs1(351)}")   # 预期: 25
print(f"L=500, CS1={calculate_cs1(500)}")   # 预期: 25