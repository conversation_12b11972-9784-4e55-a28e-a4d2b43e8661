def calculate_b2_optimized(w: float) -> int | str:
    """
    根据包装盒宽度(W)计算坑盒粘位尺寸(B2)。

    Args:
        w: 包装盒宽度 (mm)

    Returns:
        B2尺寸(mm)或表示未定义的字符串。
    """
    if w < 25:
        # 明确处理 W < 25 的情况
        return "[未定义]"
    elif w < 150: # 隐含 w >= 25
        return 20
    else:         # 隐含 w >= 150
        return 25

# --- 测试用例 ---
# 验证新增的边界情况
print(f"W=24.9, B2={calculate_b2_optimized(24.9)}") # 预期: [未定义]
print(f"W=10, B2={calculate_b2_optimized(10)}")     # 预期: [未定义]

# 验证原始逻辑
print(f"W=25, B2={calculate_b2_optimized(25)}")     # 预期: 20
print(f"W=149.9, B2={calculate_b2_optimized(149.9)}") # 预期: 20
print(f"W=150, B2={calculate_b2_optimized(150)}")   # 预期: 25
print(f"W=200, B2={calculate_b2_optimized(200)}")   # 预期: 25