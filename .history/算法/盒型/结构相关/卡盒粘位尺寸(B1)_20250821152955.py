def calculate_b1_final(w: float) -> float:
    """
    根据包装盒宽度(W)计算卡盒粘位尺寸(B1).

    Args:
        w: 包装盒宽度 (mm)

    Returns:
        B1尺寸(mm)。
    """
    # 1. 应用“不小于最小值”规则
    if w < 12:
        # 对于小于12的宽度，将B1钳制在算法的全局最小值10
        return 10
    
    # 2. W >= 12 的情况
    elif w < 15:
        # 线性规则
        return w - 2
    elif w < 25:
        return 12
    elif w < 35:
        return 13.5
    elif w < 180:
        return 15
    else:  # w >= 180
        return 18

# --- 测试用例 ---
# 验证新规则
print(f"W=10, B1={calculate_b1_final(10)}")     # 预期: 10 (被钳制在最小值)
print(f"W=5, B1={calculate_b1_final(5)}")      # 预期: 10 (被钳制在最小值)
# 验证原始规则
print(f"W=12, B1={calculate_b1_final(12)}")     # 预期: 10.0
print(f"W=14.9, B1={calculate_b1_final(14.9)}") # 预期: 12.9
print(f"W=15, B1={calculate_b1_final(15)}")     # 预期: 12
print(f"W=180, B1={calculate_b1_final(180)}")   # 预期: 18