# PDF线条长度计算工具集

本工具集提供了多种计算PDF文件中线条长度的方法，支持不同的PDF处理库。

## 文件说明

### 1. line_length.py
基础线条长度计算模块，提供以下功能：
- 两点间直线距离计算
- 折线总长度计算
- 矩形周长计算
- 圆周长计算
- 圆弧长度计算
- 包装盒切割线长度计算

### 2. pdf_line_analyzer.py
基于PyMuPDF的高级PDF线条分析器，功能包括：
- 提取PDF中的直线、曲线、矩形
- 计算各种图形的长度
- 按页面统计线条信息
- 生成详细分析报告
- 导出JSON格式结果

### 3. simple_pdf_calculator.py
简化版PDF线条计算器，特点：
- 支持多种PDF处理库（PyMuPDF、pdfplumber、PyPDF2）
- 自动选择可用的处理方式
- 容错性强，处理损坏的PDF文件
- 简洁的报告输出

## 安装依赖

### 必需依赖
```bash
# 推荐安装PyMuPDF（功能最完整）
pip install PyMuPDF

# 或者安装pdfplumber
pip install pdfplumber

# 基础PDF处理
pip install PyPDF2
```

### 可选依赖
```bash
# 所有依赖一次性安装
pip install PyMuPDF pdfplumber PyPDF2
```

## 使用方法

### 基础使用
```python
# 使用simple_pdf_calculator.py
python simple_pdf_calculator.py your_file.pdf

# 使用pdf_line_analyzer.py
python pdf_line_analyzer.py
```

### 代码集成
```python
from simple_pdf_calculator import SimplePDFCalculator

# 创建计算器实例
calculator = SimplePDFCalculator("your_file.pdf")

# 分析PDF
if calculator.analyze():
    report = calculator.get_report()
    print(f"总长度：{report['总长度']}")
    
    # 保存详细报告
    calculator.save_report("output.json")
```

## 输出格式

### 报告包含的信息
- 文件基本信息
- 总线条数
- 总长度（单位：PDF坐标单位）
- 按类型统计的线条数量
- 按页面统计的详细信息
- 详细线条数据（起点、终点、长度等）

### JSON输出示例
```json
{
  "summary": {
    "文件": "example.pdf",
    "总线条数": 25,
    "总长度": 1250.5,
    "线条类型": {
      "line": 15,
      "rectangle_edge": 10
    },
    "页面统计": {
      "1": {"count": 15, "length": 750.25},
      "2": {"count": 10, "length": 500.25}
    }
  },
  "lines": [
    {
      "type": "line",
      "page": 1,
      "start": [100, 100],
      "end": [200, 100],
      "length": 100.0,
      "source": "PyMuPDF"
    }
  ]
}
```

## 常见问题

### 1. PDF文件无法读取
- 检查文件路径是否正确
- 确认文件不是密码保护
- 尝试使用simple_pdf_calculator.py，它支持多种处理方式

### 2. 依赖安装失败
- 确保Python版本 >= 3.6
- 使用管理员权限安装：`pip install --user package_name`
- 尝试使用国内镜像：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple package_name`

### 3. 计算结果不准确
- PDF中的线条可能是矢量图形，确保使用支持矢量图形的库（PyMuPDF）
- 检查PDF文件是否包含实际的矢量线条，而非位图图像

## 技术说明

### 坐标系统
- PDF使用左下角为原点的坐标系统
- 长度单位为PDF点（1点 = 1/72英寸）
- 实际物理长度需要根据PDF的DPI进行转换

### 支持的图形类型
- **直线**：直接计算两点间距离
- **曲线**：使用线段近似计算长度
- **矩形**：分解为四条边分别计算
- **多边形**：分解为多条边分别计算

## 扩展功能

### 单位转换
```python
# 将PDF点转换为毫米
def points_to_mm(points, dpi=72):
    return points * 25.4 / dpi

# 将PDF点转换为英寸
def points_to_inches(points, dpi=72):
    return points / dpi
```

### 自定义处理
可以继承SimplePDFCalculator类，添加自定义的处理逻辑：
```python
class CustomCalculator(SimplePDFCalculator):
    def custom_processing(self):
        # 添加自定义处理逻辑
        pass
```

## 联系与支持

如有问题或建议，请通过以下方式联系：
- 提交Issue到项目仓库
- 发送邮件至开发者邮箱
- 查看详细的API文档和示例代码
