#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式线长计算器
用于实际计算包装印刷相关的线长
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from line_length import (
    calculate_line_length,
    calculate_polyline_length,
    calculate_rectangle_perimeter,
    calculate_circle_circumference,
    calculate_arc_length,
    calculate_cutting_line_length
)

def interactive_line_length():
    """交互式两点间直线距离计算"""
    print("\n=== 两点间直线距离计算 ===")
    try:
        x1 = float(input("请输入起点X坐标: "))
        y1 = float(input("请输入起点Y坐标: "))
        x2 = float(input("请输入终点X坐标: "))
        y2 = float(input("请输入终点Y坐标: "))
        
        distance = calculate_line_length((x1, y1), (x2, y2))
        if isinstance(distance, str):
            print(f"错误: {distance}")
        else:
            print(f"两点间直线距离: {distance:.2f} 单位")
    except ValueError:
        print("请输入有效的数字！")

def interactive_rectangle_perimeter():
    """交互式矩形周长计算"""
    print("\n=== 矩形周长计算 ===")
    try:
        length = float(input("请输入矩形长度 (mm): "))
        width = float(input("请输入矩形宽度 (mm): "))
        
        perimeter = calculate_rectangle_perimeter(length, width)
        if isinstance(perimeter, str):
            print(f"错误: {perimeter}")
        else:
            print(f"矩形周长: {perimeter:.2f} mm")
    except ValueError:
        print("请输入有效的数字！")

def interactive_circle_circumference():
    """交互式圆周长计算"""
    print("\n=== 圆周长计算 ===")
    try:
        radius = float(input("请输入圆半径 (mm): "))
        
        circumference = calculate_circle_circumference(radius)
        if isinstance(circumference, str):
            print(f"错误: {circumference}")
        else:
            print(f"圆周长: {circumference:.2f} mm")
    except ValueError:
        print("请输入有效的数字！")

def interactive_arc_length():
    """交互式圆弧长度计算"""
    print("\n=== 圆弧长度计算 ===")
    try:
        radius = float(input("请输入圆半径 (mm): "))
        angle = float(input("请输入圆心角 (度): "))
        
        arc_len = calculate_arc_length(radius, angle)
        if isinstance(arc_len, str):
            print(f"错误: {arc_len}")
        else:
            print(f"圆弧长度: {arc_len:.2f} mm")
    except ValueError:
        print("请输入有效的数字！")

def interactive_cutting_line():
    """交互式包装盒切割线计算"""
    print("\n=== 包装盒切割线计算 ===")
    try:
        length = float(input("请输入盒长 (mm): "))
        width = float(input("请输入盒宽 (mm): "))
        height = float(input("请输入盒高 (mm): "))
        thickness = input("请输入材料厚度 (mm，默认为3): ").strip()
        
        if thickness == "":
            thickness = 3
        else:
            thickness = float(thickness)
        
        result = calculate_cutting_line_length(length, width, height, thickness)
        if isinstance(result, str):
            print(f"错误: {result}")
        else:
            print("\n切割线计算结果:")
            for key, value in result.items():
                print(f"  {key}: {value} mm")
    except ValueError:
        print("请输入有效的数字！")

def interactive_polyline():
    """交互式折线长度计算"""
    print("\n=== 折线长度计算 ===")
    try:
        num_points = int(input("请输入点的数量 (至少2个点): "))
        if num_points < 2:
            print("至少需要2个点！")
            return
        
        points = []
        for i in range(num_points):
            print(f"\n第 {i+1} 个点:")
            x = float(input("  X坐标: "))
            y = float(input("  Y坐标: "))
            points.append((x, y))
        
        length = calculate_polyline_length(points)
        if isinstance(length, str):
            print(f"错误: {length}")
        else:
            print(f"\n折线总长度: {length:.2f} 单位")
    except ValueError:
        print("请输入有效的数字！")

def show_menu():
    """显示主菜单"""
    print("\n" + "="*50)
    print("    线长计算工具 - 交互式菜单")
    print("="*50)
    print("1. 两点间直线距离计算")
    print("2. 矩形周长计算")
    print("3. 圆周长计算")
    print("4. 圆弧长度计算")
    print("5. 包装盒切割线计算")
    print("6. 折线长度计算")
    print("0. 退出程序")
    print("="*50)

def main():
    """主程序"""
    print("欢迎使用线长计算工具！")
    
    while True:
        show_menu()
        choice = input("\n请选择功能 (0-6): ").strip()
        
        if choice == "0":
            print("感谢使用，再见！")
            break
        elif choice == "1":
            interactive_line_length()
        elif choice == "2":
            interactive_rectangle_perimeter()
        elif choice == "3":
            interactive_circle_circumference()
        elif choice == "4":
            interactive_arc_length()
        elif choice == "5":
            interactive_cutting_line()
        elif choice == "6":
            interactive_polyline()
        else:
            print("无效选择，请输入0-6之间的数字！")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
