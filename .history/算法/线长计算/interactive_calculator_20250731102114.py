#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式PDF线条长度计算器
提供友好的命令行界面
"""

import os
import sys
import json
from typing import List
import glob

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from simple_pdf_calculator import SimplePDFCalculator
from pdf_line_analyzer import PDFLineAnalyzer

class InteractiveCalculator:
    """交互式计算器类"""
    
    def __init__(self):
        self.current_file = None
        self.last_result = None
        
    def clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def show_banner(self):
        """显示欢迎信息"""
        print("\n" + "="*60)
        print("    PDF线条长度计算器 - 交互式版本")
        print("="*60)
        print("功能：")
        print("1. 分析PDF文件中的线条长度")
        print("2. 支持多种PDF处理引擎")
        print("3. 生成详细分析报告")
        print("4. 支持批量处理")
        print("="*60)
    
    def show_menu(self):
        """显示主菜单"""
        print("\n" + "-"*40)
        print("主菜单：")
        print("1. 分析单个PDF文件")
        print("2. 批量分析PDF文件")
        print("3. 使用高级分析器")
        print("4. 查看分析结果")
        print("5. 安装依赖")
        print("6. 退出")
        print("-"*40)
    
    def get_pdf_files(self, directory: str = ".") -> List[str]:
        """获取目录中的PDF文件"""
        pdf_files = []
        for ext in ["*.pdf", "*.PDF"]:
            pdf_files.extend(glob.glob(os.path.join(directory, ext)))
        return sorted(pdf_files)
    
    def select_file(self) -> str:
        """选择PDF文件"""
        print("\n当前目录中的PDF文件：")
        pdf_files = self.get_pdf_files()
        
        if not pdf_files:
            print("未找到PDF文件")
            return None
        
        for i, file in enumerate(pdf_files, 1):
            print(f"{i}. {file}")
        
        print("0. 手动输入文件路径")
        
        try:
            choice = int(input("\n请选择文件编号："))
            if choice == 0:
                return input("请输入PDF文件路径：").strip()
            elif 1 <= choice <= len(pdf_files):
                return pdf_files[choice - 1]
            else:
                print("无效的选择")
                return None
        except ValueError:
            print("请输入有效的数字")
            return None
    
    def analyze_single_file(self):
        """分析单个文件"""
        file_path = self.select_file()
        if not file_path:
            return
        
        if not os.path.exists(file_path):
            print(f"文件不存在：{file_path}")
            return
        
        print(f"\n正在分析：{file_path}")
        
        # 使用简化版计算器
        calculator = SimplePDFCalculator(file_path)
        if calculator.analyze():
            calculator.print_summary()
            
            # 保存结果
            save_choice = input("\n是否保存详细报告？(y/n): ").lower()
            if save_choice == 'y':
                output_path = file_path.replace(".pdf", "_report.json")
                saved_path = calculator.save_report(output_path)
                print(f"报告已保存到：{saved_path}")
            
            self.last_result = calculator.get_report()
            self.current_file = file_path
        else:
            print("分析失败")
    
    def batch_analyze(self):
        """批量分析"""
        directory = input("请输入PDF文件所在目录（默认为当前目录）：").strip()
        if not directory:
            directory = "."
        
        pdf_files = self.get_pdf_files(directory)
        if not pdf_files:
            print("未找到PDF文件")
            return
        
        print(f"\n找到 {len(pdf_files)} 个PDF文件")
        
        results = []
        for i, file_path in enumerate(pdf_files, 1):
            print(f"\n[{i}/{len(pdf_files)}] 分析：{os.path.basename(file_path)}")
            
            calculator = SimplePDFCalculator(file_path)
            if calculator.analyze():
                report = calculator.get_report()
                results.append({
                    "file": file_path,
                    "report": report
                })
                print(f"  ✓ 总长度：{report['总长度']} 单位")
            else:
                print(f"  ✗ 分析失败")
        
        # 保存批量结果
        if results:
            batch_report = {
                "total_files": len(pdf_files),
                "successful": len(results),
                "results": results
            }
            
            output_file = os.path.join(directory, "batch_analysis.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(batch_report, f, ensure_ascii=False, indent=2)
            
            print(f"\n批量分析完成！结果已保存到：{output_file}")
    
    def advanced_analyze(self):
        """使用高级分析器"""
        file_path = self.select_file()
        if not file_path:
            return
        
        print(f"\n使用高级分析器分析：{file_path}")
        
        analyzer = PDFLineAnalyzer(file_path)
        if analyzer.load_pdf():
            analyzer.extract_lines()
            analyzer.print_detailed_report()
            
            save_choice = input("\n是否保存详细报告？(y/n): ").lower()
            if save_choice == 'y':
                output_path = file_path.replace(".pdf", "_detailed.json")
                analyzer.export_results(output_path)
                print(f"详细报告已保存到：{output_path}")
    
    def view_results(self):
        """查看上次分析结果"""
        if not self.last_result:
            print("没有可用的分析结果")
            return
        
        print("\n" + "="*50)
        print("上次分析结果")
        print("="*50)
        print(f"文件：{self.current_file}")
        
        if "error" not in self.last_result:
            print(f"总线条数：{self.last_result['总线条数']}")
            print(f"总长度：{self.last_result['总长度']} 单位")
            
            print("\n线条类型：")
            for t, count in self.last_result["线条类型"].items():
                print(f"  {t}：{count}")
            
            print("\n页面统计：")
            for page, stats in self.last_result["页面统计"].items():
                print(f"  第{page}页：{stats['count']}条线，总长度{stats['length']:.2f}")
        else:
            print(self.last_result["error"])
    
    def install_dependencies(self):
        """安装依赖"""
        print("\n" + "="*40)
        print("安装依赖")
        print("="*40)
        
        dependencies = [
            "PyMuPDF",
            "pdfplumber",
            "PyPDF2"
        ]
        
        for dep in dependencies:
            try:
                if dep == "PyMuPDF":
                    import fitz
                    print(f"✓ {dep} 已安装")
                elif dep == "pdfplumber":
                    import pdfplumber
                    print(f"✓ {dep} 已安装")
                elif dep == "PyPDF2":
                    import PyPDF2
                    print(f"✓ {dep} 已安装")
            except ImportError:
                print(f"✗ {dep} 未安装")
                print(f"  安装命令：pip install {dep}")
    
    def run(self):
        """运行交互式程序"""
        self.clear_screen()
        self.show_banner()
        
        while True:
            self.show_menu()
            
            try:
                choice = int(input("\n请选择操作（1-6）："))
                
                if choice == 1:
                    self.analyze_single_file()
                elif choice == 2:
                    self.batch_analyze()
                elif choice == 3:
                    self.advanced_analyze()
                elif choice == 4:
                    self.view_results()
                elif choice == 5:
                    self.install_dependencies()
                elif choice == 6:
                    print("感谢使用！再见！")
                    break
                else:
                    print("无效的选择，请重试")
                
                input("\n按回车键继续...")
                self.clear_screen()
                
            except ValueError:
                print("请输入有效的数字")
                input("\n按回车键继续...")
                self.clear_screen()
            except KeyboardInterrupt:
                print("\n\n程序被中断，再见！")
                break

def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 命令行模式
        file_path = sys.argv[1]
        if os.path.exists(file_path):
            calculator = SimplePDFCalculator(file_path)
            if calculator.analyze():
                calculator.print_summary()
                calculator.save_report()
        else:
            print(f"文件不存在：{file_path}")
    else:
        # 交互式模式
        app = InteractiveCalculator()
        app.run()

if __name__ == "__main__":
    main()
