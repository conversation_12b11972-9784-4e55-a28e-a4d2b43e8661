import fitz  # PyMuPDF库
import numpy as np
from typing import List, Tuple

# ==============================================================================
# 核心计算函数 (复用自方法一)
# ==============================================================================
def calculate_length_from_points(points: np.ndarray) -> float:
    """
    高效地计算由一系列有序点定义的单条线的长度。
    """
    if points.shape[0] < 2:
        return 0.0
    
    diffs = np.diff(points, axis=0)
    segment_lengths = np.linalg.norm(diffs, axis=1)
    total_length = np.sum(segment_lengths)
    
    return total_length

# ==============================================================================
# 步骤一：从PDF提取线条坐标
# ==============================================================================
def extract_lines_from_pdf(pdf_path: str) -> List[np.ndarray]:
    """
    从PDF文件的所有页面中提取所有矢量线条。
    
    参数:
    pdf_path (str): PDF文件的路径。

    返回:
    List[np.ndarray]: 一个列表，其中每个元素都是一个Numpy数组，
                      代表一条连续的线（由其顶点坐标构成）。
    """
    all_lines = []
    doc = fitz.open(pdf_path)
    
    print(f"开始处理PDF文件: {pdf_path}，共 {len(doc)} 页...")
    
    for page_num, page in enumerate(doc):
        # 使用 page.get_drawings() 来提取页面中的所有矢量图形
        # 这些图形包括线、矩形、二次和三次贝塞尔曲线等
        drawings = page.get_drawings()
        
        print(f"  - 正在处理第 {page_num + 1} 页，发现 {len(drawings)} 个绘图对象...")
        
        for path in drawings:
            # path['items'] 包含绘图指令，如 'l' (line), 'c' (curve), 're' (rect)
            # 我们将每个path（可能包含多个线段或曲线）中的所有点连接起来
            
            current_line_points = []
            
            # path['rect'] 定义了矩形，我们可以将其转换为闭合线条
            if path['type'] == 's' and path['rect']:
                r = path['rect']
                current_line_points.extend([
                    (r.x0, r.y0), (r.x1, r.y0), 
                    (r.x1, r.y1), (r.x0, r.y1), 
                    (r.x0, r.y0) # 闭合
                ])

            # 遍历路径中的所有绘图项
            for item in path['items']:
                command = item[0]
                points = item[1:]
                
                if command == 'l':  # 'l': line to (x, y)
                    # 路径的起始点需要从上一个点或path的起点获得
                    if not current_line_points:
                         # 如果是路径的第一个点，添加起点
                         current_line_points.append( (path['start'].x, path['start'].y) )
                    current_line_points.append( (points[0].x, points[0].y) )
                
                elif command == 'c': # 'c': curve to (x1, y1, x2, y2, x3, y3)
                    if not current_line_points:
                         current_line_points.append( (path['start'].x, path['start'].y) )
                    # 对于贝塞尔曲线，我们通过添加其控制点来近似其长度
                    # 一个更精确的方法是递归地细分曲线，但通常这已足够
                    current_line_points.append( (points[0].x, points[0].y) )
                    current_line_points.append( (points[1].x, points[1].y) )
                    current_line_points.append( (points[2].x, points[2].y) )

            if len(current_line_points) > 1:
                all_lines.append(np.array(current_line_points))

    doc.close()
    return all_lines

# ==============================================================================
# 步骤二：主流程 - 计算总长度
# ==============================================================================
def main():
    # --- 请将这里替换为您的PDF文件路径 ---
    pdf_file_path = "/Users/<USER>/Desktop/pdf-line.pdf" 
    
    try:
        # 1. 从PDF提取所有线条
        extracted_lines = extract_lines_from_pdf(pdf_file_path)
        
        if not extracted_lines:
            print("在PDF中没有找到可计算长度的矢量线条。")
            return
            
        print(f"\n提取完成！共找到 {len(extracted_lines)} 条独立的矢量路径。")
        
        # 2. 计算每条线的长度并累加
        total_length = 0.0
        line_lengths = []
        
        for i, line_points in enumerate(extracted_lines):
            length = calculate_length_from_points(line_points)
            line_lengths.append(length)
            total_length += length
            # 如果需要，可以取消下面的注释来查看每一条线的长度
            # print(f"  - 线 {i+1:03d} 的长度: {length:.2f}")

        # 3. 打印最终结果
        print("\n--- 计算结果 ---")
        print(f"所有线条的总长度为: {total_length:.4f}")
        # PDF中的单位通常是 "points" (1 point = 1/72 inch)。
        # 您可能需要根据PDF的实际情况进行单位换算。
        print(f"以英寸计的总长度约为: {total_length / 72:.4f} inches")
        print(f"以厘米计的总长度约为: {(total_length / 72) * 2.54:.4f} cm")
        
    except FileNotFoundError:
        print(f"错误：文件未找到，请确认路径 '{pdf_file_path}' 是否正确。")
    except Exception as e:
        raise

if __name__ == '__main__':
    main()