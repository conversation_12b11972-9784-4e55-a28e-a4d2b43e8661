#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版PDF线条长度计算器
支持多种PDF处理方式
"""

import os
import sys
import json
import math
from typing import List, Dict, Tuple, Optional

class SimplePDFCalculator:
    """简化版PDF线条计算器"""
    
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.lines = []
        self.total_length = 0.0
        
    def calculate_distance(self, p1: Tuple[float, float], p2: Tuple[float, float]) -> float:
        """计算两点间距离"""
        return math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
    
    def try_pymupdf(self) -> bool:
        """尝试使用PyMuPDF"""
        try:
            import fitz
            print("使用PyMuPDF处理...")
            
            doc = fitz.open(self.pdf_path)
            print(f"PDF页数：{len(doc)}")
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                drawings = page.get_drawings()
                
                for drawing in drawings:
                    for item in drawing.get("items", []):
                        if item[0] == "l":  # 直线
                            start = (item[1].x, item[1].y)
                            end = (item[2].x, item[2].y)
                            length = self.calculate_distance(start, end)
                            
                            self.lines.append({
                                "type": "line",
                                "page": page_num + 1,
                                "start": start,
                                "end": end,
                                "length": length,
                                "source": "PyMuPDF"
                            })
                            
                        elif item[0] == "re":  # 矩形
                            rect = item[1]
                            # 四条边
                            edges = [
                                ((rect.x0, rect.y0), (rect.x1, rect.y0)),
                                ((rect.x1, rect.y0), (rect.x1, rect.y1)),
                                ((rect.x1, rect.y1), (rect.x0, rect.y1)),
                                ((rect.x0, rect.y1), (rect.x0, rect.y0))
                            ]
                            
                            for start, end in edges:
                                length = self.calculate_distance(start, end)
                                self.lines.append({
                                    "type": "rectangle_edge",
                                    "page": page_num + 1,
                                    "start": start,
                                    "end": end,
                                    "length": length,
                                    "source": "PyMuPDF"
                                })
            
            doc.close()
            return True
            
        except ImportError:
            print("PyMuPDF未安装")
            return False
        except Exception as e:
            print(f"PyMuPDF处理失败：{e}")
            return False
    
    def try_pdfplumber(self) -> bool:
        """尝试使用pdfplumber"""
        try:
            import pdfplumber
            print("使用pdfplumber处理...")
            
            with pdfplumber.open(self.pdf_path) as pdf:
                print(f"PDF页数：{len(pdf.pages)}")
                
                for page_num, page in enumerate(pdf.pages):
                    # 获取线条
                    lines = page.lines
                    for line in lines:
                        start = (line['x0'], line['y0'])
                        end = (line['x1'], line['y1'])
                        length = self.calculate_distance(start, end)
                        
                        self.lines.append({
                            "type": "line",
                            "page": page_num + 1,
                            "start": start,
                            "end": end,
                            "length": length,
                            "source": "pdfplumber"
                        })
                    
                    # 获取矩形
                    rects = page.rects
                    for rect in rects:
                        edges = [
                            ((rect['x0'], rect['y0']), (rect['x1'], rect['y0'])),
                            ((rect['x1'], rect['y0']), (rect['x1'], rect['y1'])),
                            ((rect['x1'], rect['y1']), (rect['x0'], rect['y1'])),
                            ((rect['x0'], rect['y1']), (rect['x0'], rect['y0']))
                        ]
                        
                        for start, end in edges:
                            length = self.calculate_distance(start, end)
                            self.lines.append({
                                "type": "rectangle_edge",
                                "page": page_num + 1,
                                "start": start,
                                "end": end,
                                "length": length,
                                "source": "pdfplumber"
                            })
            
            return True
            
        except ImportError:
            print("pdfplumber未安装")
            return False
        except Exception as e:
            print(f"pdfplumber处理失败：{e}")
            return False
    
    def try_pypdf2(self) -> bool:
        """尝试使用PyPDF2获取页面尺寸"""
        try:
            import PyPDF2
            print("使用PyPDF2获取基本信息...")
            
            with open(self.pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                print(f"PDF页数：{len(reader.pages)}")
                
                # PyPDF2主要用于文本提取，对矢量图形支持有限
                # 这里只提供基本信息
                for page_num, page in enumerate(reader.pages):
                    if '/MediaBox' in page:
                        box = page['/MediaBox']
                        width = float(box[2]) - float(box[0])
                        height = float(box[3]) - float(box[1])
                        print(f"第{page_num + 1}页尺寸：{width} x {height}")
            
            return True
            
        except ImportError:
            print("PyPDF2未安装")
            return False
        except Exception as e:
            print(f"PyPDF2处理失败：{e}")
            return False
    
    def analyze(self) -> bool:
        """分析PDF文件"""
        if not os.path.exists(self.pdf_path):
            print(f"文件不存在：{self.pdf_path}")
            return False
        
        print(f"开始分析PDF：{self.pdf_path}")
        
        # 尝试不同的处理方式
        methods = [
            self.try_pymupdf,
            self.try_pdfplumber,
            self.try_pypdf2
        ]
        
        success = False
        for method in methods:
            try:
                if method():
                    success = True
                    break
            except Exception as e:
                print(f"方法 {method.__name__} 失败：{e}")
                continue
        
        if not success:
            print("所有处理方法都失败了")
            return False
        
        # 计算总长度
        self.total_length = sum(line["length"] for line in self.lines)
        
        return True
    
    def get_report(self) -> Dict:
        """获取分析报告"""
        if not self.lines:
            return {"error": "未找到线条"}
        
        # 按类型统计
        type_stats = {}
        for line in self.lines:
            t = line["type"]
            type_stats[t] = type_stats.get(t, 0) + 1
        
        # 按页面统计
        page_stats = {}
        for line in self.lines:
            p = line["page"]
            if p not in page_stats:
                page_stats[p] = {"count": 0, "length": 0.0}
            page_stats[p]["count"] += 1
            page_stats[p]["length"] += line["length"]
        
        return {
            "文件": os.path.basename(self.pdf_path),
            "总线条数": len(self.lines),
            "总长度": round(self.total_length, 2),
            "线条类型": type_stats,
            "页面统计": page_stats,
            "处理引擎": list(set(line.get("source", "unknown") for line in self.lines))
        }
    
    def save_report(self, output_path: str = None) -> str:
        """保存报告"""
        if not output_path:
            output_path = self.pdf_path.replace(".pdf", "_report.json")
        
        report = self.get_report()
        
        # 添加详细线条信息
        full_report = {
            "summary": report,
            "lines": self.lines
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(full_report, f, ensure_ascii=False, indent=2)
        
        return output_path
    
    def print_summary(self):
        """打印摘要"""
        report = self.get_report()
        
        if "error" in report:
            print(report["error"])
            return
        
        print("\n" + "="*50)
        print("PDF线条长度分析报告")
        print("="*50)
        
        print(f"文件：{report['文件']}")
        print(f"总线条数：{report['总线条数']}")
        print(f"总长度：{report['总长度']} 单位")
        print(f"处理引擎：{', '.join(report['处理引擎'])}")
        
        print("\n线条类型：")
        for t, count in report["线条类型"].items():
            print(f"  {t}：{count}")
        
        print("\n页面统计：")
        for page, stats in report["页面统计"].items():
            print(f"  第{page}页：{stats['count']}条线，总长度{stats['length']:.2f}")

def install_requirements():
    """安装依赖"""
    print("正在检查依赖...")
    
    try:
        import fitz
        print("✓ PyMuPDF已安装")
    except ImportError:
        print("建议安装：pip install PyMuPDF")
    
    try:
        import pdfplumber
        print("✓ pdfplumber已安装")
    except ImportError:
        print("建议安装：pip install pdfplumber")
    
    try:
        import PyPDF2
        print("✓ PyPDF2已安装")
    except ImportError:
        print("建议安装：pip install PyPDF2")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    else:
        pdf_path = "算法/线长计算/pdf-line.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"文件不存在：{pdf_path}")
        return
    
    # 检查依赖
    install_requirements()
    
    # 创建计算器
    calculator = SimplePDFCalculator(pdf_path)
    
    # 分析PDF
    if calculator.analyze():
        calculator.print_summary()
        
        # 保存报告
        report_path = calculator.save_report()
        print(f"\n详细报告已保存到：{report_path}")
    else:
        print("分析失败")

if __name__ == "__main__":
    main()
