"""Core line length calculation functionality."""

import math
import time
from typing import List, Optional, Tuple, Any
import logging

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

from .models import LineInfo, AnalysisResult, ProcessingOptions, Point

logger = logging.getLogger(__name__)


class LineLengthCalculator:
    """Main calculator for line lengths in PDF files."""
    
    def __init__(self):
        self.processing_options = ProcessingOptions()
        
    def calculate_distance(self, point1: Tuple[float, float], point2: Tuple[float, float]) -> float:
        """Calculate Euclidean distance between two points."""
        return math.sqrt((point2[0] - point1[0]) ** 2 + (point2[1] - point1[1]) ** 2)
    
    def is_horizontal_line(self, line: LineInfo, tolerance: float = 0.01) -> bool:
        """Check if line is horizontal within tolerance."""
        return abs(line.start_y - line.end_y) <= tolerance
    
    def is_vertical_line(self, line: LineInfo, tolerance: float = 0.01) -> bool:
        """Check if line is vertical within tolerance."""
        return abs(line.start_x - line.end_x) <= tolerance
    
    def is_diagonal_line(self, line: LineInfo, tolerance: float = 0.01) -> bool:
        """Check if line is diagonal."""
        return not (self.is_horizontal_line(line, tolerance) or 
                   self.is_vertical_line(line, tolerance))
    
    def filter_lines(self, lines: List[LineInfo], options: ProcessingOptions) -> List[LineInfo]:
        """Filter lines based on processing options."""
        filtered = []
        
        for line in lines:
            # Length filtering
            if line.length < options.min_length:
                continue
            if options.max_length and line.length > options.max_length:
                continue
            
            # Diagonal filtering
            if not options.include_diagonals and self.is_diagonal_line(line, options.tolerance):
                continue
            
            # Stroke width filtering
            if (line.stroke_width and 
                line.stroke_width < options.stroke_width_threshold):
                continue
            
            # Color filtering (basic implementation)
            if options.color_filter and line.color != options.color_filter:
                continue
            
            # Page range filtering
            if options.page_range and line.page not in options.page_range:
                continue
                
            filtered.append(line)
        
        return filtered


class PyMuPDFProcessor:
    """PDF processing using PyMuPDF."""
    
    @staticmethod
    def extract_lines(pdf_path: str, options: ProcessingOptions) -> List[LineInfo]:
        """Extract lines using PyMuPDF."""
        if not PYMUPDF_AVAILABLE:
            raise ImportError("PyMuPDF not available")
        
        lines = []
        doc = fitz.open(pdf_path)
        
        try:
            for page_num in range(len(doc)):
                if (options.page_range and 
                    page_num + 1 not in options.page_range):
                    continue
                
                page = doc[page_num]
                drawings = page.get_drawings()
                
                for drawing in drawings:
                    if drawing['items']:
                        for item in drawing['items']:
                            if item[0] == 'l':  # Line
                                start_point = item[1]
                                end_point = item[2]
                                
                                length = math.sqrt(
                                    (end_point[0] - start_point[0]) ** 2 +
                                    (end_point[1] - start_point[1]) ** 2
                                )
                                
                                line = LineInfo(
                                    start_x=start_point[0],
                                    start_y=start_point[1],
                                    end_x=end_point[0],
                                    end_y=end_point[1],
                                    length=length,
                                    page=page_num + 1,
                                    stroke_width=drawing.get('width'),
                                    color=str(drawing.get('color', ''))
                                )
                                lines.append(line)
        finally:
            doc.close()
        
        return lines


class PDFPlumberProcessor:
    """PDF processing using pdfplumber."""
    
    @staticmethod
    def extract_lines(pdf_path: str, options: ProcessingOptions) -> List[LineInfo]:
        """Extract lines using pdfplumber."""
        if not PDFPLUMBER_AVAILABLE:
            raise ImportError("pdfplumber not available")
        
        lines = []
        
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                if (options.page_range and 
                    page_num + 1 not in options.page_range):
                    continue
                
                # Get lines from page
                if hasattr(page, 'lines'):
                    for line in page.lines:
                        start_x = line.get('x0', 0)
                        start_y = line.get('y0', 0)
                        end_x = line.get('x1', 0)
                        end_y = line.get('y1', 0)
                        
                        length = math.sqrt(
                            (end_x - start_x) ** 2 + (end_y - start_y) ** 2
                        )
                        
                        line_info = LineInfo(
                            start_x=start_x,
                            start_y=start_y,
                            end_x=end_x,
                            end_y=end_y,
                            length=length,
                            page=page_num + 1,
                            stroke_width=line.get('stroke_width'),
                            color=str(line.get('stroke_color', ''))
                        )
                        lines.append(line_info)
        
        return lines


class UniversalProcessor:
    """Universal processor that tries multiple backends."""
    
    def __init__(self):
        self.calculator = LineLengthCalculator()
    
    def process_pdf(self, pdf_path: str, options: ProcessingOptions = None) -> AnalysisResult:
        """Process PDF using the best available backend."""
        if options is None:
            options = ProcessingOptions()
        
        start_time = time.time()
        lines = []
        
        # Try PyMuPDF first
        if PYMUPDF_AVAILABLE:
            try:
                lines = PyMuPDFProcessor.extract_lines(pdf_path, options)
                logger.info("Used PyMuPDF backend")
            except Exception as e:
                logger.warning(f"PyMuPDF failed: {e}")
        
        # Fallback to pdfplumber
        if not lines and PDFPLUMBER_AVAILABLE:
            try:
                lines = PDFPlumberProcessor.extract_lines(pdf_path, options)
                logger.info("Used pdfplumber backend")
            except Exception as e:
                logger.warning(f"pdfplumber failed: {e}")
        
        if not lines:
            raise RuntimeError("No suitable PDF backend available")
        
        # Filter lines
        filtered_lines = self.calculator.filter_lines(lines, options)
        
        # Calculate totals
        total_length = sum(line.length for line in filtered_lines)
        
        # Get page count
        page_count = 1  # Default, will be updated
        
        processing_time = time.time() - start_time
        
        return AnalysisResult(
            filename=pdf_path,
            total_lines=len(filtered_lines),
            total_length=total_length,
            lines=filtered_lines,
            page_count=page_count,
            processing_time=processing_time
        )
