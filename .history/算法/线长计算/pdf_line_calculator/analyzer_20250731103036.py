"""Advanced PDF line analyzer with visualization and reporting."""

import json
import os
from pathlib import Path
from typing import Optional, Dict, Any
import logging

import matplotlib.pyplot as plt
import numpy as np
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

from .core import UniversalProcessor
from .models import ProcessingOptions, AnalysisResult

console = Console()
logger = logging.getLogger(__name__)


class PDFLineAnalyzer:
    """Advanced analyzer for PDF line analysis with visualization."""
    
    def __init__(self):
        self.processor = UniversalProcessor()
    
    def analyze(self, pdf_path: str, options: ProcessingOptions = None) -> AnalysisResult:
        """Analyze PDF and return detailed results."""
        if options is None:
            options = ProcessingOptions()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Analyzing PDF...", total=None)
            result = self.processor.process_pdf(pdf_path, options)
            progress.update(task, completed=True)
        
        return result
    
    def generate_report(self, result: AnalysisResult, output_dir: str = None) -> str:
        """Generate comprehensive analysis report."""
        if output_dir is None:
            output_dir = os.path.dirname(result.filename)
        
        report_path = os.path.join(output_dir, f"{Path(result.filename).stem}_analysis_report.json")
        
        report_data = {
            "summary": {
                "filename": result.filename,
                "total_lines": result.total_lines,
                "total_length": result.total_length,
                "average_length": result.average_length,
                "page_count": result.page_count,
                "processing_time": result.processing_time
            },
            "lines": [
                {
                    "start_x": line.start_x,
                    "start_y": line.start_y,
                    "end_x": line.end_x,
                    "end_y": line.end_y,
                    "length": line.length,
                    "page": line.page,
                    "stroke_width": line.stroke_width,
                    "color": line.color
                }
                for line in result.lines
            ],
            "statistics": self._calculate_statistics(result)
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        return report_path
    
    def _calculate_statistics(self, result: AnalysisResult) -> Dict[str, Any]:
        """Calculate detailed statistics."""
        if not result.lines:
            return {}
        
        lengths = [line.length for line in result.lines]
        
        stats = {
            "length_distribution": {
                "min": min(lengths),
                "max": max(lengths),
                "mean": np.mean(lengths),
                "median": np.median(lengths),
                "std": np.std(lengths),
                "percentiles": {
                    "25th": np.percentile(lengths, 25),
                    "75th": np.percentile(lengths, 75),
                    "90th": np.percentile(lengths, 90),
                    "95th": np.percentile(lengths, 95)
                }
            },
            "page_distribution": {},
            "line_types": {
                "horizontal": 0,
                "vertical": 0,
                "diagonal": 0
            }
        }
        
        # Page distribution
        for line in result.lines:
            page = line.page
            if page not in stats["page_distribution"]:
                stats["page_distribution"][page] = {
                    "count": 0,
                    "total_length": 0
                }
            stats["page_distribution"][page]["count"] += 1
            stats["page_distribution"][page]["total_length"] += line.length
        
        # Line type distribution
        calculator = self.processor.calculator
        for line in result.lines:
            if calculator.is_horizontal_line(line):
                stats["line_types"]["horizontal"] += 1
            elif calculator.is_vertical_line(line):
                stats["line_types"]["vertical"] += 1
            else:
                stats["line_types"]["diagonal"] += 1
        
        return stats
    
    def display_summary(self, result: AnalysisResult):
        """Display analysis summary using rich formatting."""
        console.print("\n[bold green]PDF Line Analysis Summary[/bold green]")
        console.print("=" * 50)
        
        # Basic info
        table = Table(title="Basic Information")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="yellow")
        
        table.add_row("Filename", Path(result.filename).name)
        table.add_row("Total Lines", str(result.total_lines))
        table.add_row("Total Length", f"{result.total_length:.2f}")
        table.add_row("Average Length", f"{result.average_length:.2f}")
        table.add_row("Pages", str(result.page_count))
        table.add_row("Processing Time", f"{result.processing_time:.2f}s")
        
        console.print(table)
        
        if result.lines:
            # Statistics
            stats = self._calculate_statistics(result)
            
            console.print("\n[bold blue]Length Statistics[/bold blue]")
            length_stats = stats["length_distribution"]
            stats_table = Table()
            stats_table.add_column("Statistic", style="cyan")
            stats_table.add_column("Value", style="yellow")
            
            stats_table.add_row("Minimum", f"{length_stats['min']:.2f}")
            stats_table.add_row("Maximum", f"{length_stats['max']:.2f}")
            stats_table.add_row("Mean", f"{length_stats['mean']:.2f}")
            stats_table.add_row("Median", f"{length_stats['median']:.2f}")
            stats_table.add_row("Std Dev", f"{length_stats['std']:.2f}")
            
            console.print(stats_table)
            
            # Line types
            console.print("\n[bold magenta]Line Type Distribution[/bold magenta]")
            type_table = Table()
            type_table.add_column("Type", style="cyan")
            type_table.add_column("Count", style="yellow")
            type_table.add_column("Percentage", style="green")
            
            total = sum(stats["line_types"].values())
            for line_type, count in stats["line_types"].items():
                percentage = (count / total * 100) if total > 0 else 0
                type_table.add_row(
                    line_type.title(), 
                    str(count), 
                    f"{percentage:.1f}%"
                )
            
            console.print(type_table)
    
    def create_visualization(self, result: AnalysisResult, output_dir: str = None) -> str:
        """Create visualization of line analysis."""
        if output_dir is None:
            output_dir = os.path.dirname(result.filename)
        
        # Create multiple plots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'PDF Line Analysis: {Path(result.filename).name}', fontsize=16)
        
        if result.lines:
            lengths = [line.length for line in result.lines]
            pages = [line.page for line in result.lines]
            
            # Length distribution histogram
            ax1.hist(lengths, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            ax1.set_title('Line Length Distribution')
            ax1.set_xlabel('Length')
            ax1.set_ylabel('Frequency')
            
            # Length vs Page scatter
            ax2.scatter(pages, lengths, alpha=0.6, color='orange')
            ax2.set_title('Length by Page')
            ax2.set_xlabel('Page')
            ax2.set_ylabel('Length')
            
            # Line type pie chart
            stats = self._calculate_statistics(result)
            line_types = stats["line_types"]
            colors = ['#ff9999', '#66b3ff', '#99ff99']
            ax3.pie(line_types.values(), labels=line_types.keys(), colors=colors, autopct='%1.1f%%')
            ax3.set_title('Line Type Distribution')
            
            # Page distribution
            page_data = stats["page_distribution"]
            pages_list = list(page_data.keys())
            counts = [page_data[p]["count"] for p in pages_list]
            ax4.bar(pages_list, counts, color='lightcoral')
            ax4.set_title('Lines per Page')
            ax4.set_xlabel('Page')
            ax4.set_ylabel('Count')
        
        else:
            fig.text(0.5, 0.5, 'No lines found', ha='center', va='center', 
                    transform=fig.transFigure, fontsize=14)
        
        plt.tight_layout()
        
        # Save plot
        plot_path = os.path.join(output_dir, f"{Path(result.filename).stem}_analysis.png")
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return plot_path
    
    def export_csv(self, result: AnalysisResult, output_dir: str = None) -> str:
        """Export line data to CSV."""
        if output_dir is None:
            output_dir = os.path.dirname(result.filename)
        
        csv_path = os.path.join(output_dir, f"{Path(result.filename).stem}_lines.csv")
        
        import csv
        
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['start_x', 'start_y', 'end_x', 'end_y', 'length', 'page', 
                         'stroke_width', 'color', 'line_type']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for line in result.lines:
                writer.writerow({
                    'start_x': line.start_x,
                    'start_y': line.start_y,
                    'end_x': line.end_x,
                    'end_y': line.end_y,
                    'length': line.length,
                    'page': line.page,
                    'stroke_width': line.stroke_width or '',
                    'color': line.color or '',
                    'line_type': line.line_type
                })
        
        return csv_path


def main():
    """Main function for command line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Analyze PDF line lengths")
    parser.add_argument("pdf_path", help="Path to PDF file")
    parser.add_argument("--min-length", type=float, default=0.1, 
                       help="Minimum line length to include")
    parser.add_argument("--max-length", type=float, 
                       help="Maximum line length to include")
    parser.add_argument("--no-diagonals", action="store_true",
                       help="Exclude diagonal lines")
    parser.add_argument("--output-dir", help="Output directory for reports")
