"""Data models for PDF line length calculation."""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from dataclasses import dataclass


@dataclass
class Point:
    """Represents a 2D point."""
    x: float
    y: float
    
    def distance_to(self, other: 'Point') -> float:
        """Calculate Euclidean distance to another point."""
        return ((self.x - other.x) ** 2 + (self.y - other.y) ** 2) ** 0.5


class LineInfo(BaseModel):
    """Information about a detected line in PDF."""
    start_x: float
    start_y: float
    end_x: float
    end_y: float
    length: float
    page: int
    stroke_width: Optional[float] = None
    color: Optional[str] = None
    line_type: str = "line"
    
    @property
    def start_point(self) -> Point:
        """Get start point as Point object."""
        return Point(self.start_x, self.start_y)
    
    @property
    def end_point(self) -> Point:
        """Get end point as Point object."""
        return Point(self.end_x, self.end_y)
    
    @property
    def midpoint(self) -> Point:
        """Get midpoint of the line."""
        return Point(
            (self.start_x + self.end_x) / 2,
            (self.start_y + self.end_y) / 2
        )


class AnalysisResult(BaseModel):
    """Result of PDF line analysis."""
    filename: str
    total_lines: int
    total_length: float
    lines: List[LineInfo]
    page_count: int
    processing_time: float
    metadata: Dict[str, Any] = {}
    
    @property
    def average_length(self) -> float:
        """Calculate average line length."""
        return self.total_length / max(self.total_lines, 1)
    
    def get_lines_by_page(self, page: int) -> List[LineInfo]:
        """Get all lines from a specific page."""
        return [line for line in self.lines if line.page == page]
    
    def get_longest_line(self) -> Optional[LineInfo]:
        """Get the longest line."""
        if not self.lines:
            return None
        return max(self.lines, key=lambda l: l.length)
    
    def get_shortest_line(self) -> Optional[LineInfo]:
        """Get the shortest line."""
        if not self.lines:
            return None
        return min(self.lines, key=lambda l: l.length)


class ProcessingOptions(BaseModel):
    """Options for PDF processing."""
    min_length: float = 0.1
    max_length: Optional[float] = None
    include_diagonals: bool = True
    stroke_width_threshold: float = 0.0
    color_filter: Optional[str] = None
    page_range: Optional[List[int]] = None
    tolerance: float = 0.01
