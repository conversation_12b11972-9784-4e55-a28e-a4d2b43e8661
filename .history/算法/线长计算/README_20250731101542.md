# 线长计算工具

## 简介
这是一个专为包装印刷行业设计的线长计算工具，提供多种线长计算功能。

## 文件说明
- `line_length.py` - 核心计算模块，包含所有线长计算函数
- `interactive_calculator.py` - 交互式计算器，提供用户友好的菜单界面
- `README.md` - 使用说明文档

## 功能特性

### 1. 基础线长计算
- **两点间直线距离**：计算二维平面上两点间的直线距离
- **折线长度**：计算多个点组成的折线总长度
- **矩形周长**：计算矩形的周长
- **圆周长**：计算圆的周长
- **圆弧长度**：计算圆弧的长度

### 2. 包装印刷专用计算
- **包装盒切割线计算**：计算包装盒的切割线总长度，包括外框、高度切割线和折痕线

## 使用方法

### 方法1：直接运行测试
```bash
cd 算法/线长计算
python3 line_length.py
```

### 方法2：使用交互式计算器
```bash
cd 算法/线长计算
python3 interactive_calculator.py
```

### 方法3：作为模块导入使用
```python
from line_length import calculate_line_length, calculate_cutting_line_length

# 计算两点间距离
distance = calculate_line_length((0, 0), (100, 50))

# 计算包装盒切割线
result = calculate_cutting_line_length(200, 150, 80, 3)
```

## 函数说明

### calculate_line_length(start_point, end_point)
计算两点间直线距离
- 参数：start_point (x1, y1), end_point (x2, y2)
- 返回：距离值

### calculate_polyline_length(points)
计算折线总长度
- 参数：points = [(x1,y1), (x2,y2), ...]
- 返回：总长度

### calculate_rectangle_perimeter(length, width)
计算矩形周长
- 参数：length, width
- 返回：周长

### calculate_circle_circumference(radius)
计算圆周长
- 参数：radius
- 返回：周长

### calculate_arc_length(radius, angle_degrees)
计算圆弧长度
- 参数：radius, angle_degrees（角度）
- 返回：弧长

### calculate_cutting_line_length(box_length, box_width, box_height, material_thickness=3)
计算包装盒切割线
- 参数：盒长、盒宽、盒高、材料厚度（默认3mm）
- 返回：包含各项切割线长度的字典

## 示例

### 包装盒切割线计算
```python
result = calculate_cutting_line_length(200, 150, 80, 3)
# 结果：
# 外框周长: 700 mm
# 高度切割线: 320 mm
# 折痕线: 676 mm
# 总切割线长度: 1696 mm
```

### 折线长度计算
```python
points = [(0,0), (50,0), (50,30), (100,30)]
length = calculate_polyline_length(points)  # 结果: 130.0 mm
```

## 错误处理
所有函数都包含完善的错误处理机制：
- 检查参数类型和范围
- 提供清晰的错误提示信息
- 防止除零错误和无效输入

## 更新日志
- 2025-07-31: 初始版本创建，包含基础线长计算功能
- 2025-07-31: 添加包装印刷专用计算功能
- 2025-07-31: 添加交互式计算器界面
