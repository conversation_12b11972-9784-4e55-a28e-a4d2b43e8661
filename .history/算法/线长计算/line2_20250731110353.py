import xml.etree.ElementTree as ET
import re
import numpy as np
import math
from typing import List, Tu<PERSON>, Dict, Union
import json

try:
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

try:
    from svgpathtools import parse_path, Path, Line, QuadraticBezier, CubicBezier, Arc
    SVGPATHTOOLS_AVAILABLE = True
except ImportError:
    SVGPATHTOOLS_AVAILABLE = False

class SVGPDFLineAnalyzer:
    """SVG和PDF文件中线条长度分析工具"""
    
    def __init__(self):
        self.results = {
            'total_length': 0.0,
            'line_count': 0,
            'paths': [],
            'statistics': {}
        }
    
    def analyze_svg_file(self, svg_file_path: str) -> Dict:
        """
        分析SVG文件中的所有线条和路径长度
        
        Args:
            svg_file_path: SVG文件路径
            
        Returns:
            dict: 包含长度信息的分析结果
        """
        try:
            tree = ET.parse(svg_file_path)
            root = tree.getroot()
            
            # 重置结果
            self.results = {
                'total_length': 0.0,
                'line_count': 0,
                'paths': [],
                'statistics': {}
            }
            
            # 处理不同类型的图形元素
            self._process_svg_elements(root)
            
            # 计算统计信息
            self._calculate_statistics()
            
            return self.results
            
        except Exception as e:
            return {'error': f'解析SVG文件时出错: {str(e)}'}
    
    def _process_svg_elements(self, element):
        """递归处理SVG元素"""
        # 处理path元素
        if element.tag.endswith('path'):
            self._process_path_element(element)
        
        # 处理line元素
        elif element.tag.endswith('line'):
            self._process_line_element(element)
        
        # 处理polyline元素
        elif element.tag.endswith('polyline'):
            self._process_polyline_element(element)
        
        # 处理polygon元素
        elif element.tag.endswith('polygon'):
            self._process_polygon_element(element)
        
        # 处理circle元素
        elif element.tag.endswith('circle'):
            self._process_circle_element(element)
        
        # 处理ellipse元素
        elif element.tag.endswith('ellipse'):
            self._process_ellipse_element(element)
        
        # 处理rect元素
        elif element.tag.endswith('rect'):
            self._process_rect_element(element)
        
        # 递归处理子元素
        for child in element:
            self._process_svg_elements(child)
    
    def _process_path_element(self, element):
        """处理SVG path元素"""
        d = element.get('d', '')
        if not d:
            return
        
        if SVGPATHTOOLS_AVAILABLE:
            # 使用svgpathtools精确计算
            try:
                path = parse_path(d)
                length = path.length()
                self._add_path_result('path', length, {'d': d})
            except:
                # 降级到基础解析
                length = self._parse_path_basic(d)
                self._add_path_result('path', length, {'d': d})
        else:
            # 基础路径解析
            length = self._parse_path_basic(d)
            self._add_path_result('path', length, {'d': d})
    
    def _parse_path_basic(self, d: str) -> float:
        """基础SVG路径解析（不依赖外部库）"""
        # 简化的路径解析，处理基本的M, L, H, V, Z命令
        commands = re.findall(r'[MmLlHhVvZzCcSsQqTtAa][^MmLlHhVvZzCcSsQqTtAa]*', d)
        
        total_length = 0.0
        current_pos = [0.0, 0.0]
        start_pos = [0.0, 0.0]
        
        for command in commands:
            cmd = command[0]
            params = re.findall(r'-?\d*\.?\d+', command[1:])
            params = [float(p) for p in params]
            
            if cmd.upper() == 'M':  # MoveTo
                if len(params) >= 2:
                    if cmd.isupper():  # 绝对坐标
                        current_pos = [params[0], params[1]]
                    else:  # 相对坐标
                        current_pos[0] += params[0]
                        current_pos[1] += params[1]
                    start_pos = current_pos.copy()
            
            elif cmd.upper() == 'L':  # LineTo
                for i in range(0, len(params), 2):
                    if i + 1 < len(params):
                        if cmd.isupper():
                            target = [params[i], params[i+1]]
                        else:
                            target = [current_pos[0] + params[i], current_pos[1] + params[i+1]]
                        
                        length = math.sqrt((target[0] - current_pos[0])**2 + 
                                         (target[1] - current_pos[1])**2)
                        total_length += length
                        current_pos = target
            
            elif cmd.upper() == 'H':  # 水平线
                for param in params:
                    if cmd.isupper():
                        target_x = param
                    else:
                        target_x = current_pos[0] + param
                    
                    length = abs(target_x - current_pos[0])
                    total_length += length
                    current_pos[0] = target_x
            
            elif cmd.upper() == 'V':  # 垂直线
                for param in params:
                    if cmd.isupper():
                        target_y = param
                    else:
                        target_y = current_pos[1] + param
                    
                    length = abs(target_y - current_pos[1])
                    total_length += length
                    current_pos[1] = target_y
            
            elif cmd.upper() == 'Z':  # ClosePath
                length = math.sqrt((start_pos[0] - current_pos[0])**2 + 
                                 (start_pos[1] - current_pos[1])**2)
                total_length += length
                current_pos = start_pos.copy()
            
            # 简化处理曲线命令（C, S, Q, T, A）
            elif cmd.upper() in ['C', 'S', 'Q', 'T']:
                # 对于曲线，使用控制点估算长度
                if cmd.upper() == 'C' and len(params) >= 6:  # 三次贝塞尔
                    for i in range(0, len(params), 6):
                        if i + 5 < len(params):
                            if cmd.isupper():
                                end_point = [params[i+4], params[i+5]]
                            else:
                                end_point = [current_pos[0] + params[i+4], 
                                           current_pos[1] + params[i+5]]
                            
                            # 简单估算：直线距离 * 1.33（经验值）
                            straight_dist = math.sqrt((end_point[0] - current_pos[0])**2 + 
                                                    (end_point[1] - current_pos[1])**2)
                            total_length += straight_dist * 1.33
                            current_pos = end_point
        
        return total_length
    
    def _process_line_element(self, element):
        """处理SVG line元素"""
        x1 = float(element.get('x1', 0))
        y1 = float(element.get('y1', 0))
        x2 = float(element.get('x2', 0))
        y2 = float(element.get('y2', 0))
        
        length = math.sqrt((x2 - x1)**2 + (y2 - y1)**2)
        self._add_path_result('line', length, {'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2})
    
    def _process_polyline_element(self, element):
        """处理SVG polyline元素"""
        points_str = element.get('points', '')
        points = self._parse_points(points_str)
        
        if len(points) < 2:
            return
        
        total_length = 0.0
        for i in range(1, len(points)):
            length = math.sqrt((points[i][0] - points[i-1][0])**2 + 
                             (points[i][1] - points[i-1][1])**2)
            total_length += length
        
        self._add_path_result('polyline', total_length, {'points': points})
    
    def _process_polygon_element(self, element):
        """处理SVG polygon元素"""
        points_str = element.get('points', '')
        points = self._parse_points(points_str)
        
        if len(points) < 3:
            return
        
        total_length = 0.0
        for i in range(len(points)):
            next_i = (i + 1) % len(points)
            length = math.sqrt((points[next_i][0] - points[i][0])**2 + 
                             (points[next_i][1] - points[i][1])**2)
            total_length += length
        
        self._add_path_result('polygon', total_length, {'points': points})
    
    def _process_circle_element(self, element):
        """处理SVG circle元素"""
        r = float(element.get('r', 0))
        circumference = 2 * math.pi * r
        
        cx = float(element.get('cx', 0))
        cy = float(element.get('cy', 0))
        
        self._add_path_result('circle', circumference, {'cx': cx, 'cy': cy, 'r': r})
    
    def _process_ellipse_element(self, element):
        """处理SVG ellipse元素"""
        rx = float(element.get('rx', 0))
        ry = float(element.get('ry', 0))
        
        # 椭圆周长近似公式 (Ramanujan)
        h = ((rx - ry) / (rx + ry))**2
        circumference = math.pi * (rx + ry) * (1 + (3 * h) / (10 + math.sqrt(4 - 3 * h)))
        
        cx = float(element.get('cx', 0))
        cy = float(element.get('cy', 0))
        
        self._add_path_result('ellipse', circumference, {'cx': cx, 'cy': cy, 'rx': rx, 'ry': ry})
    
    def _process_rect_element(self, element):
        """处理SVG rect元素"""
        width = float(element.get('width', 0))
        height = float(element.get('height', 0))
        
        perimeter = 2 * (width + height)
        
        x = float(element.get('x', 0))
        y = float(element.get('y', 0))
        
        self._add_path_result('rect', perimeter, {'x': x, 'y': y, 'width': width, 'height': height})
    
    def _parse_points(self, points_str: str) -> List[Tuple[float, float]]:
        """解析points属性"""
        points = []
        coords = re.findall(r'-?\d*\.?\d+', points_str)
        
        for i in range(0, len(coords), 2):
            if i + 1 < len(coords):
                points.append((float(coords[i]), float(coords[i+1])))
        
        return points
    
    def _add_path_result(self, element_type: str, length: float, attributes: Dict):
        """添加路径结果"""
        self.results['paths'].append({
            'type': element_type,
            'length': length,
            'attributes': attributes
        })
        self.results['total_length'] += length
        self.results['line_count'] += 1
    
    def analyze_pdf_file(self, pdf_file_path: str) -> Dict:
        """
        分析PDF文件中的线条长度（基础实现）
        注意：PDF解析比SVG复杂得多，这里提供基础框架
        """
        if not PDF_AVAILABLE:
            return {'error': '需要安装PyPDF2: pip install PyPDF2'}
        
        try:
            # 重置结果
            self.results = {
                'total_length': 0.0,
                'line_count': 0,
                'paths': [],
                'statistics': {},
                'note': 'PDF解析功能有限，建议转换为SVG后分析'
            }
            
            with open(pdf_file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    # 提取页面内容
                    if hasattr(page, 'extract_text'):
                        content = page.extract_text()
                        # 这里需要更复杂的PDF内容解析
                        # 实际应用中建议使用专门的PDF解析库如pdfplumber
                        
            return self.results
            
        except Exception as e:
            return {'error': f'解析PDF文件时出错: {str(e)}'}
    
    def _calculate_statistics(self):
        """计算统计信息"""
        if not self.results['paths']:
            return
        
        lengths = [path['length'] for path in self.results['paths']]
        types = [path['type'] for path in self.results['paths']]
        
        self.results['statistics'] = {
            'total_length': self.results['total_length'],
            'average_length': np.mean(lengths),
            'min_length': min(lengths),
            'max_length': max(lengths),
            'std_length': np.std(lengths),
            'element_types': {t: types.count(t) for t in set(types)}
        }
    
    def export_results(self, output_file: str = None, format: str = 'json'):
        """导出分析结果，单位为毫米（mm）"""
        # SVG标准单位通常为pt（1pt=1/72英寸），1英寸=25.4mm
        # 1pt = 25.4/72 mm ≈ 0.3528 mm
        pt_to_mm = 25.4 / 72
        def to_mm(val):
            return val * pt_to_mm
        if format == 'json':
            # 仅转换总长度和每条线长度为mm
            results_mm = json.loads(json.dumps(self.results))
            results_mm['total_length_mm'] = to_mm(self.results['total_length'])
            for i, path in enumerate(results_mm.get('paths', [])):
                path['length_mm'] = to_mm(path['length'])
            if 'statistics' in results_mm:
                stats = results_mm['statistics']
                stats['total_length_mm'] = to_mm(stats.get('total_length', 0))
                stats['average_length_mm'] = to_mm(stats.get('average_length', 0))
                stats['min_length_mm'] = to_mm(stats.get('min_length', 0))
                stats['max_length_mm'] = to_mm(stats.get('max_length', 0))
                stats['std_length_mm'] = to_mm(stats.get('std_length', 0))
            output = json.dumps(results_mm, indent=2, ensure_ascii=False)
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(output)
            return output
        elif format == 'txt':
            lines = [
                "=== SVG/PDF 线条长度分析报告（单位：mm） ===",
                f"总长度: {to_mm(self.results['total_length']):.4f} mm",
                f"线条数量: {self.results['line_count']}",
                ""
            ]
            if 'statistics' in self.results and self.results['statistics']:
                stats = self.results['statistics']
                lines.extend([
                    "统计信息:",
                    f"  平均长度: {to_mm(stats.get('average_length', 0)):.4f} mm",
                    f"  最短长度: {to_mm(stats.get('min_length', 0)):.4f} mm",
                    f"  最长长度: {to_mm(stats.get('max_length', 0)):.4f} mm",
                    f"  标准差: {to_mm(stats.get('std_length', 0)):.4f} mm",
                    ""
                ])
                if 'element_types' in stats:
                    lines.append("元素类型分布:")
                    for elem_type, count in stats['element_types'].items():
                        lines.append(f"  {elem_type}: {count}")
                    lines.append("")
            lines.append("详细信息:")
            for i, path in enumerate(self.results['paths']):
                lines.append(f"  {i+1}. {path['type']}: {to_mm(path['length']):.4f} mm")
            output = '\n'.join(lines)
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(output)
            return output

# 使用示例和批量处理功能
class BatchAnalyzer:
    """批量文件分析器"""
    
    def __init__(self):
        self.analyzer = SVGPDFLineAnalyzer()
    
    def analyze_directory(self, directory_path: str, file_extensions: List[str] = None):
        """分析目录中的所有SVG/PDF文件"""
        import os
        
        if file_extensions is None:
            file_extensions = ['.svg', '.pdf']
        
        results = {}
        
        for filename in os.listdir(directory_path):
            if any(filename.lower().endswith(ext) for ext in file_extensions):
                file_path = os.path.join(directory_path, filename)
                
                if filename.lower().endswith('.svg'):
                    result = self.analyzer.analyze_svg_file(file_path)
                elif filename.lower().endswith('.pdf'):
                    result = self.analyzer.analyze_pdf_file(file_path)
                else:
                    continue
                
                results[filename] = result
        
        return results
    
    def generate_comparison_report(self, results: Dict) -> str:
        """生成比较报告"""
        lines = [
            "=== 批量文件分析比较报告 ===",
            ""
        ]
        
        total_files = len(results)
        total_length = sum(r.get('total_length', 0) for r in results.values() if 'error' not in r)
        
        lines.extend([
            f"分析文件总数: {total_files}",
            f"总线条长度: {total_length:.4f}",
            ""
        ])
        
        # 按长度排序
        sorted_results = sorted(
            [(name, result) for name, result in results.items() if 'error' not in result],
            key=lambda x: x[1].get('total_length', 0),
            reverse=True
        )
        
        lines.append("文件长度排序:")
        for i, (filename, result) in enumerate(sorted_results, 1):
            lines.append(f"  {i}. {filename}: {result.get('total_length', 0):.4f}")
        
        return '\n'.join(lines)

# 示例使用
def demo_usage():
    """演示使用方法"""
    
    # 指定要测量的SVG文件路径
    svg_file = '/Users/<USER>/Documents/code-project/pack相关/算法/线长计算/sample.svg'  # 用户指定的SVG文件路径
    analyzer = SVGPDFLineAnalyzer()
    results = analyzer.analyze_svg_file(svg_file)
    print(f"=== {svg_file} 文件分析结果 ===")
    print(json.dumps(results, indent=2, ensure_ascii=False))
    report = analyzer.export_results(format='txt')
    print("\n" + report)

if __name__ == "__main__":
    demo_usage()