#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速开始脚本
自动安装依赖并运行示例
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 6):
        print("错误：需要Python 3.6或更高版本")
        return False
    print(f"✓ Python版本：{version.major}.{version.minor}.{version.micro}")
    return True

def install_dependencies():
    """安装依赖"""
    print("\n" + "="*50)
    print("安装依赖...")
    print("="*50)
    
    # 使用国内镜像源
    pip_args = [
        sys.executable, "-m", "pip", "install",
        "-i", "https://pypi.tuna.tsinghua.edu.cn/simple",
        "-r", "requirements.txt"
    ]
    
    try:
        subprocess.run(pip_args, check=True)
        print("✓ 依赖安装成功")
        return True
    except subprocess.CalledProcessError:
        print("✗ 依赖安装失败")
        print("请手动运行：pip install -r requirements.txt")
        return False

def check_dependencies():
    """检查依赖是否已安装"""
    print("\n" + "="*50)
    print("检查依赖...")
    print("="*50)
    
    dependencies = {
        "PyMuPDF": "fitz",
        "pdfplumber": "pdfplumber",
        "PyPDF2": "PyPDF2"
    }
    
    all_installed = True
    for name, module in dependencies.items():
        try:
            __import__(module)
            print(f"✓ {name} 已安装")
        except ImportError:
            print(f"✗ {name} 未安装")
            all_installed = False
    
    return all_installed

def create_sample_pdf():
    """创建示例PDF文件"""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        sample_path = "sample_lines.pdf"
        c = canvas.Canvas(sample_path, pagesize=letter)
        width, height = letter
        
        # 绘制一些线条
        c.setStrokeColorRGB(0, 0, 0)
        c.setLineWidth(1)
        
        # 水平线
        c.line(100, 700, 300, 700)
        
        # 垂直线
        c.line(100, 700, 100, 500)
        
        # 对角线
        c.line(100, 500, 300, 700)
        
        # 矩形
        c.rect(400, 600, 100, 50)
        
        # 第二页
        c.showPage()
        c.line(50, 50, 550, 750)
        
        c.save()
        print(f"✓ 示例PDF已创建：{sample_path}")
        return sample_path
        
    except ImportError:
        print("✗ 需要安装reportlab来创建示例")
        return None

def run_demo():
    """运行演示"""
    print("\n" + "="*50)
    print("运行演示...")
    print("="*50)
    
    # 检查是否有PDF文件
    pdf_files = [f for f in os.listdir(".") if f.lower().endswith('.pdf')]
    
    if not pdf_files:
        print("未找到PDF文件，创建示例...")
        sample_pdf = create_sample_pdf()
        if sample_pdf:
            pdf_files = [sample_pdf]
    
    if pdf_files:
        print(f"找到 {len(pdf_files)} 个PDF文件")
        
        # 使用第一个PDF文件进行演示
        test_file = pdf_files[0]
        print(f"\n分析文件：{test_file}")
        
        try:
            from simple_pdf_calculator import SimplePDFCalculator
            calculator = SimplePDFCalculator(test_file)
            if calculator.analyze():
                calculator.print_summary()
                report_path = calculator.save_report()
                print(f"\n详细报告已保存到：{report_path}")
            else:
                print("分析失败")
        except Exception as e:
            print(f"分析时出错：{e}")
    else:
        print("没有可用的PDF文件进行演示")

def main():
    """主函数"""
    print("="*60)
    print("    PDF线条长度计算器 - 快速开始")
    print("="*60)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 检查依赖
    if not check_dependencies():
        print("\n正在安装缺失的依赖...")
        if not install_dependencies():
            print("请手动安装依赖后重试")
            return
    
    # 运行演示
    run_demo()
    
    print("\n" + "="*60)
    print("快速开始完成！")
    print("="*60)
    print("使用方法：")
    print("1. 交互式模式：python interactive_calculator.py")
    print("2. 命令行模式：python simple_pdf_calculator.py your_file.pdf")
    print("3. 高级分析：python pdf_line_analyzer.py")
    print("="*60)

if __name__ == "__main__":
    main()
