import math

def calculate_line_length(start_point, end_point):
    """
    计算两点之间的直线距离（欧几里得距离）
    
    参数:
    start_point: tuple/list - 起点坐标 (x1, y1)
    end_point: tuple/list - 终点坐标 (x2, y2)
    
    返回:
    float - 两点之间的直线距离
    """
    if len(start_point) != 2 or len(end_point) != 2:
        return "坐标点必须是二维坐标 (x, y)"
    
    x1, y1 = start_point
    x2, y2 = end_point
    
    if not all(isinstance(coord, (int, float)) for coord in [x1, y1, x2, y2]):
        return "所有坐标必须是数字"
    
    distance = math.sqrt((x2 - x1)**2 + (y2 - y1)**2)
    return distance

def calculate_polyline_length(points):
    """
    计算折线的总长度
    
    参数:
    points: list - 包含多个点的列表，每个点是 (x, y) 格式的元组
    
    返回:
    float - 折线的总长度
    """
    if len(points) < 2:
        return "至少需要2个点来计算折线长度"
    
    total_length = 0
    for i in range(len(points) - 1):
        segment_length = calculate_line_length(points[i], points[i + 1])
        if isinstance(segment_length, str):  # 错误信息
            return segment_length
        total_length += segment_length
    
    return total_length

def calculate_rectangle_perimeter(length, width):
    """
    计算矩形的周长
    
    参数:
    length: float - 矩形长度
    width: float - 矩形宽度
    
    返回:
    float - 矩形周长
    """
    if not all(isinstance(dim, (int, float)) and dim > 0 for dim in [length, width]):
        return "长度和宽度必须是正数"
    
    return 2 * (length + width)

def calculate_circle_circumference(radius):
    """
    计算圆的周长
    
    参数:
    radius: float - 圆半径
    
    返回:
    float - 圆周长
    """
    if not isinstance(radius, (int, float)) or radius <= 0:
        return "半径必须是正数"
    
    return 2 * math.pi * radius

def calculate_arc_length(radius, angle_degrees):
    """
    计算圆弧长度
    
    参数:
    radius: float - 圆半径
    angle_degrees: float - 圆心角（度）
    
    返回:
    float - 圆弧长度
    """
    if not isinstance(radius, (int, float)) or radius <= 0:
        return "半径必须是正数"
    
    if not isinstance(angle_degrees, (int, float)) or angle_degrees < 0 or angle_degrees > 360:
        return "角度必须在0-360度之间"
    
    angle_radians = math.radians(angle_degrees)
    return radius * angle_radians

# 包装印刷相关的线长计算函数
def calculate_cutting_line_length(box_length, box_width, box_height, material_thickness=3):
    """
    计算包装盒的切割线总长度
    
    参数:
    box_length: float - 盒长 (mm)
    box_width: float - 盒宽 (mm)
    box_height: float - 盒高 (mm)
    material_thickness: float - 材料厚度 (mm)，默认为3mm
    
    返回:
    dict - 包含各种切割线长度的字典
    """
    if not all(isinstance(dim, (int, float)) and dim > 0 for dim in [box_length, box_width, box_height, material_thickness]):
        return "所有尺寸参数必须是正数"
    
    # 基础切割线长度
    outer_perimeter = 2 * (box_length + box_width)  # 外框周长
    
    # 内折线长度（考虑材料厚度）
    inner_length = box_length - 2 * material_thickness
    inner_width = box_width - 2 * material_thickness
    
    # 高度方向的切割线
    height_lines = 4 * box_height  # 四条垂直边
    
    # 折痕线长度
    fold_lines = 2 * (inner_length + inner_width)
    
    total_cutting_length = outer_perimeter + height_lines + fold_lines
    
    return {
        "外框周长": outer_perimeter,
        "高度切割线": height_lines,
        "折痕线": fold_lines,
        "总切割线长度": total_cutting_length
    }

# 测试和示例
if __name__ == "__main__":
    print("=== 线长计算工具测试 ===\n")
    
    # 测试1：两点间直线距离
    point1 = (0, 0)
    point2 = (3, 4)
    distance = calculate_line_length(point1, point2)
    print(f"1. 两点({point1})到({point2})的直线距离: {distance} 单位")
    
    # 测试2：折线长度
    points = [(0, 0), (3, 0), (3, 4), (6, 4)]
    polyline_len = calculate_polyline_length(points)
    print(f"2. 折线长度: {polyline_len} 单位")
    
    # 测试3：矩形周长
    rect_perimeter = calculate_rectangle_perimeter(100, 50)
    print(f"3. 矩形(100×50)的周长: {rect_perimeter} mm")
    
    # 测试4：圆周长
    circle_circ = calculate_circle_circumference(25)
    print(f"4. 圆(半径25)的周长: {circle_circ:.2f} mm")
    
    # 测试5：圆弧长度
    arc_len = calculate_arc_length(50, 90)
    print(f"5. 圆弧(半径50，角度90°)的长度: {arc_len:.2f} mm")
    
    # 测试6：包装盒切割线
    box_info = calculate_cutting_line_length(200, 150, 80, 3)
    print("\n6. 包装盒(200×150×80mm)切割线计算:")
    for key, value in box_info.items():
        print(f"   {key}: {value} mm")
    
    # 测试7：错误处理
    print("\n7. 错误处理测试:")
    print("   无效坐标:", calculate_line_length((1, 2, 3), (4, 5)))
    print("   负数尺寸:", calculate_rectangle_perimeter(-10, 20))
    print("   无效角度:", calculate_arc_length(10, 400))
