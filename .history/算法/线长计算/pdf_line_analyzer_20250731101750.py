#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF线条长度分析器
用于从PDF文件中提取和分析线条，计算其长度
"""

import fitz  # PyMuPDF
import math
import json
from typing import List, Tuple, Dict, Any
import os

class PDFLineAnalyzer:
    """PDF线条分析器类"""
    
    def __init__(self, pdf_path: str):
        """初始化PDF分析器"""
        self.pdf_path = pdf_path
        self.doc = None
        self.lines = []
        self.total_length = 0.0
        
    def load_pdf(self) -> bool:
        """加载PDF文件"""
        try:
            if not os.path.exists(self.pdf_path):
                print(f"错误：文件 {self.pdf_path} 不存在")
                return False
                
            self.doc = fitz.open(self.pdf_path)
            print(f"成功加载PDF文件：{self.pdf_path}")
            print(f"页数：{len(self.doc)}")
            return True
        except Exception as e:
            print(f"加载PDF文件时出错：{e}")
            return False
    
    def extract_lines(self) -> List[Dict[str, Any]]:
        """从PDF中提取所有线条"""
        if not self.doc:
            print("请先加载PDF文件")
            return []
        
        all_lines = []
        
        for page_num in range(len(self.doc)):
            try:
                page = self.doc[page_num]
                
                # 获取页面尺寸
                rect = page.rect
                print(f"第{page_num + 1}页尺寸：{rect.width} x {rect.height}")
                
                # 获取矢量图形
                drawings = page.get_drawings()
                
                page_lines = []
                
                for drawing in drawings:
                    # 处理路径
                    for item in drawing.get("items", []):
                        if item[0] == "l":  # 直线
                            start_point = (item[1].x, item[1].y)
                            end_point = (item[2].x, item[2].y)
                            length = self.calculate_distance(start_point, end_point)
                            
                            line_info = {
                                "page": page_num + 1,
                                "type": "line",
                                "start": start_point,
                                "end": end_point,
                                "length": length,
                                "color": drawing.get("color", None),
                                "stroke_width": drawing.get("width", 1)
                            }
                            page_lines.append(line_info)
                            
                        elif item[0] == "c":  # 曲线
                            # 对于曲线，我们可以近似计算其长度
                            points = [item[1], item[2], item[3], item[4]]
                            curve_length = self.approximate_curve_length(points)
                            
                            curve_info = {
                                "page": page_num + 1,
                                "type": "curve",
                                "points": [(p.x, p.y) for p in points],
                                "length": curve_length,
                                "color": drawing.get("color", None),
                                "stroke_width": drawing.get("width", 1)
                            }
                            page_lines.append(curve_info)
                            
                        elif item[0] == "re":  # 矩形
                            rect_coords = item[1]
                            rect_lines = self.rectangle_to_lines(rect_coords)
                            for line in rect_lines:
                                line.update({
                                    "page": page_num + 1,
                                    "type": "rectangle_line",
                                    "color": drawing.get("color", None),
                                    "stroke_width": drawing.get("width", 1)
                                })
                                page_lines.extend(rect_lines)
                
                all_lines.extend(page_lines)
                
            except Exception as e:
                print(f"处理第{page_num + 1}页时出错：{e}")
                continue
        
        self.lines = all_lines
        self.total_length = sum(line["length"] for line in all_lines)
        
        return all_lines
    
    def calculate_distance(self, point1: Tuple[float, float], point2: Tuple[float, float]) -> float:
        """计算两点之间的距离"""
        x1, y1 = point1
        x2, y2 = point2
        return math.sqrt((x2 - x1)**2 + (y2 - y1)**2)
    
    def approximate_curve_length(self, points: List[Any]) -> float:
        """近似计算曲线长度（使用线段近似）"""
        if len(points) < 2:
            return 0.0
        
        total_length = 0.0
        for i in range(len(points) - 1):
            total_length += self.calculate_distance(
                (points[i].x, points[i].y),
                (points[i+1].x, points[i+1].y)
            )
        
        return total_length
    
    def rectangle_to_lines(self, rect) -> List[Dict[str, Any]]:
        """将矩形分解为四条边"""
        x0, y0, x1, y1 = rect.x0, rect.y0, rect.x1, rect.y1
        
        lines = [
            {"start": (x0, y0), "end": (x1, y0), "length": abs(x1 - x0)},
            {"start": (x1, y0), "end": (x1, y1), "length": abs(y1 - y0)},
            {"start": (x1, y1), "end": (x0, y1), "length": abs(x1 - x0)},
            {"start": (x0, y1), "end": (x0, y0), "length": abs(y1 - y0)}
        ]
        
        return lines
    
    def get_summary(self) -> Dict[str, Any]:
        """获取线条分析摘要"""
        if not self.lines:
            return {"error": "未找到线条数据"}
        
        line_count = len([l for l in self.lines if l["type"] == "line"])
        curve_count = len([l for l in self.lines if l["type"] == "curve"])
        rect_count = len([l for l in self.lines if l["type"] == "rectangle_line"])
        
        # 按页面分组
        pages = {}
        for line in self.lines:
            page = line["page"]
            if page not in pages:
                pages[page] = {"lines": [], "total_length": 0.0}
            pages[page]["lines"].append(line)
            pages[page]["total_length"] += line["length"]
        
        return {
            "总线条数": len(self.lines),
            "直线数": line_count,
            "曲线数": curve_count,
            "矩形边数": rect_count,
            "总长度": self.total_length,
            "按页面统计": pages
        }
    
    def export_results(self, output_path: str = None) -> str:
        """导出分析结果"""
        if not output_path:
            output_path = self.pdf_path.replace(".pdf", "_analysis.json")
        
        summary = self.get_summary()
        
        # 添加详细线条信息
        full_results = {
            "summary": summary,
            "lines": self.lines,
            "file_path": self.pdf_path
        }
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(full_results, f, ensure_ascii=False, indent=2)
            print(f"分析结果已导出到：{output_path}")
            return output_path
        except Exception as e:
            print(f"导出结果时出错：{e}")
            return None
    
    def print_detailed_report(self):
        """打印详细报告"""
        if not self.lines:
            print("未找到任何线条")
            return
        
        summary = self.get_summary()
        
        print("\n" + "="*50)
        print("PDF线条长度分析报告")
        print("="*50)
        
        print(f"文件路径：{self.pdf_path}")
        print(f"总线条数：{summary['总线条数']}")
        print(f"直线数：{summary['直线数']}")
        print(f"曲线数：{summary['曲线数']}")
        print(f"矩形边数：{summary['矩形边数']}")
        print(f"总长度：{summary['总长度']:.2f} 单位")
        
        print("\n按页面统计：")
        for page, data in summary["按页面统计"].items():
            print(f"  第{page}页：{len(data['lines'])}条线，总长度{data['total_length']:.2f} 单位")
        
        print("\n详细线条信息：")
        for i, line in enumerate(self.lines[:10], 1):  # 只显示前10条
            print(f"\n{i}. 第{line['page']}页 - {line['type']}")
            if line["type"] == "line":
                print(f"   起点：{line['start']}")
                print(f"   终点：{line['end']}")
            elif line["type"] == "curve":
                print(f"   控制点：{line['points']}")
            print(f"   长度：{line['length']:.2f} 单位")
            print(f"   线宽：{line.get('stroke_width', 1)}")
        
        if len(self.lines) > 10:
            print(f"\n... 还有 {len(self.lines) - 10} 条线未显示")
    
    def close(self):
        """关闭PDF文档"""
        if self.doc:
            self.doc.close()

def main():
    """主函数"""
    pdf_path = "算法/线长计算/pdf-line.pdf"
    
    # 创建分析器实例
    analyzer = PDFLineAnalyzer(pdf_path)
    
    try:
        # 加载PDF
        if not analyzer.load_pdf():
            return
        
        # 提取线条
        lines = analyzer.extract_lines()
        print(f"成功提取 {len(lines)} 条线")
        
        # 打印详细报告
        analyzer.print_detailed_report()
        
        # 导出结果
        analyzer.export_results()
        
    except Exception as e:
        print(f"分析过程中出错：{e}")
    finally:
        analyzer.close()

if __name__ == "__main__":
    # 检查依赖
    try:
        import fitz
    except ImportError:
        print("请先安装PyMuPDF：pip install PyMuPDF")
        exit(1)
    
    main()
