[project]
name = "pdf-line-length-calculator"
version = "0.1.0"
description = "A modern PDF line length calculator using uv package management"
authors = [
    {name = "PDF Line Calculator", email = "<EMAIL>"}
]
dependencies = [
    "pymupdf>=1.23.0",
    "pdfplumber>=0.10.0",
    "pypdf2>=3.0.0",
    "pillow>=10.0.0",
    "click>=8.1.0",
    "rich>=13.0.0",
    "pydantic>=2.0.0",
    "matplotlib>=3.7.0",
    "numpy>=1.24.0",
]
requires-python = ">=3.8"
readme = "README.md"
license = {text = "MIT"}

[project.scripts]
pdf-lines = "pdf_line_calculator.cli:main"
pdf-analyze = "pdf_line_calculator.analyzer:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
    "mypy>=1.0.0",
]
