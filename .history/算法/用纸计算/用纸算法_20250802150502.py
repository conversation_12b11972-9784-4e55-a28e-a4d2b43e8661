import math

def calculate_spoilage(order_quantity, spoilage_rules):
    """根据订单数量和损耗规则计算损耗数量。"""
    if order_quantity < 0:
        return "订单数量不能为负数。"

    if not all(key in spoilage_rules for key in ("base_quantity", "base_spoilage", "increment_quantity", "increment_spoilage")):
        return "损耗规则不完整。"

    base_quantity = spoilage_rules["base_quantity"]
    base_spoilage = spoilage_rules["base_spoilage"]
    increment_quantity = spoilage_rules["increment_quantity"]
    increment_spoilage = spoilage_rules["increment_spoilage"]

    if order_quantity <= base_quantity:
        spoilage = base_spoilage
    else:
        spoilage = base_spoilage + math.ceil((order_quantity - base_quantity) / increment_quantity) * increment_spoilage

    return spoilage

def calculate_layout_and_paper_cost(equipment_length, equipment_width, product_length, product_width, bleed, order_quantity, unit_price_per_square_meter, spoilage_rules, margin):
    """
    计算排版、所需纸张尺寸和用纸成本，包含可自定义的损耗计算、纸张单价和余量。
    """
    if not all(isinstance(x, (int, float)) and x > 0 for x in [equipment_length, equipment_width, product_length, product_width, bleed, order_quantity, unit_price_per_square_meter, margin]):
        return "所有尺寸、数量、单价和余量参数必须为正数。"

    product_length += 2 * bleed
    product_width += 2 * bleed

    # 确定产品方向，保证长边大于等于短边
    if product_length < product_width:
        product_length, product_width = product_width, product_length

    # 计算水平和垂直方向的排布数量
    num_horizontal = math.floor(equipment_length / product_length) * math.floor(equipment_width / product_width)
    num_vertical = math.floor(equipment_length / product_width) * math.floor(equipment_width / product_length)

    # 选择最佳排布方式并计算纸张尺寸
    if num_horizontal >= num_vertical:  # 修正：使用 >= 确保相等时优先水平排布
        max_num_per_sheet = num_horizontal
        paper_length = math.floor(equipment_length / product_length) * product_length  # 修正：使用计算出的排布数量 * 产品尺寸
        paper_width = math.floor(equipment_width / product_width) * product_width
    else:
        max_num_per_sheet = num_vertical
        paper_length = math.floor(equipment_length / product_width) * product_width  # 修正：使用计算出的排布数量 * 产品尺寸
        paper_width = math.floor(equipment_width / product_length) * product_length
    
    # 增加自定义余量
    paper_length += 2 * margin
    paper_width += 2 * margin

    required_sheets = math.ceil(order_quantity / max_num_per_sheet)
    spoilage_sheets = calculate_spoilage(order_quantity, spoilage_rules)

    if isinstance(spoilage_sheets, str): # 如果 spoilage_sheets 是字符串，说明有错误信息，直接返回
        return spoilage_sheets

    total_sheets = required_sheets + spoilage_sheets

    paper_area_square_meter = (paper_length * paper_width) / (1000 * 1000)
    total_area_square_meter = paper_area_square_meter * total_sheets
    total_cost = total_area_square_meter * unit_price_per_square_meter

    result_string = f"每张纸最多排布: {max_num_per_sheet} 个成品\n"
    result_string += f"所需纸张尺寸(含{margin}mm余量): {paper_length} x {paper_width} mm\n"
    result_string += f"需要纸张数量：{required_sheets} 张\n"
    result_string += f"损耗纸张数量：{spoilage_sheets} 张\n"
    result_string += f"总共需要纸张数量：{total_sheets} 张\n"
    result_string += f"纸张总面积：{total_area_square_meter:.3f} 平方米\n"
    result_string += f"纸张总成本：{total_cost:.2f} 元"

    return result_string

# 定义损耗规则
spoilage_rules = {
    "base_quantity": 10000,
    "base_spoilage": 300,
    "increment_quantity": 1000,
    "increment_spoilage": 15
}

# 设置余量（单位：毫米）
# 余量是在计算出的纸张尺寸基础上额外增加的边距，用于确保印刷时有足够的操作空间
margin = 10

# 示例参数设置
# 设备参数（单位：毫米）
equipment_length = 1000    # 印刷设备的最大长度（纸张可印刷的最大长度）
equipment_width = 700      # 印刷设备的最大宽度（纸张可印刷的最大宽度）

# 产品参数（单位：毫米）
product_length = 456       # 单个成品的长度（不含出血位）
product_width = 290        # 单个成品的宽度（不含出血位）
bleed = 3                  # 出血位大小，印刷时为了避免白边而在成品尺寸外增加的额外区域

# 订单参数
order_quantity = 1000      # 订单数量，需要生产的成品总数

# 成本参数（单位：元/平方米）
unit_price_per_square_meter = 1.75  # 纸张单价，每平方米纸张的价格

result = calculate_layout_and_paper_cost(equipment_length, equipment_width, product_length, product_width, bleed, order_quantity, unit_price_per_square_meter, spoilage_rules, margin)
print(result)

# 测试不同的损耗规则和余量
# spoilage_rules_2 = {
#     "base_quantity": 5000,
#     "base_spoilage": 150,
#     "increment_quantity": 500,
#     "increment_spoilage": 10
# }
# margin_2 = 30

# result_2 = calculate_layout_and_paper_cost(equipment_length, equipment_width, product_length, product_width, bleed, order_quantity, unit_price_per_square_meter, spoilage_rules_2, margin_2)
# print("\n使用不同的损耗规则和余量：")
# print(result_2)

# # 测试输入参数错误的情况
# result_error = calculate_layout_and_paper_cost(-100, 700, 456, 290, 3, 1000, 1.75, spoilage_rules, margin)
# print("\n测试输入参数错误的情况：")
# print(result_error)

# result_error_spoilage = calculate_spoilage(-100, spoilage_rules)
# print("\n测试损耗规则输入参数错误的情况：")
# print(result_error_spoilage)

# spoilage_rules_error = {
#     "base_quantity": 10000,
#     "base_spoilage": 300,
#     "increment_quantity": 1000
# }
# result_error_spoilage_rules = calculate_spoilage(1000, spoilage_rules_error)
# print("\n测试损耗规则字典不完整的情况：")
# print(result_error_spoilage_rules)