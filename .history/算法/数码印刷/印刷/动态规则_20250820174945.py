import math

def solve_layout_with_dp(sheet_w, sheet_h, item_w, item_h):
    """
    使用动态规划（Dynamic Programming）解决铡刀式裁切问题。
    该函数保证找到在紧密贴合（无间距）情况下的理论最优解。

    :param sheet_w: 纸张宽度 (mm)
    :param sheet_h: 纸张高度 (mm)
    :param item_w: （含出血）产品宽度 (mm)
    :param item_h: （含出血）产品高度 (mm)
    :return: 理论上最大可容纳的产品数量
    """
    
    # 初始化一个二维数组（DP表）来存储所有子问题的最优解。
    # dp[w][h] 将代表在 w x h 尺寸的纸片上能容纳的最大产品数。
    dp = [[0] * (sheet_h + 1) for _ in range(sheet_w + 1)]

    # 定义产品旋转前后的尺寸，方便使用
    item_normal = (item_w, item_h)
    item_rotated = (item_h, item_w)

    # --- 核心逻辑：自底向上地填充DP表 ---
    # 从最小的 1x1 尺寸开始，逐步计算到 sheet_w x sheet_h
    for w in range(1, sheet_w + 1):
        for h in range(1, sheet_h + 1):
            
            # 1. 基准解：计算当前 w x h 尺寸下，不进行任何切割，直接用单一方向平铺能放下的数量
            max_items = 0
            # 不旋转
            if w >= item_normal[0] and h >= item_normal[1]:
                max_items = max(max_items, math.floor(w / item_normal[0]) * math.floor(h / item_normal[1]))
            # 旋转
            if w >= item_rotated[0] and h >= item_rotated[1]:
                max_items = max(max_items, math.floor(w / item_rotated[0]) * math.floor(h / item_rotated[1]))
            
            dp[w][h] = max_items

            # 2. 递推：尝试所有可能的“最后一刀”切割方式
            # 尝试所有可能的垂直切割
            # 将 w x h 的区域看作是由两个更小的区域 j x h 和 (w-j) x h 组合而成
            for j in range(1, w // 2 + 1):
                dp[w][h] = max(dp[w][h], dp[j][h] + dp[w - j][h])
            
            # 尝试所有可能的水平切割
            # 将 w x h 的区域看作是由两个更小的区域 w x i 和 w x (h-i) 组合而成
            for i in range(1, h // 2 + 1):
                dp[w][h] = max(dp[w][h], dp[w][i] + dp[w][h - i])
                
    # 所有子问题都已解决，最终右下角的值即为原始问题的最优解
    return dp[sheet_w][sheet_h]

# --- 执行计算 ---
if __name__ == "__main__":
    # --- 用户输入区域 ---
    sheet_length = 740      # 纸张长度 (mm)
    sheet_width = 510       # 纸张宽度 (mm)
    product_length = 200    # 产品原始长度 (mm)
    product_width = 300     # 产品原始宽度 (mm)
    bleed_value = 2         # 出血值 (mm)
    # --- 参数修改结束 ---

    # 根据出血值，计算出用于排版的实际产品尺寸
    item_length_bleed = product_length + (2 * bleed_value)
    item_width_bleed = product_width + (2 * bleed_value)

    print("--- 动态规划铡刀裁切算法分析 ---")
    print(f"纸张尺寸: {sheet_length}mm x {sheet_width}mm")
    print(f"含出血排版尺寸: {item_length_bleed}mm x {item_width_bleed}mm")
    print("正在计算理论最优解（紧密贴合，无间距）...")
    print("-" * 35)

    # 调用动态规划函数进行计算
    theoretical_max_count = solve_layout_with_dp(sheet_length, sheet_width, item_length_bleed, item_width_bleed)

    # 打印最终结果
    print(f"动态规划计算出的理论最大拼版数是: {theoretical_max_count}")
    print("-" * 35)