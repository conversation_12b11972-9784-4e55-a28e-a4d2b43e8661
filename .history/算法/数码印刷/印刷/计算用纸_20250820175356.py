import math

# --- 核心算法1: 动态规划铡刀裁切验证器 ---
# 职责：计算在无间距紧密贴合下的理论最大拼版数。
memo_dp = {}
def _solve_dp_tight_pack(sheet_w, sheet_h, item_w, item_h):
    """内部DP函数，用于寻找理论最优解以供验证。"""
    global memo_dp
    if sheet_w < sheet_h: [sheet_w, sheet_h] = [sheet_h, sheet_w]
    key = (sheet_w, sheet_h, item_w, item_h) # Key应包含item尺寸
    if key in memo_dp: return memo_dp[key]

    max_items = 0
    item_normal = (item_w, item_h)
    item_rotated = (item_h, item_w)

    if sheet_w >= item_normal[0] and sheet_h >= item_normal[1]:
        max_items = max(max_items, math.floor(sheet_w / item_normal[0]) * math.floor(sheet_h / item_normal[1]))
    if sheet_w >= item_rotated[0] and sheet_h >= item_rotated[1]:
        max_items = max(max_items, math.floor(sheet_w / item_rotated[0]) * math.floor(sheet_h / item_rotated[1]))

    for j in range(1, sheet_w // 2 + 1):
        max_items = max(max_items, _solve_dp_tight_pack(j, sheet_h, item_w, item_h) + _solve_dp_tight_pack(sheet_w - j, sheet_h, item_w, item_h))
    for i in range(1, sheet_h // 2 + 1):
        max_items = max(max_items, _solve_dp_tight_pack(sheet_w, i, item_w, item_h) + _solve_dp_tight_pack(sheet_w, sheet_h - i, item_w, item_h))

    memo_dp[key] = max_items
    return max_items

# --- 核心算法2: 完整排版方案分析器 ---
def analyze_and_find_minimum_size(sheet_w, sheet_h, item_w, item_h, bleed=0, gutter=0):
    """
    对排版方案进行全面分析，计算最小纸张需求，并使用DP算法进行验证。
    """
    global memo_dp
    memo_dp = {}

    # 步骤1：计算含出血的产品尺寸
    item_w_bleed = item_w + 2 * bleed
    item_h_bleed = item_h + 2 * bleed

    # 步骤2：分析两种主要的“实用网格布局”
    # 方案A: 常规网格 (不旋转)
    cols_a = math.floor((sheet_w + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    rows_a = math.floor((sheet_h + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    count_a = cols_a * rows_a
    layout_a = {
        "type": "常规网格 (Normal Grid)",
        "count": count_a,
        "min_width": cols_a * item_w_bleed + max(0, cols_a - 1) * gutter,
        "min_height": rows_a * item_h_bleed + max(0, rows_a - 1) * gutter
    }

    # 方案B: 旋转网格
    cols_b = math.floor((sheet_w + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    rows_b = math.floor((sheet_h + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    count_b = cols_b * rows_b
    layout_b = {
        "type": "旋转网格 (Rotated Grid)",
        "count": count_b,
        "min_width": cols_b * item_h_bleed + max(0, cols_b - 1) * gutter,
        "min_height": rows_b * item_w_bleed + max(0, rows_b - 1) * gutter
    }

    # 步骤3：确定最佳实用方案
    best_practical_layout = layout_a if layout_a["count"] >= layout_b["count"] else layout_b

    # 步骤4：调用DP算法进行理论验证
    theoretical_max_count = _solve_dp_tight_pack(sheet_w, sheet_h, item_w_bleed, item_h_bleed)

    # 步骤5：生成最终分析报告
    item_net_area = item_w * item_h
    efficiency = (best_practical_layout["count"] * item_net_area / (sheet_w * sheet_h)) * 100
    
    analysis_summary = f"最佳实用方案可拼 {best_practical_layout['count']} 个。经DP算法验证，理论最大拼版数是 {theoretical_max_count} 个。"
    if best_practical_layout['count'] == theoretical_max_count:
        analysis_summary += " 结论：当前实用方案已达到理论最优，是完美的生产策略。"
    else:
        analysis_summary += f" 提示：存在能拼下更多 ({theoretical_max_count} 个) 的紧密贴合布局，但无法满足间距要求。"

    result = {
        "最优合拼版数 (实用方案)": best_practical_layout["count"],
        "布局类型": best_practical_layout["type"],
        "最小纸张尺寸 (拼版后实际尺寸)": f"{best_practical_layout['min_width']}mm x {best_practical_layout['min_height']}mm",
        "理论最大拼版数 (DP验证)": theoretical_max_count,
        "纸张利用率 (基于产品净尺寸)": f"{efficiency:.2f}%",
        "分析概要": analysis_summary,
        "输入参数": {
            "纸张尺寸": f"{sheet_w}mm x {sheet_h}mm",
            "产品原始尺寸": f"{item_w}mm x {item_h}mm",
            "出血": f"{bleed}mm",
            "间距": f"{gutter}mm",
        }
    }
    return result

# --- 执行计算 ---
if __name__ == "__main__":
    # --- 用户输入区域 ---
    sheet_length = 740
    sheet_width = 510
    product_length = 200
    product_width = 300
    bleed_value = 3
    gutter_value = 5
    # --- 参数修改结束 ---

    # 调用最终的分析函数
    final_report = analyze_and_find_minimum_size(sheet_length, sheet_width, product_length, product_width, bleed_value, gutter_value)

    # 打印格式化的报告
    print("--- 包装排版优化分析报告 (DP验证版) ---")
    for key, value in final_report.items():
        if isinstance(value, dict):
            print(f"\n[ {key} ]")
            for sub_key, sub_value in value.items():
                print(f"  - {sub_key}: {sub_value}")
        else:
            print(f"- {key}: {value}")
    print("---------------------------------------")