import math

# --- 内部核心算法 (保持不变) ---
memo = {}
def _find_max_count_recursive(sheet_w, sheet_h, item_w, item_h):
    # (此部分代码与之前版本完全相同，此处省略以保持简洁)
    global memo
    if sheet_w < sheet_h:
        [sheet_w, sheet_h] = [sheet_h, sheet_w]
    key = (sheet_w, sheet_h)
    if key in memo:
        return memo[key]
    max_items = 0
    if sheet_w >= item_w and sheet_h >= item_h:
        max_items = max(max_items, math.floor(sheet_w / item_w) * math.floor(sheet_h / item_h))
    if sheet_w >= item_h and sheet_h >= item_w:
        max_items = max(max_items, math.floor(sheet_w / item_h) * math.floor(sheet_h / item_w))
    if sheet_w >= item_w and sheet_h >= item_h:
        res1 = _find_max_count_recursive(sheet_w - item_w, sheet_h, item_w, item_h) + _find_max_count_recursive(item_w, sheet_h - item_h, item_w, item_h)
        max_items = max(max_items, 1 + res1)
        res2 = _find_max_count_recursive(sheet_w, sheet_h - item_h, item_w, item_h) + _find_max_count_recursive(sheet_w - item_w, item_h, item_w, item_h)
        max_items = max(max_items, 1 + res2)
    if sheet_w >= item_h and sheet_h >= item_w:
        res3 = _find_max_count_recursive(sheet_w - item_h, sheet_h, item_w, item_h) + _find_max_count_recursive(item_h, sheet_h - item_w, item_w, item_h)
        max_items = max(max_items, 1 + res3)
        res4 = _find_max_count_recursive(sheet_w, sheet_h - item_w, item_w, item_w) + _find_max_count_recursive(sheet_w - item_h, item_w, item_w, item_w)
        max_items = max(max_items, 1 + res4)
    memo[key] = max_items
    return max_items

# --- 优化后的主分析函数 ---
def analyze_layout(sheet_w, sheet_h, item_w, item_h, bleed=0, gutter=0):
    """
    对排版方案进行全面分析，生成包含详细面积使用分析的报告。
    """
    global memo
    memo = {}

    # 步骤 1: 准备计算参数
    item_w_bleed = item_w + 2 * bleed
    item_h_bleed = item_h + 2 * bleed

    # 步骤 2: 分析两种“标准网格”布局方案
    # (此部分逻辑与之前版本相同)
    # 方案A: 不旋转
    cols_a = math.floor((sheet_w + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    rows_a = math.floor((sheet_h + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    count_a = cols_a * rows_a
    layout_a = {
        "type": "常规网格 (Normal Grid)",
        "count": count_a,
        "total_width": cols_a * item_w_bleed + max(0, cols_a - 1) * gutter,
        "total_height": rows_a * item_h_bleed + max(0, rows_a - 1) * gutter
    }
    # 方案B: 旋转
    cols_b = math.floor((sheet_w + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    rows_b = math.floor((sheet_h + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    count_b = cols_b * rows_b
    layout_b = {
        "type": "旋转网格 (Rotated Grid)",
        "count": count_b,
        "total_width": cols_b * item_h_bleed + max(0, cols_b - 1) * gutter,
        "total_height": rows_b * item_w_bleed + max(0, rows_b - 1) * gutter
    }
    
    # 步骤 3: 递归验证
    best_tight_pack_count = _find_max_count_recursive(sheet_w, sheet_h, item_w_bleed, item_h_bleed)

    # 步骤 4: 最终决策
    best_layout = layout_a if layout_a["count"] >= layout_b["count"] else layout_b
    if best_tight_pack_count > best_layout["count"]:
        best_layout = {
            "type": "混合紧密布局 (Mixed Tight-Pack)",
            "count": best_tight_pack_count,
            "total_width": sheet_w, 
            "total_height": sheet_h
        }
    
    # --- 步骤 5: 新增 - 进行详细的面积使用分析 ---
    total_sheet_area = sheet_w * sheet_h
    
    # 这是拼版方案实际占用的矩形区域面积
    imposition_area = best_layout["total_width"] * best_layout["total_height"]
    
    # 这是最终得到的、有价值的成品总面积
    total_net_item_area = best_layout["count"] * item_w * item_h
    
    # 这是所有产品（含出血）占用的总面积
    total_bleed_item_area = best_layout["count"] * item_w_bleed * item_h_bleed
    
    # 过程耗损 - 出血总面积
    total_bleed_area = total_bleed_item_area - total_net_item_area
    
    # 过程耗损 - 间距总面积
    total_gutter_area = imposition_area - total_bleed_item_area
    
    # 最终废料 - 拼版区域之外的边角料
    trim_waste_area = total_sheet_area - imposition_area
    
    # 基于产品净尺寸计算的真实利用率
    efficiency = (total_net_item_area / total_sheet_area) * 100 if total_sheet_area > 0 else 0
    
    # 整理并返回包含所有详细信息的最终报告
    result = {
        "核心指标": {
            "最优合拼版数": best_layout["count"],
            "布局类型": best_layout["type"],
            "拼版后实际尺寸": f"{best_layout['total_width']}mm x {best_layout['total_height']}mm",
            "纸张利用率(基于产品净尺寸)": f"{efficiency:.2f}%",
        },
        "面积使用分析": {
            "总纸张面积": f"{total_sheet_area} mm²",
            "-> 拼版区域面积 (含间距)": f"{imposition_area} mm²",
            "--> 产品净尺寸总面积": f"{total_net_item_area} mm²",
            "--> 过程耗损-出血总面积": f"{total_bleed_area} mm²",
            "--> 过程耗损-间距总面积": f"{total_gutter_area if total_gutter_area >= 0 else 0} mm²",
            "-> 裁切废料总面积": f"{trim_waste_area} mm²"
        },
        "输入参数": {
            "纸张尺寸": f"{sheet_w}mm x {sheet_h}mm",
            "产品原始尺寸": f"{item_w}mm x {item_h}mm",
            "出血值": f"{bleed}mm",
            "间距": f"{gutter}mm",
        }
    }
    return result

# --- 执行计算 ---
if __name__ == "__main__":
    sheet_length, sheet_width = 740, 510
    product_length, product_width = 200, 300
    bleed_value, gutter_value = 3, 5

    analysis_result = analyze_layout(sheet_length, sheet_width, product_length, product_width, bleed_value, gutter_value)

    # 打印最终的分析报告
    print("--- 包装排版优化分析报告 (最终版) ---")
    for section, data in analysis_result.items():
        print(f"\n[ {section} ]")
        for key, value in data.items():
            print(f"  - {key}: {value}")
    print("-----------------------------------")