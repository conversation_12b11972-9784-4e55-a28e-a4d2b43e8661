import math

# --- 核心引擎: 动态规划铡刀裁切算法 ---
# 职责：计算在无间距紧密贴合下的理论最大拼版数。这是一个纯粹的数学求解器。
# ---
memo_dp = {}
def _solve_dp_tight_pack(sheet_w, sheet_h, item_w, item_h):
    """
    内部动态规划函数，通过“自底向上”的方式，系统性地寻找理论最优解。
    这是保证结果精确性的核心。
    """
    global memo_dp
    # 为提高缓存命中率，使用规范化的尺寸作为键
    if sheet_w < sheet_h: [sheet_w, sheet_h] = [sheet_h, sheet_w]
    
    # 将产品尺寸也加入键中，确保不同产品尺寸的缓存互相独立
    key = (sheet_w, sheet_h, item_w, item_h)
    if key in memo_dp: return memo_dp[key]

    # --- 1. 基准解：计算当前 w x h 区域不切割时的最优解 ---
    max_items = 0
    item_normal = (item_w, item_h)
    item_rotated = (item_h, item_w)

    # 不旋转
    if sheet_w >= item_normal[0] and sheet_h >= item_normal[1]:
        max_items = max(max_items, math.floor(sheet_w / item_normal[0]) * math.floor(sheet_h / item_normal[1]))
    # 旋转
    if sheet_w >= item_rotated[0] and sheet_h >= item_rotated[1]:
        max_items = max(max_items, math.floor(sheet_w / item_rotated[0]) * math.floor(sheet_h / item_rotated[1]))

    # --- 2. 递推：尝试所有可能的“最后一刀”，并组合子问题的最优解 ---
    # 垂直切割
    for j in range(1, sheet_w // 2 + 1):
        max_items = max(max_items, 
                        _solve_dp_tight_pack(j, sheet_h, item_w, item_h) + 
                        _solve_dp_tight_pack(sheet_w - j, sheet_h, item_w, item_h))
    # 水平切割
    for i in range(1, sheet_h // 2 + 1):
        max_items = max(max_items, 
                        _solve_dp_tight_pack(sheet_w, i, item_w, item_h) + 
                        _solve_dp_tight_pack(sheet_w, sheet_h - i, item_w, item_h))

    memo_dp[key] = max_items
    return max_items

# --- 主分析函数：结合实用性与理论最优 ---
def analyze_optimal_layout_with_dp_validation(sheet_w, sheet_h, item_w, item_h, bleed=0, gutter=0):
    """
    对排版方案进行全面分析，计算包含间距的实用布局及其最小纸张需求，
    并使用动态规划算法进行理论最优验证。
    """
    global memo_dp
    memo_dp = {} # 每次调用都清空缓存

    # 步骤1：计算包含出血的排版单元尺寸
    item_w_bleed = item_w + 2 * bleed
    item_h_bleed = item_h + 2 * bleed

    # 步骤2：分析两种主要的、可实际生产的“网格布局”
    # 方案A: 常规网格 (不旋转)
    cols_a = math.floor((sheet_w + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    rows_a = math.floor((sheet_h + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    layout_a = {
        "type": "常规网格 (Normal Grid)",
        "count": cols_a * rows_a,
        "min_width": cols_a * item_w_bleed + max(0, cols_a - 1) * gutter,
        "min_height": rows_a * item_h_bleed + max(0, rows_a - 1) * gutter
    }

    # 方案B: 旋转网格
    cols_b = math.floor((sheet_w + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    rows_b = math.floor((sheet_h + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    layout_b = {
        "type": "旋转网格 (Rotated Grid)",
        "count": cols_b * rows_b,
        "min_width": cols_b * item_h_bleed + max(0, cols_b - 1) * gutter,
        "min_height": rows_b * item_w_bleed + max(0, rows_b - 1) * gutter
    }

    # 步骤3：确定最佳的“实用方案”
    best_practical_layout = layout_a if layout_a["count"] >= layout_b["count"] else layout_b

    # 步骤4：调用DP算法进行理论验证
    # 注意：DP求解的是无间距的紧密贴合问题
    theoretical_max_count = _solve_dp_tight_pack(sheet_w, sheet_h, item_w_bleed, item_h_bleed)

    # 步骤5：生成最终的综合分析报告
    efficiency = (best_practical_layout["count"] * (item_w * item_h) / (sheet_w * sheet_h)) * 100
    summary = f"最佳实用方案可拼 {best_practical_layout['count']} 个。经DP算法验证，理论最大拼版数是 {theoretical_max_count} 个。"
    if best_practical_layout['count'] == theoretical_max_count:
        summary += " 结论：当前实用方案已达到理论最优，是完美的生产策略。"
    
    result = {
        "最优合拼版数 (实用方案)": best_practical_layout["count"],
        "布局类型": best_practical_layout["type"],
        "最小纸张尺寸 (拼版后实际尺寸)": f"{best_practical_layout['min_width']}mm x {best_practical_layout['min_height']}mm",
        "理论最大拼版数 (DP验证)": theoretical_max_count,
        "纸张利用率 (基于产品净尺寸)": f"{efficiency:.2f}%",
        "分析概要": summary,
        "输入参数": {
            "纸张尺寸": f"{sheet_w}mm x {sheet_h}mm", "产品原始尺寸": f"{item_w}mm x {item_h}mm",
            "出血": f"{bleed}mm", "间距": f"{gutter}mm",
        }
    }
    return result

# --- 执行计算 ---
if __name__ == "__main__":
    # --- 用户输入区域 ---
    sheet_length = 740
    sheet_width = 510
    product_length = 200
    product_width = 300
    
    print("--- 场景1：考虑出血和间距的实际生产分析 ---")
    final_report_practical = analyze_optimal_layout_with_dp_validation(
        sheet_length, sheet_width, product_length, product_width, bleed=3, gutter=5
    )
    # 打印格式化的报告
    for key, value in final_report_practical.items():
        if isinstance(value, dict): continue
        print(f"- {key}: {value}")

    print("\n" + "="*50 + "\n")

    print("--- 场景2：不考虑出血和间距的理论极限分析 ---")
    final_report_theoretical = analyze_optimal_layout_with_dp_validation(
        sheet_length, sheet_width, product_length, product_width, bleed=0, gutter=0
    )
    for key, value in final_report_theoretical.items():
        if isinstance(value, dict): continue
        print(f"- {key}: {value}")