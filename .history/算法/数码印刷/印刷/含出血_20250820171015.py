import math

# 内部递归函数保持不变，用于探索理论上的最大数量
memo = {}
def _find_max_count_recursive(sheet_w, sheet_h, item_w, item_h):
    global memo
    if sheet_w < sheet_h:
        [sheet_w, sheet_h] = [sheet_h, sheet_w]
    key = (sheet_w, sheet_h)
    if key in memo:
        return memo[key]

    max_items = 0
    if sheet_w >= item_w and sheet_h >= item_h:
        max_items = max(max_items, math.floor(sheet_w / item_w) * math.floor(sheet_h / item_h))
    if sheet_w >= item_h and sheet_h >= item_w:
        max_items = max(max_items, math.floor(sheet_w / item_h) * math.floor(sheet_h / item_w))

    if sheet_w >= item_w and sheet_h >= item_h:
        res1 = _find_max_count_recursive(sheet_w - item_w, sheet_h, item_w, item_h) + _find_max_count_recursive(item_w, sheet_h - item_h, item_w, item_h)
        max_items = max(max_items, 1 + res1)
        res2 = _find_max_count_recursive(sheet_w, sheet_h - item_h, item_w, item_h) + _find_max_count_recursive(sheet_w - item_w, item_h, item_w, item_h)
        max_items = max(max_items, 1 + res2)
    if sheet_w >= item_h and sheet_h >= item_w:
        res3 = _find_max_count_recursive(sheet_w - item_h, sheet_h, item_w, item_h) + _find_max_count_recursive(item_h, sheet_h - item_w, item_w, item_h)
        max_items = max(max_items, 1 + res3)
        res4 = _find_max_count_recursive(sheet_w, sheet_h - item_w, item_w, item_h) + _find_max_count_recursive(sheet_w - item_h, item_w, item_w, item_h)
        max_items = max(max_items, 1 + res4)

    memo[key] = max_items
    return max_items

# --- 优化后的主分析函数 ---
def analyze_layout(sheet_w, sheet_h, item_w, item_h, bleed=0, gutter=0):
    """
    对排版方案进行全面分析，引入“间距(gutter)”参数，计算精确的拼版总尺寸。
    """
    global memo
    memo = {}

    # 1. 计算含出血的产品尺寸
    item_w_bleed = item_w + 2 * bleed
    item_h_bleed = item_h + 2 * bleed

    # 2. 分析两种“标准网格”布局方案（已包含间距计算）
    # 方案A: 不旋转
    cols_a = math.floor((sheet_w + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    rows_a = math.floor((sheet_h + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    count_a = cols_a * rows_a
    layout_a = {
        "type": "常规网格 (Normal Grid)",
        "count": count_a,
        "total_width": cols_a * item_w_bleed + max(0, cols_a - 1) * gutter,
        "total_height": rows_a * item_h_bleed + max(0, rows_a - 1) * gutter
    }

    # 方案B: 旋转
    cols_b = math.floor((sheet_w + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    rows_b = math.floor((sheet_h + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    count_b = cols_b * rows_b
    layout_b = {
        "type": "旋转网格 (Rotated Grid)",
        "count": count_b,
        "total_width": cols_b * item_h_bleed + max(0, cols_b - 1) * gutter,
        "total_height": rows_b * item_w_bleed + max(0, rows_b - 1) * gutter
    }
    
    # 3. 调用递归算法，验证是否存在数量更多的“紧密贴合”混合布局
    best_tight_pack_count = _find_max_count_recursive(sheet_w, sheet_h, item_w_bleed, item_h_bleed)

    # 4. 决策：比较并选出最终的最优方案
    best_layout = layout_a if layout_a["count"] >= layout_b["count"] else layout_b
    
    if best_tight_pack_count > best_layout["count"]:
        best_layout = {
            "type": "混合紧密布局 (Mixed Tight-Pack)",
            "count": best_tight_pack_count,
            "total_width": sheet_w, 
            "total_height": sheet_h
        }
    
    # 5. 计算最终利用率并整理输出
    total_area = sheet_w * sheet_h
    # 纸张利用率应基于产品净尺寸计算
    item_net_area = item_w * item_h
    efficiency = (best_layout["count"] * item_net_area / total_area) * 100 if total_area > 0 else 0
    
    result = {
        "最优合拼版数": best_layout["count"],
        "布局类型": best_layout["type"],
        "拼版后实际尺寸": f"{best_layout['total_width']}mm x {best_layout['total_height']}mm",
        "纸张利用率(基于产品净尺寸)": f"{efficiency:.2f}%",
        "输入参数": {
            "纸张尺寸": f"{sheet_w}mm x {sheet_h}mm",
            "产品原始尺寸": f"{item_w}mm x {item_h}mm",
            "出血值": f"{bleed}mm",
            "间距": f"{gutter}mm",
            "含出血排版单元尺寸": f"{item_w_bleed}mm x {item_h_bleed}mm"
        }
    }
    return result

# --- 执行计算 ---
if __name__ == "__main__":
    # 定义设备和产品信息
    sheet_length = 740
    sheet_width = 510
    product_length = 200
    product_width = 300
    bleed_value = 1
    gutter_value = 0  # 新增：定义产品间的间距为5mm

    # 调用新的分析函数
    analysis_result = analyze_layout(sheet_length, sheet_width, product_length, product_width, bleed_value, gutter_value)

    # 打印格式化的结果
    print("--- 包装排版优化分析报告 (含间距) ---")
    for key, value in analysis_result.items():
        if isinstance(value, dict):
            print(f"\n[ {key} ]")
            for sub_key, sub_value in value.items():
                print(f"  - {sub_key}: {sub_value}")
        else:
            print(f"- {key}: {value}")
    print("-----------------------------------")