import math

# ----------------------------------------------------------------------------
# 内部核心算法：递归分割 (用于验证是否存在更优的复杂布局)
# 在绝大多数情况下，您不需要修改这部分代码。
# ----------------------------------------------------------------------------
memo = {}
def _find_max_count_recursive(sheet_w, sheet_h, item_w, item_h):
    """内部递归函数，探索理论上的最大产品安放数量（紧密贴合，无间距）。"""
    global memo
    if sheet_w < sheet_h:
        [sheet_w, sheet_h] = [sheet_h, sheet_w]
    key = (sheet_w, sheet_h)
    if key in memo:
        return memo[key]

    max_items = 0
    if sheet_w >= item_w and sheet_h >= item_h:
        max_items = max(max_items, math.floor(sheet_w / item_w) * math.floor(sheet_h / item_h))
    if sheet_w >= item_h and sheet_h >= item_w:
        max_items = max(max_items, math.floor(sheet_w / item_h) * math.floor(sheet_h / item_w))

    if sheet_w >= item_w and sheet_h >= item_h:
        res1 = _find_max_count_recursive(sheet_w - item_w, sheet_h, item_w, item_h) + _find_max_count_recursive(item_w, sheet_h - item_h, item_w, item_h)
        max_items = max(max_items, 1 + res1)
        res2 = _find_max_count_recursive(sheet_w, sheet_h - item_h, item_w, item_h) + _find_max_count_recursive(sheet_w - item_w, item_h, item_w, item_h)
        max_items = max(max_items, 1 + res2)
    if sheet_w >= item_h and sheet_h >= item_w:
        res3 = _find_max_count_recursive(sheet_w - item_h, sheet_h, item_w, item_h) + _find_max_count_recursive(item_h, sheet_h - item_w, item_w, item_h)
        max_items = max(max_items, 1 + res3)
        res4 = _find_max_count_recursive(sheet_w, sheet_h - item_w, item_w, item_h) + _find_max_count_recursive(sheet_w - item_h, item_w, item_w, item_h)
        max_items = max(max_items, 1 + res4)

    memo[key] = max_items
    return max_items

# ----------------------------------------------------------------------------
# 主要分析函数：您将调用的核心功能
# ----------------------------------------------------------------------------
def analyze_layout(sheet_w, sheet_h, item_w, item_h, bleed=0, gutter=0):
    """
    对排版方案进行全面分析，计算最优拼版数、布局方式以及精确的最小所需纸张尺寸。

    :param sheet_w: 纸张宽度 (mm) - 用于计算可容纳数量
    :param sheet_h: 纸张高度 (mm) - 用于计算可容纳数量
    :param item_w: 产品原始宽度 (mm)
    :param item_h: 产品原始高度 (mm)
    :param bleed: 添加到产品每个边缘的出血值 (mm)，默认为0
    :param gutter: 产品之间的间距 (mm)，默认为0
    :return: 一个包含详细分析结果的字典
    """
    global memo
    memo = {} 

    # 步骤 1: 计算含出血的产品尺寸
    item_w_bleed = item_w + 2 * bleed
    item_h_bleed = item_h + 2 * bleed

    # 步骤 2: 分析两种“标准网格”布局方案（已包含间距）
    # A. 常规布局（不旋转）
    cols_a = math.floor((sheet_w + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    rows_a = math.floor((sheet_h + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    count_a = cols_a * rows_a
    layout_a = {
        "type": "常规网格 (Normal Grid)",
        "count": count_a,
        "cols": cols_a,
        "rows": rows_a,
        "item_dims": (item_w_bleed, item_h_bleed)
    }

    # B. 旋转布局
    cols_b = math.floor((sheet_w + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    rows_b = math.floor((sheet_h + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    count_b = cols_b * rows_b
    layout_b = {
        "type": "旋转网格 (Rotated Grid)",
        "count": count_b,
        "cols": cols_b,
        "rows": rows_b,
        "item_dims": (item_h_bleed, item_w_bleed)
    }
    
    # 步骤 3: 递归验证是否存在数量更多的“紧密贴合”布局
    best_tight_pack_count = _find_max_count_recursive(sheet_w, sheet_h, item_w_bleed, item_h_bleed)

    # 步骤 4: 决策并选出最优方案
    best_layout_info = layout_a if layout_a["count"] >= layout_b["count"] else layout_b
    
    if best_tight_pack_count > best_layout_info["count"]:
        # 这种情况很少见，但算法会捕捉到
        best_layout_info = {
            "type": "混合紧密布局 (Mixed Tight-Pack)",
            "count": best_tight_pack_count,
            "cols": 1, "rows": 1, # 无法用简单行列描述
            "item_dims": (sheet_w, sheet_h) # 占用整个版面
        }
        min_width = sheet_w
        min_height = sheet_h
    else:
        # 步骤 5: 计算最优方案的精确最小尺寸
        item_layout_w, item_layout_h = best_layout_info["item_dims"]
        cols, rows = best_layout_info["cols"], best_layout_info["rows"]
        
        min_width = cols * item_layout_w + max(0, cols - 1) * gutter
        min_height = rows * item_layout_h + max(0, rows - 1) * gutter

    # 步骤 6: 整理并返回报告
    result = {
        "最优合拼版数": best_layout_info["count"],
        "布局类型": best_layout_info["type"],
        "理论最小纸张尺寸": {
            "width": min_width,
            "height": min_height
        },
        "输入参数": {
            "纸张尺寸": f"{sheet_w}mm x {sheet_h}mm",
            "产品原始尺寸": f"{item_w}mm x {item_h}mm",
            "出血": f"{bleed}mm",
            "间距": f"{gutter}mm",
        }
    }
    return result

# ----------------------------------------------------------------------------
# 执行脚本
# ----------------------------------------------------------------------------
if __name__ == "__main__":
    
    # --- 用户输入区域：请在此处修改您的纸张、产品及生产参数 ---
    sheet_length = 740      # 纸张长度 (mm)
    sheet_width = 510       # 纸张宽度 (mm)
    product_length = 200    # 产品原始长度 (mm)
    product_width = 300     # 产品原始宽度 (mm)
    bleed_value = 3         # 出血值 (mm)
    gutter_value = 5        # 产品之间的间距 (mm)
    gripper_margin = 10     # 印刷机咬口值 (mm)，通常加在纸张短边
    # --- 参数修改结束 ---

    # 调用主分析函数
    analysis_result = analyze_layout(sheet_length, sheet_width, product_length, product_width, bleed_value, gutter_value)

    # --- 打印格式化的分析报告 ---
    print("--- 包装排版优化及最小尺寸分析报告 ---")
    
    # 打印核心结果
    print(f"\n[ 核心结论 ]")
    print(f"  - 最优合拼版数: {analysis_result['最优合拼版数']}")
    print(f"  - 最佳布局类型: {analysis_result['布局类型']}")
    
    # 提取并计算最终尺寸
    min_dims = analysis_result['理论最小纸张尺寸']
    min_w, min_h = min_dims['width'], min_dims['height']
    
    print(f"\n[ 尺寸分析 ]")
    print(f"  - 理论最小纸张尺寸: {min_w}mm x {min_h}mm")
    
    # 计算实际建议采购尺寸
    practical_w, practical_h = min_w, min_h
    if practical_w > practical_h:
        practical_h += gripper_margin # 咬口加在短边
    else:
        practical_w += gripper_margin # 咬口加在短边
        
    print(f"  - 建议实际纸张尺寸: {practical_w}mm x {practical_h}mm (已包含 {gripper_margin}mm 咬口)")

    # 打印输入参数供核对
    print(f"\n[ 输入参数回顾 ]")
    for key, value in analysis_result['输入参数'].items():
        print(f"  - {key}: {value}")
        
    print("\n--------------------------------------")