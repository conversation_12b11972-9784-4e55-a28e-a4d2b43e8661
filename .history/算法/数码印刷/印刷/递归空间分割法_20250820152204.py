# 用于存储已计算结果的字典，避免重复计算
memo = {}

def find_optimal_layout(sheet_w, sheet_h, item_w, item_h):
    """
    主函数，用于启动最优排版计算。
    
    Args:
        sheet_w (int): 纸张宽度
        sheet_h (int): 纸张高度
        item_w (int): 产品宽度
        item_h (int): 产品高度

    Returns:
        int: 最大可排版数量
    """
    global memo
    # 每次新的计算都清空记忆缓存
    memo = {}
    
    # 为了提高记忆缓存的命中率，我们使用一个规范的尺寸表示（宽边在前）
    if sheet_w < sheet_h:
        sheet_w, sheet_h = sheet_h, sheet_w
        
    return _solve(sheet_w, sheet_h, item_w, item_h)


def _solve(sheet_w, sheet_h, item_w, item_h):
    """
    执行递归计算的核心函数。
    """
    global memo
    
    # 规范化尺寸以便于记忆缓存的查询
    if sheet_w < sheet_h:
        sheet_w, sheet_h = sheet_h, sheet_w
    
    # 1. 检查记忆缓存中是否已有结果
    if (sheet_w, sheet_h) in memo:
        return memo[(sheet_w, sheet_h)]

    # --- 基础计算：计算单一方向铺满的最大数量作为基准 ---
    max_items = 0
    # 方案A：所有产品不旋转 (item_w x item_h)
    if sheet_w >= item_w and sheet_h >= item_h:
        max_items = max(max_items, (sheet_w // item_w) * (sheet_h // item_h))
    
    # 方案B：所有产品旋转90度 (item_h x item_w)
    if sheet_w >= item_h and sheet_h >= item_w:
        max_items = max(max_items, (sheet_w // item_h) * (sheet_h // item_w))

    # --- 递归分割：尝试混合旋转方案 ---

    # 尝试摆放一个“不旋转”的产品，并对剩余空间进行递归计算
    if sheet_w >= item_w and sheet_h >= item_h:
        # 方式1: 垂直切割，将剩余的L形空间分割为两个矩形
        res1 = _solve(sheet_w - item_w, sheet_h, item_w, item_h) + \
               _solve(item_w, sheet_h - item_h, item_w, item_h)
        max_items = max(max_items, 1 + res1)

        # 方式2: 水平切割，将剩余的L形空间分割为另外两个矩形
        res2 = _solve(sheet_w, sheet_h - item_h, item_w, item_h) + \
               _solve(sheet_w - item_w, item_h, item_w, item_h)
        max_items = max(max_items, 1 + res2)

    # 尝试摆放一个“旋转”的产品，并对剩余空间进行递归计算
    if sheet_w >= item_h and sheet_h >= item_w:
        # 方式1: 垂直切割
        res3 = _solve(sheet_w - item_h, sheet_h, item_w, item_h) + \
               _solve(item_h, sheet_h - item_w, item_w, item_h)
        max_items = max(max_items, 1 + res3)
        
        # 方式2: 水平切割
        res4 = _solve(sheet_w, sheet_h - item_w, item_w, item_h) + \
               _solve(sheet_w - item_h, item_w, item_w, item_h)
        max_items = max(max_items, 1 + res4)

    # 2. 将最终计算结果存入记忆缓存
    memo[(sheet_w, sheet_h)] = max_items
    return max_items

# --- 使用您的数据进行测试 ---

# 设备和产品信息
sheet_length = 740
sheet_width = 510
product_length = 146
product_width = 400

# 执行算法
best_number = find_optimal_layout(sheet_length, sheet_width, product_length, product_width)

# 打印结果
print(f"设备信息:")
print(f"  有效印刷尺寸: {sheet_length}mm x {sheet_width}mm")
print("-" * 30)
print(f"产品信息:")
print(f"  产品尺寸: {product_length}mm x {product_width}mm")
print("-" * 30)
print(f"算法计算结果:")
print(f"  最佳合拼版数: {best_number}")