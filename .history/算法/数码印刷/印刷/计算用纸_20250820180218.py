import math

# --- 核心引擎: 启发式递归算法 ---
# 职责：通过“自顶向下”的递归分割，快速探索理论上的最大拼版数。
# ---
memo_recursive = {}
def _find_max_count_recursive(sheet_w, sheet_h, item_w, item_h):
    """
    内部启发式递归函数，用于快速验证是否存在更优的混合布局。
    """
    global memo_recursive
    if sheet_w < sheet_h: [sheet_w, sheet_h] = [sheet_h, sheet_w]
    key = (sheet_w, sheet_h, item_w, item_h)
    if key in memo_recursive: return memo_recursive[key]

    # (递归逻辑与之前版本相同)
    max_items = 0
    if sheet_w >= item_w and sheet_h >= item_h:
        max_items = max(max_items, math.floor(sheet_w / item_w) * math.floor(sheet_h / item_h))
    if sheet_w >= item_h and sheet_h >= item_w:
        max_items = max(max_items, math.floor(sheet_w / item_h) * math.floor(sheet_h / item_w))
    if sheet_w >= item_w and sheet_h >= item_h:
        res1 = _find_max_count_recursive(sheet_w - item_w, sheet_h, item_w, item_h) + _find_max_count_recursive(item_w, sheet_h - item_h, item_w, item_h)
        max_items = max(max_items, 1 + res1)
        res2 = _find_max_count_recursive(sheet_w, sheet_h - item_h, item_w, item_h) + _find_max_count_recursive(sheet_w - item_w, item_h, item_w, item_h)
        max_items = max(max_items, 1 + res2)
    if sheet_w >= item_h and sheet_h >= item_w:
        res3 = _find_max_count_recursive(sheet_w - item_h, sheet_h, item_w, item_h) + _find_max_count_recursive(item_h, sheet_h - item_w, item_w, item_h)
        max_items = max(max_items, 1 + res3)
        res4 = _find_max_count_recursive(sheet_w, sheet_h - item_w, item_w, item_h) + _find_max_count_recursive(sheet_w - item_h, item_w, item_w, item_h)
        max_items = max(max_items, 1 + res4)
    
    memo_recursive[key] = max_items
    return max_items


# --- 主分析函数 ---
def analyze_layout_with_recursion(sheet_w, sheet_h, item_w, item_h, bleed=0, gutter=0):
    """
    对排版方案进行全面分析，计算最小用纸面积，并使用启发式递归算法进行验证。
    """
    global memo_recursive
    memo_recursive = {} # 每次调用都清空缓存

    # 步骤1：计算包含出血的排版单元尺寸
    item_w_bleed = item_w + 2 * bleed
    item_h_bleed = item_h + 2 * bleed

    # 步骤2：分析两种主要的“实用网格布局”
    # 方案A: 常规网格
    cols_a = math.floor((sheet_w + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    rows_a = math.floor((sheet_h + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    layout_a = {
        "type": "常规网格 (Normal Grid)",
        "count": cols_a * rows_a,
        "min_width": cols_a * item_w_bleed + max(0, cols_a - 1) * gutter,
        "min_height": rows_a * item_h_bleed + max(0, rows_a - 1) * gutter
    }

    # 方案B: 旋转网格
    cols_b = math.floor((sheet_w + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    rows_b = math.floor((sheet_h + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    layout_b = {
        "type": "旋转网格 (Rotated Grid)",
        "count": cols_b * rows_b,
        "min_width": cols_b * item_h_bleed + max(0, cols_b - 1) * gutter,
        "min_height": rows_b * item_w_bleed + max(0, rows_b - 1) * gutter
    }

    # 步骤3：确定最佳的“实用方案”
    best_practical_layout = layout_a if layout_a["count"] >= layout_b["count"] else layout_b

    # 步骤4：调用启发式递归算法进行理论验证
    theoretical_max_count = _find_max_count_recursive(sheet_w, sheet_h, item_w_bleed, item_h_bleed)

    # 步骤5：生成最终分析报告
    min_width = best_practical_layout["min_width"]
    min_height = best_practical_layout["min_height"]
    min_area = min_width * min_height

    summary = f"最佳实用方案可拼 {best_practical_layout['count']} 个。经启发式递归验证，理论最大拼版数是 {theoretical_max_count} 个。"
    if best_practical_layout['count'] >= theoretical_max_count:
        summary += " 结论：当前实用方案已达到或超过理论值，是理想的生产策略。"
    
    result = {
        "最优合拼版数": best_practical_layout["count"],
        "布局类型": best_practical_layout["type"],
        "最小用纸尺寸": f"{min_width}mm x {min_height}mm",
        "最小用纸面积": f"{min_area} mm²",
        "分析概要": summary,
    }
    return result

# --- 执行计算 ---
if __name__ == "__main__":
    # --- 用户输入区域 ---
    sheet_length = 740
    sheet_width = 510
    product_length = 200
    product_width = 300
    
    print("--- 场景1：不考虑出血和间距的理论极限分析 ---")
    report_theoretical = analyze_layout_with_recursion(
        sheet_length, sheet_width, product_length, product_width, bleed=0, gutter=0
    )
    for key, value in report_theoretical.items():
        print(f"- {key}: {value}")