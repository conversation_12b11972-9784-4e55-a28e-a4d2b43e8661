import math

# --- 内部核心算法 ---
memo = {}
def _find_max_count_recursive(sheet_w, sheet_h, item_w, item_h):
    # (内部递归逻辑... 省略，与之前版本相同)
    global memo
    if sheet_w < sheet_h: [sheet_w, sheet_h] = [sheet_h, sheet_w]
    key = (sheet_w, sheet_h)
    if key in memo: return memo[key]
    max_items = 0
    if sheet_w >= item_w and sheet_h >= item_h: max_items = max(max_items, math.floor(sheet_w / item_w) * math.floor(sheet_h / item_h))
    if sheet_w >= item_h and sheet_h >= item_w: max_items = max(max_items, math.floor(sheet_w / item_h) * math.floor(sheet_h / item_w))
    if sheet_w >= item_w and sheet_h >= item_h:
        res1 = _find_max_count_recursive(sheet_w - item_w, sheet_h, item_w, item_h) + _find_max_count_recursive(item_w, sheet_h - item_h, item_w, item_h)
        max_items = max(max_items, 1 + res1)
        res2 = _find_max_count_recursive(sheet_w, sheet_h - item_h, item_w, item_h) + _find_max_count_recursive(sheet_w - item_w, item_h, item_w, item_h)
        max_items = max(max_items, 1 + res2)
    if sheet_w >= item_h and sheet_h >= item_w:
        res3 = _find_max_count_recursive(sheet_w - item_h, sheet_h, item_w, item_h) + _find_max_count_recursive(item_h, sheet_h - item_w, item_w, item_h)
        max_items = max(max_items, 1 + res3)
        res4 = _find_max_count_recursive(sheet_w, sheet_h - item_w, item_w, item_h) + _find_max_count_recursive(sheet_w - item_h, item_w, item_w, item_h)
        max_items = max(max_items, 1 + res4)
    memo[key] = max_items
    return max_items

# --- 主分析函数 ---
def analyze_layout(sheet_w, sheet_h, item_w, item_h, bleed=0, gutter=0):
    """
    对排版方案进行全面分析，此函数已包含最小纸张尺寸的计算。
    """
    global memo
    memo = {} 
    item_w_bleed = item_w + 2 * bleed
    item_h_bleed = item_h + 2 * bleed

    # --- 分析常规网格布局 ---
    cols_a = math.floor((sheet_w + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    rows_a = math.floor((sheet_h + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    layout_a = {
        "type": "常规网格 (Normal Grid)",
        "count": cols_a * rows_a,
        # 【核心计算】计算此布局的最小宽度需求
        "total_width": cols_a * item_w_bleed + max(0, cols_a - 1) * gutter,
        # 【核心计算】计算此布局的最小高度需求
        "total_height": rows_a * item_h_bleed + max(0, rows_a - 1) * gutter
    }

    # --- 分析旋转网格布局 ---
    cols_b = math.floor((sheet_w + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    rows_b = math.floor((sheet_h + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    layout_b = {
        "type": "旋转网格 (Rotated Grid)",
        "count": cols_b * rows_b,
        # 【核心计算】计算此布局的最小宽度需求
        "total_width": cols_b * item_h_bleed + max(0, cols_b - 1) * gutter,
        # 【核心计算】计算此布局的最小高度需求
        "total_height": rows_b * item_w_bleed + max(0, rows_b - 1) * gutter
    }
    
    # --- 决策与验证 ---
    best_practical_layout = layout_a if layout_a["count"] >= layout_b["count"] else layout_b
    best_tight_pack_count = _find_max_count_recursive(sheet_w, sheet_h, item_w_bleed, item_h_bleed)
    if best_tight_pack_count > best_practical_layout["count"]:
        best_practical_layout = {
            "type": "混合紧密布局 (Mixed Tight-Pack)", "count": best_tight_pack_count,
            "total_width": sheet_w, "total_height": sheet_h
        }
    
    # --- 整理输出报告 ---
    # ★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★
    #  这里的'拼版后实际尺寸'就是您所需要的“最小纸张利用尺寸”
    # ★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★
    result = {
        "最优合拼版数": best_practical_layout["count"],
        "布局类型": best_practical_layout["type"],
        "拼版后实际尺寸 (最小纸张利用尺寸)": f"{best_practical_layout['total_width']}mm x {best_practical_layout['total_height']}mm",
        "输入参数": {
            "纸张尺寸": f"{sheet_w}mm x {sheet_h}mm", "产品原始尺寸": f"{item_w}mm x {item_h}mm",
            "出血值": f"{bleed}mm", "间距": f"{gutter}mm",
        }
    }
    return result

# --- 执行计算 ---
if __name__ == "__main__":
    sheet_length, sheet_width = 740, 510
    product_length, product_width = 200, 300
    bleed_value, gutter_value = 2, 0

    analysis_result = analyze_layout(sheet_length, sheet_width, product_length, product_width, bleed_value, gutter_value)

    print("--- 包装排版优化分析报告 ---")
    for key, value in analysis_result.items():
        if isinstance(value, dict):
            print(f"\n[ {key} ]")
            for sub_key, sub_value in value.items():
                print(f"  - {sub_key}: {sub_value}")
        else:
            print(f"- {key}: {value}")
    print("----------------------------")