import math

# --- 内部核心算法 ---
# 这是一个高级功能，用于探索理论上的最优解，通常情况下您不需要修改它。
# ---

# 全局变量，用于“记忆化”缓存，避免重复计算，提升递归效率
memo = {}

def _find_max_count_recursive(sheet_w, sheet_h, item_w, item_h):
    """
    内部递归函数（高级功能）：通过递归空间分割，探索理论上的最大产品安放数量。
    它用于验证是否存在比标准网格布局更优的、不考虑间距的复杂混合布局。
    """
    global memo
    # (内部递归逻辑... 省略，与之前版本相同)
    if sheet_w < sheet_h:
        [sheet_w, sheet_h] = [sheet_h, sheet_w]
    key = (sheet_w, sheet_h)
    if key in memo:
        return memo[key]
    max_items = 0
    if sheet_w >= item_w and sheet_h >= item_h:
        max_items = max(max_items, math.floor(sheet_w / item_w) * math.floor(sheet_h / item_h))
    if sheet_w >= item_h and sheet_h >= item_w:
        max_items = max(max_items, math.floor(sheet_w / item_h) * math.floor(sheet_h / item_w))
    if sheet_w >= item_w and sheet_h >= item_h:
        res1 = _find_max_count_recursive(sheet_w - item_w, sheet_h, item_w, item_h) + _find_max_count_recursive(item_w, sheet_h - item_h, item_w, item_h)
        max_items = max(max_items, 1 + res1)
        res2 = _find_max_count_recursive(sheet_w, sheet_h - item_h, item_w, item_h) + _find_max_count_recursive(sheet_w - item_w, item_h, item_w, item_h)
        max_items = max(max_items, 1 + res2)
    if sheet_w >= item_h and sheet_h >= item_w:
        res3 = _find_max_count_recursive(sheet_w - item_h, sheet_h, item_w, item_h) + _find_max_count_recursive(item_h, sheet_h - item_w, item_w, item_h)
        max_items = max(max_items, 1 + res3)
        res4 = _find_max_count_recursive(sheet_w, sheet_h - item_w, item_w, item_h) + _find_max_count_recursive(sheet_w - item_h, item_w, item_w, item_h)
        max_items = max(max_items, 1 + res4)
    memo[key] = max_items
    return max_items


# --- 主要分析函数 ---
# 这是您将要调用的主函数。
# ---

def analyze_layout(sheet_w, sheet_h, item_w, item_h, bleed=0, gutter=0):
    """
    对排版方案进行全面分析，并返回包含详细信息的字典。
    这是本脚本的核心功能函数。

    :param sheet_w: 纸张宽度 (mm)
    :param sheet_h: 纸张高度 (mm)
    :param item_w: 产品原始宽度 (mm)
    :param item_h: 产品原始高度 (mm)
    :param bleed: 添加到产品每个边缘的出血值 (mm)，默认为0
    :param gutter: 产品之间的间距 (mm)，默认为0
    :return: 一个包含详细分析结果的字典
    """
    global memo
    memo = {} # 每次调用主函数时，清空缓存，确保计算的独立性

    # --- 步骤 1: 准备计算参数 ---
    # 根据出血值，计算出单个产品在排版时实际占用的空间尺寸。
    item_w_bleed = item_w + 2 * bleed
    item_h_bleed = item_h + 2 * bleed

    # --- 步骤 2: 分析两种“标准网格”布局方案 ---
    # 这是最常见的两种生产布局，算法将详细计算并比较它们。

    # --- A. 常规布局（不旋转）---
    # 关键公式：计算在有间距的情况下，一行/列能容纳的最大产品数。
    cols_a = math.floor((sheet_w + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    rows_a = math.floor((sheet_h + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    count_a = cols_a * rows_a
    layout_a = {
        "type": "常规网格 (Normal Grid)",
        "count": count_a,
        # 关键公式：计算N个产品加上N-1个间距后的拼版总宽度/高度。
        "total_width": cols_a * item_w_bleed + max(0, cols_a - 1) * gutter,
        "total_height": rows_a * item_h_bleed + max(0, rows_a - 1) * gutter
    }

    # --- B. 旋转布局 ---
    cols_b = math.floor((sheet_w + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    rows_b = math.floor((sheet_h + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    count_b = cols_b * rows_b
    layout_b = {
        "type": "旋转网格 (Rotated Grid)",
        "count": count_b,
        "total_width": cols_b * item_h_bleed + max(0, cols_b - 1) * gutter,
        "total_height": rows_b * item_w_bleed + max(0, rows_b - 1) * gutter
    }
    
    # --- 步骤 3: 递归验证 ---
    # 调用内部递归算法，验证是否存在数量更多的“紧密贴合”（无间距）的复杂混合布局。
    # 这确保我们不会错过任何理论上可能更优的方案。
    best_tight_pack_count = _find_max_count_recursive(sheet_w, sheet_h, item_w_bleed, item_h_bleed)

    # --- 步骤 4: 最终决策 ---
    # 比较所有已分析的方案，选出产品数量最多的那一个作为最终的最优方案。
    best_layout = layout_a if layout_a["count"] >= layout_b["count"] else layout_b
    
    # 如果递归验证发现有数量更多的方案（小概率事件），则采纳该方案。
    if best_tight_pack_count > best_layout["count"]:
        best_layout = {
            "type": "混合紧密布局 (Mixed Tight-Pack)",
            "count": best_tight_pack_count,
            "total_width": sheet_w, 
            "total_height": sheet_h
        }
    
    # --- 步骤 5: 整理并输出报告 ---
    # 计算纸张利用率等最终指标，并格式化输出。
    total_area = sheet_w * sheet_h
    # 纸张利用率应基于产品的“净尺寸”（不含出血）来计算，这更能反映真实成本。
    item_net_area = item_w * item_h
    efficiency = (best_layout["count"] * item_net_area / total_area) * 100 if total_area > 0 else 0
    
    result = {
        "最优合拼版数": best_layout["count"],
        "布局类型": best_layout["type"],
        "拼版后实际尺寸": f"{best_layout['total_width']}mm x {best_layout['total_height']}mm",
        "纸张利用率(基于产品净尺寸)": f"{efficiency:.2f}%",
        "输入参数": {
            "纸张尺寸": f"{sheet_w}mm x {sheet_h}mm",
            "产品原始尺寸": f"{item_w}mm x {item_h}mm",
            "出血值": f"{bleed}mm",
            "间距": f"{gutter}mm",
            "含出血排版单元尺寸": f"{item_w_bleed}mm x {item_h_bleed}mm"
        }
    }
    return result

# --- 执行计算 ---
# 这是脚本的入口，您可以直接在这里修改参数来得到不同的计算结果。
# ---
if __name__ == "__main__":
    
    # --- 用户输入区域：请在此处修改您的纸张、产品及生产参数 ---
    sheet_length = 740      # 纸张长度 (mm)
    sheet_width = 510       # 纸张宽度 (mm)
    product_length = 200    # 产品原始长度 (mm)
    product_width = 300     # 产品原始宽度 (mm)
    bleed_value = 3         # 出血值 (mm)
    gutter_value = 5        # 产品之间的间距 (mm)
    # --- 参数修改结束 ---

    # 调用主分析函数，传入以上参数
    analysis_result = analyze_layout(sheet_length, sheet_width, product_length, product_width, bleed_value, gutter_value)

    # 将详细的分析结果格式化地打印出来
    print("--- 包装排版优化分析报告 (含间距) ---")
    for key, value in analysis_result.items():
        if isinstance(value, dict):
            print(f"\n[ {key} ]")
            for sub_key, sub_value in value.items():
                print(f"  - {sub_key}: {sub_value}")
        else:
            print(f"- {key}: {value}")
    print("-----------------------------------")