import sys

# 增大Python的递归深度限制，以处理复杂的计算
sys.setrecursionlimit(2000)

# 全局记忆化缓存
memo = {}

def _solve_recursive(sheet_w, sheet_h, item_w, item_h):
    """
    经过优化的核心递归函数。
    返回一个字典，包含: {'count': 数量, 'width': 使用宽度, 'height': 使用高度}。
    """
    global memo
    if sheet_w < sheet_h:
        sheet_w, sheet_h = sheet_h, sheet_w
    key = (sheet_w, sheet_h)
    if key in memo:
        return memo[key]

    # 基准情况：如果区域太小，无法容纳任何产品
    if (sheet_w < item_w and sheet_w < item_h) or \
       (sheet_h < item_w and sheet_h < item_h):
        return {'count': 0, 'width': 0, 'height': 0}

    # 将“什么都不放”作为初始最优解
    best_solution = {'count': 0, 'width': 0, 'height': 0}

    # 探索所有可能的“第一刀”切割方案
    #  item_dims 包含了 [不旋转尺寸] 和 [旋转尺寸]
    item_dims = [(item_w, item_h)]
    if item_w != item_h:
        item_dims.append((item_h, item_w))

    for iw, ih in item_dims:
        # --- 尝试垂直切割 ---
        if sheet_w >= iw:
            # 递归求解切割后的两个子区域
            solution1 = _solve_recursive(iw, sheet_h, item_w, item_h)
            solution2 = _solve_recursive(sheet_w - iw, sheet_h, item_w, item_h)

            # 合并结果
            combined_count = solution1['count'] + solution2['count']
            combined_width = solution1['width'] + solution2['width']
            combined_height = max(solution1['height'], solution2['height'])
            
            # 如果这是一个更优的解（能放更多，或者同样多但面积更小），则更新最优解
            if combined_count > best_solution['count'] or \
               (combined_count == best_solution['count'] and combined_width * combined_height < best_solution['width'] * best_solution['height']):
                best_solution = {'count': combined_count, 'width': combined_width, 'height': combined_height}

        # --- 尝试水平切割 ---
        if sheet_h >= ih:
            solution1 = _solve_recursive(sheet_w, ih, item_w, item_h)
            solution2 = _solve_recursive(sheet_w, sheet_h - ih, item_w, item_h)

            combined_count = solution1['count'] + solution2['count']
            combined_width = max(solution1['width'], solution2['width'])
            combined_height = solution1['height'] + solution2['height']

            if combined_count > best_solution['count'] or \
               (combined_count == best_solution['count'] and combined_width * combined_height < best_solution['width'] * best_solution['height']):
                best_solution = {'count': combined_count, 'width': combined_width, 'height': combined_height}

    memo[key] = best_solution
    return best_solution

def analyze_layout_advanced(sheet_w, sheet_h, item_w, item_h, bleed=0):
    """
    更高精度的排版分析函数。
    """
    global memo
    memo = {}

    item_w_bleed = item_w + 2 * bleed
    item_h_bleed = item_h + 2 * bleed

    # 直接调用优化后的递归函数得到最终的最优解
    best_layout = _solve_recursive(sheet_w, sheet_h, item_w_bleed, item_h_bleed)

    # 检查最优解是否恰好是简单的网格布局
    layout_type = "混合布局 (Mixed Layout)"
    # 检查常规网格
    cols_a = sheet_w // item_w_bleed
    rows_a = sheet_h // item_h_bleed
    if best_layout['count'] == cols_a * rows_a and best_layout['width'] == cols_a * item_w_bleed and best_layout['height'] == rows_a * item_h_bleed:
        layout_type = "常规网格 (Normal Grid)"
    # 检查旋转网格
    cols_b = sheet_w // item_h_bleed
    rows_b = sheet_h // item_w_bleed
    if best_layout['count'] == cols_b * rows_b and best_layout['width'] == cols_b * item_h_bleed and best_layout['height'] == rows_b * item_w_bleed:
        layout_type = "旋转网格 (Rotated Grid)"

    used_area = best_layout["width"] * best_layout["height"]
    total_area = sheet_w * sheet_h
    efficiency = (used_area / total_area) * 100 if total_area > 0 else 0
    
    result = {
        "最优合拼版数": best_layout["count"],
        "布局类型": layout_type,
        "最佳纸张使用尺寸 (精确)": f"{best_layout['width']}mm x {best_layout['height']}mm",
        "纸张利用率": f"{efficiency:.2f}%",
        "输入参数": {
            "纸张尺寸": f"{sheet_w}mm x {sheet_h}mm",
            "产品原始尺寸": f"{item_w}mm x {item_h}mm",
            "出血值": f"{bleed}mm",
            "含出血排版尺寸": f"{item_w_bleed}mm x {item_h_bleed}mm"
        }
    }
    return result

# --- 执行计算 ---
# 我们使用一个已知混合布局更优的经典案例来测试新算法的精确性
if __name__ == "__main__":
    sheet_length = 170
    sheet_width = 170
    product_length = 100
    product_width = 70
    bleed_value = 0

    analysis_result = analyze_layout_advanced(sheet_length, sheet_width, product_length, product_width, bleed_value)

    print("--- 高精度包装排版优化分析报告 ---")
    for key, value in analysis_result.items():
        if isinstance(value, dict):
            print(f"\n[ {key} ]")
            for sub_key, sub_value in value.items():
                print(f"  - {sub_key}: {sub_value}")
        else:
            print(f"- {key}: {value}")
    print("---------------------------------")