# 用于存储已计算结果的字典（记忆化），以避免重复计算，极大提升效率
memo = {}

def find_optimal_layout(sheet_w, sheet_h, item_w, item_h):
    """
    主函数，用于启动最优排版计算。
    它会清空记忆缓存，以确保每次都是全新的计算。
    """
    global memo
    memo = {}
    
    # 为了提高记忆缓存的命中率，使用规范的尺寸表示（宽边在前）
    if sheet_w < sheet_h:
        sheet_w, sheet_h = sheet_h, sheet_w
        
    return _solve(sheet_w, sheet_h, item_w, item_h)


def _solve(sheet_w, sheet_h, item_w, item_h):
    """
    执行递归计算的核心函数，包含记忆化逻辑。
    """
    global memo
    
    # 规范化尺寸以便于记忆缓存的查询
    if sheet_w < sheet_h:
        sheet_w, sheet_h = sheet_h, sheet_w
    
    # 1. 检查记忆缓存中是否已有当前尺寸的结果，如有则直接返回
    if (sheet_w, sheet_h) in memo:
        return memo[(sheet_w, sheet_h)]

    # --- 基础计算：计算单一方向铺满的最大数量作为基准解 ---
    max_items = 0
    # 方案A：所有产品不旋转 (item_w x item_h)
    if sheet_w >= item_w and sheet_h >= item_h:
        max_items = max(max_items, (sheet_w // item_w) * (sheet_h // item_h))
    
    # 方案B：所有产品旋转90度 (item_h x item_w)
    if sheet_w >= item_h and sheet_h >= item_w:
        max_items = max(max_items, (sheet_w // item_h) * (sheet_h // item_w))

    # --- 递归分割：尝试所有可行的混合旋转方案 ---

    # 尝试摆放一个“不旋转”的产品，并对剩余空间进行递归计算
    if sheet_w >= item_w and sheet_h >= item_h:
        # 方式1: 垂直切割，将剩余的L形空间分割为两个矩形
        res1 = _solve(sheet_w - item_w, sheet_h, item_w, item_h) + \
               _solve(item_w, sheet_h - item_h, item_w, item_h)
        max_items = max(max_items, 1 + res1)

        # 方式2: 水平切割，将剩余的L形空间分割为另外两个矩形
        res2 = _solve(sheet_w, sheet_h - item_h, item_w, item_h) + \
               _solve(sheet_w - item_w, item_h, item_w, item_h)
        max_items = max(max_items, 1 + res2)

    # 尝试摆放一个“旋转”的产品，并对剩余空间进行递归计算
    if sheet_w >= item_h and sheet_h >= item_w:
        # 方式1: 垂直切割
        res3 = _solve(sheet_w - item_h, sheet_h, item_w, item_h) + \
               _solve(item_h, sheet_h - item_w, item_w, item_h)
        max_items = max(max_items, 1 + res3)
        
        # 方式2: 水平切割
        res4 = _solve(sheet_w, sheet_h - item_w, item_w, item_h) + \
               _solve(sheet_w - item_h, item_w, item_w, item_h)
        max_items = max(max_items, 1 + res4)

    # 2. 将最终计算结果存入记忆缓存，并返回
    memo[(sheet_w, sheet_h)] = max_items
    return max_items

# --- 执行计算 ---
if __name__ == "__main__":
    # 定义设备和产品信息
    sheet_length = 740
    sheet_width = 510
    product_length = 200
    product_width = 300

    print("正在使用递归分割算法进行计算...")
    print("-" * 30)
    print(f"设备尺寸: {sheet_length}mm x {sheet_width}mm")
    print(f"产品尺寸: {product_length}mm x {product_width}mm")
    print("-" * 30)

    # 调用主函数执行算法
    best_number = find_optimal_layout(sheet_length, sheet_width, product_length, product_width)

    # 打印最终结果
    print(f"算法计算出的最优合拼版数是: {best_number}")