# 原始的递归分割算法核心代码保持不变，作为“最优数量”的计算引擎
memo = {}

def _find_max_count_recursive(sheet_w, sheet_h, item_w, item_h):
    """
    内部递归函数，仅用于计算给定区域内的最大产品数。
    (这是原始代码的核心，现在作为辅助函数)
    """
    global memo
    if sheet_w < sheet_h:
        [sheet_w, sheet_h] = [sheet_h, sheet_w]
    key = (sheet_w, sheet_h)
    if key in memo:
        return memo[key]

    max_items = 0
    # 不旋转
    if sheet_w >= item_w and sheet_h >= item_h:
        max_items = max(max_items, (sheet_w // item_w) * (sheet_h // item_h))
    # 旋转
    if sheet_w >= item_h and sheet_h >= item_w:
        max_items = max(max_items, (sheet_w // item_h) * (sheet_h // item_w))

    # 递归 - 不旋转
    if sheet_w >= item_w and sheet_h >= item_h:
        res1 = _find_max_count_recursive(sheet_w - item_w, sheet_h, item_w, item_h) + \
               _find_max_count_recursive(item_w, sheet_h - item_h, item_w, item_h)
        max_items = max(max_items, 1 + res1)
        res2 = _find_max_count_recursive(sheet_w, sheet_h - item_h, item_w, item_h) + \
               _find_max_count_recursive(sheet_w - item_w, item_h, item_w, item_h)
        max_items = max(max_items, 1 + res2)
    # 递归 - 旋转
    if sheet_w >= item_h and sheet_h >= item_w:
        res3 = _find_max_count_recursive(sheet_w - item_h, sheet_h, item_w, item_h) + \
               _find_max_count_recursive(item_h, sheet_h - item_w, item_w, item_h)
        max_items = max(max_items, 1 + res3)
        res4 = _find_max_count_recursive(sheet_w, sheet_h - item_w, item_w, item_h) + \
               _find_max_count_recursive(sheet_w - item_h, item_w, item_w, item_h)
        max_items = max(max_items, 1 + res4)

    memo[key] = max_items
    return max_items

# --- 新增的、功能更强大的主分析函数 ---
def analyze_layout(sheet_w, sheet_h, item_w, item_h, bleed=0):
    """
    对排版方案进行全面分析，并返回包含详细信息的字典。
    """
    global memo
    memo = {}

    # 1. 计算含出血的产品尺寸
    item_w_bleed = item_w + 2 * bleed
    item_h_bleed = item_h + 2 * bleed

    # 2. 分析两种“简单网格”布局方案
    # 方案A: 不旋转
    cols_a = sheet_w // item_w_bleed
    rows_a = sheet_h // item_h_bleed
    count_a = cols_a * rows_a
    layout_a = {
        "type": "常规网格 (Normal Grid)",
        "count": count_a,
        "used_width": cols_a * item_w_bleed,
        "used_height": rows_a * item_h_bleed
    }

    # 方案B: 旋转
    cols_b = sheet_w // item_h_bleed
    rows_b = sheet_h // item_w_bleed
    count_b = cols_b * rows_b
    layout_b = {
        "type": "旋转网格 (Rotated Grid)",
        "count": count_b,
        "used_width": cols_b * item_h_bleed,
        "used_height": rows_b * item_w_bleed
    }
    
    # 3. 调用递归算法，探索是否存在更优的“混合”布局
    best_mixed_count = _find_max_count_recursive(sheet_w, sheet_h, item_w_bleed, item_h_bleed)

    # 4. 决策：比较并选出最终的最优方案
    best_layout = layout_a if layout_a["count"] >= layout_b["count"] else layout_b
    
    if best_mixed_count > best_layout["count"]:
        # 如果递归混合方案更优，更新结果
        best_layout = {
            "type": "混合布局 (Mixed Layout)",
            "count": best_mixed_count,
            # 对于复杂的混合布局，占用的通常是整个版面，或难以用简单矩形描述
            "used_width": sheet_w, 
            "used_height": sheet_h
        }
    
    # 5. 计算最终利用率并整理输出
    used_area = best_layout["used_width"] * best_layout["used_height"]
    total_area = sheet_w * sheet_h
    efficiency = (used_area / total_area) * 100 if total_area > 0 else 0
    
    result = {
        "最优合拼版数": best_layout["count"],
        "布局类型": best_layout["type"],
        "最佳纸张使用尺寸": f"{best_layout['used_width']}mm x {best_layout['used_height']}mm",
        "纸张利用率": f"{efficiency:.2f}%",
        "输入参数": {
            "纸张尺寸": f"{sheet_w}mm x {sheet_h}mm",
            "产品原始尺寸": f"{item_w}mm x {item_h}mm",
            "出血值": f"{bleed}mm",
            "含出血排版尺寸": f"{item_w_bleed}mm x {item_h_bleed}mm"
        }
    }
    return result

# --- 执行计算 ---
if __name__ == "__main__":
    # 定义设备和产品信息
    sheet_length = 740
    sheet_width = 510
    product_length = 190
    product_width = 290
    bleed_value = 2

    # 调用新的分析函数
    analysis_result = analyze_layout(sheet_length, sheet_width, product_length, product_width, bleed_value)

    # 打印格式化的结果
    print("--- 包装排版优化分析报告 ---")
    for key, value in analysis_result.items():
        if isinstance(value, dict):
            print(f"\n[ {key} ]")
            for sub_key, sub_value in value.items():
                print(f"  - {sub_key}: {sub_value}")
        else:
            print(f"- {key}: {value}")
    print("----------------------------")