import math

# --- 核心算法: 启发式递归 ---
# 职责：通过“自顶向下”的递归分割，快速探索理论上的最大拼版数。
# 这部分代码是算法引擎，通常无需修改。
# ---

# 全局变量，用于“记忆化”缓存，以避免对相同尺寸的区域进行重复计算，从而提升递归效率。
memo = {}

def _find_max_count_recursive(sheet_w, sheet_h, item_w, item_h):
    """
    内部启发式递归函数。
    
    通过递归空间分割，探索在给定区域内进行紧密贴合（无间距）
    且允许混合旋转的情况下的理论最大产品数。
    """
    global memo
    # 为提高缓存命中率，使用规范化的尺寸(宽边在前)作为键
    if sheet_w < sheet_h: [sheet_w, sheet_h] = [sheet_h, sheet_w]
    key = (sheet_w, sheet_h, item_w, item_h)
    
    # 如果当前尺寸已在缓存中，直接返回结果，避免重复计算
    if key in memo: 
        return memo[key]
    
    # --- 递归逻辑 ---
    # 1. 基准解：计算当前区域不切割，直接平铺的最大数量
    max_items = 0
    # 不旋转
    if sheet_w >= item_w and sheet_h >= item_h: 
        max_items = max(max_items, math.floor(sheet_w / item_w) * math.floor(sheet_h / item_h))
    # 旋转
    if sheet_w >= item_h and sheet_h >= item_w: 
        max_items = max(max_items, math.floor(sheet_w / item_h) * math.floor(sheet_h / item_w))

    # 2. 递推：尝试所有可能的“第一刀”切割，并递归求解剩余空间
    # 尝试放置一个“不旋转”产品
    if sheet_w >= item_w and sheet_h >= item_h:
        # 垂直切割后的递归
        res1 = _find_max_count_recursive(sheet_w - item_w, sheet_h, item_w, item_h) + _find_max_count_recursive(item_w, sheet_h - item_h, item_w, item_h)
        max_items = max(max_items, 1 + res1)
        # 水平切割后的递归
        res2 = _find_max_count_recursive(sheet_w, sheet_h - item_h, item_w, item_h) + _find_max_count_recursive(sheet_w - item_w, item_h, item_w, item_h)
        max_items = max(max_items, 1 + res2)
    # 尝试放置一个“旋转”产品
    if sheet_w >= item_h and sheet_h >= item_w:
        # 垂直切割后的递归
        res3 = _find_max_count_recursive(sheet_w - item_h, sheet_h, item_w, item_h) + _find_max_count_recursive(item_h, sheet_h - item_w, item_w, item_h)
        max_items = max(max_items, 1 + res3)
        # 水平切割后的递归
        res4 = _find_max_count_recursive(sheet_w, sheet_h - item_w, item_w, item_h) + _find_max_count_recursive(sheet_w - item_h, item_w, item_w, item_h)
        max_items = max(max_items, 1 + res4)
    
    # 将计算结果存入缓存后返回
    memo[key] = max_items
    return max_items

# --- 主分析函数 ---
# 职责：接收用户输入，调用核心算法，并生成一份清晰的分析报告。
# 这是您主要交互的函数。
# ---
def analyze_layout_no_gutter(sheet_w, sheet_h, item_w, item_h, bleed=0):
    """
    对排版方案进行分析，仅考虑出血，不考虑间距。
    计算最优拼版数、布局类型及纸张利用率。
    """
    global memo
    memo = {} # 每次调用主函数时，清空缓存，确保计算的独立性

    # 步骤1：根据出血值，计算单个产品在排版时实际占用的空间尺寸
    item_w_bleed = item_w + 2 * bleed
    item_h_bleed = item_h + 2 * bleed

    # 步骤2：分析两种主要的、可实际生产的“网格布局”
    #   A. 常规网格布局 (不旋转)
    cols_a = math.floor(sheet_w / item_w_bleed) if item_w_bleed > 0 else 0
    rows_a = math.floor(sheet_h / item_h_bleed) if item_h_bleed > 0 else 0
    layout_a = {"type": "常规网格 (Normal Grid)", "count": cols_a * rows_a}

    #   B. 旋转网格布局
    cols_b = math.floor(sheet_w / item_h_bleed) if item_h_bleed > 0 else 0
    rows_b = math.floor(sheet_h / item_w_bleed) if item_w_bleed > 0 else 0
    layout_b = {"type": "旋转网格 (Rotated Grid)", "count": cols_b * rows_b}
    
    # 步骤3：决策与验证
    #   首先，在两种实用的网格布局中选择更优的一个
    best_practical_layout = layout_a if layout_a["count"] >= layout_b["count"] else layout_b
    
    #   然后，调用递归算法进行验证，检查是否存在数量更多的复杂混合布局
    theoretical_max_count = _find_max_count_recursive(sheet_w, sheet_h, item_w_bleed, item_h_bleed)
    
    #   如果混合布局能放更多，则更新最优方案
    if theoretical_max_count > best_practical_layout["count"]:
        best_practical_layout = {"type": "混合紧密布局 (Mixed Tight-Pack)", "count": theoretical_max_count}
    
    # 步骤4：计算纸张利用率
    total_sheet_area = sheet_w * sheet_h
    # 产品的“净面积”使用不含出血的原始尺寸，这更能反映真实成本
    net_product_area = item_w * item_h
    total_net_products_area = best_practical_layout["count"] * net_product_area
    efficiency = (total_net_products_area / total_sheet_area) * 100 if total_sheet_area > 0 else 0
    
    # 步骤5：整理并返回最终的分析报告
    result = {
        "最优合拼版数": best_practical_layout["count"],
        "布局类型": best_practical_layout["type"],
        "纸张利用率": f"{efficiency:.2f}% (基于产品净尺寸)",
        "输入参数": {
            "纸张尺寸": f"{sheet_w}mm x {sheet_h}mm",
            "产品原始尺寸": f"{item_w}mm x {item_h}mm",
            "出血值": f"{bleed}mm",
        }
    }
    return result

# --- 执行计算 ---
# 这是脚本的入口，您可以直接在这里修改参数来得到不同的计算结果。
# ---
if __name__ == "__main__":
    
    # --- 用户输入区域：请在此处修改您的纸张、产品及生产参数 ---
    sheet_length = 740      # 纸张长度 (mm)
    sheet_width = 510       # 纸张宽度 (mm)
    product_length = 200    # 产品原始长度 (mm)
    product_width = 300     # 产品原始宽度 (mm)
    bleed_value = 3         # 出血值 (mm)
    # --- 参数修改结束 ---

    # 调用分析函数
    analysis_result = analyze_layout_no_gutter(
        sheet_length, sheet_width, product_length, product_width, bleed_value
    )

    # 将详细的分析结果格式化地打印出来
    print("--- 包装排版优化分析报告 (无间距最终版) ---")
    for key, value in analysis_result.items():
        if isinstance(value, dict):
            print(f"\n[ {key} ]")
            for sub_key, sub_value in value.items():
                print(f"  - {sub_key}: {sub_value}")
        else:
            print(f"- {key}: {value}")
    print("---------------------------------------")