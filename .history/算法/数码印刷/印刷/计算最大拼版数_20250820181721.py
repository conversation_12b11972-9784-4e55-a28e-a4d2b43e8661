import math

# --- 内部核心算法: 启发式递归 ---
# 职责：通过递归分割，快速探索理论上的最大拼版数。
memo = {}
def _find_max_count_recursive(sheet_w, sheet_h, item_w, item_h):
    """内部启发式递归函数，用于快速验证是否存在更优的混合布局。"""
    global memo
    if sheet_w < sheet_h: [sheet_w, sheet_h] = [sheet_h, sheet_w]
    key = (sheet_w, sheet_h, item_w, item_h)
    if key in memo: return memo[key]
    
    # (递归逻辑与之前版本相同)
    max_items = 0
    if sheet_w >= item_w and sheet_h >= item_h: max_items = max(max_items, math.floor(sheet_w / item_w) * math.floor(sheet_h / item_h))
    if sheet_w >= item_h and sheet_h >= item_w: max_items = max(max_items, math.floor(sheet_w / item_h) * math.floor(sheet_h / item_w))
    if sheet_w >= item_w and sheet_h >= item_h:
        res1 = _find_max_count_recursive(sheet_w - item_w, sheet_h, item_w, item_h) + _find_max_count_recursive(item_w, sheet_h - item_h, item_w, item_h)
        max_items = max(max_items, 1 + res1)
        res2 = _find_max_count_recursive(sheet_w, sheet_h - item_h, item_w, item_h) + _find_max_count_recursive(sheet_w - item_w, item_h, item_w, item_h)
        max_items = max(max_items, 1 + res2)
    if sheet_w >= item_h and sheet_h >= item_w:
        res3 = _find_max_count_recursive(sheet_w - item_h, sheet_h, item_w, item_h) + _find_max_count_recursive(item_h, sheet_h - item_w, item_w, item_h)
        max_items = max(max_items, 1 + res3)
        res4 = _find_max_count_recursive(sheet_w, sheet_h - item_w, item_w, item_h) + _find_max_count_recursive(sheet_w - item_h, item_w, item_w, item_h)
        max_items = max(max_items, 1 + res4)
    
    memo[key] = max_items
    return max_items

# --- 主分析函数 (已增加利用率计算) ---
def analyze_layout_simplified(sheet_w, sheet_h, item_w, item_h, bleed=0, gutter=0):
    """
    对排版方案进行分析，计算最优拼版数、布局类型及纸张利用率。
    """
    global memo
    memo = {} 
    item_w_bleed = item_w + 2 * bleed
    item_h_bleed = item_h + 2 * bleed

    # --- 分析常规网格布局 ---
    cols_a = math.floor((sheet_w + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    rows_a = math.floor((sheet_h + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    layout_a = {"type": "常规网格 (Normal Grid)", "count": cols_a * rows_a}

    # --- 分析旋转网格布局 ---
    cols_b = math.floor((sheet_w + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    rows_b = math.floor((sheet_h + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    layout_b = {"type": "旋转网格 (Rotated Grid)", "count": cols_b * rows_b}
    
    # --- 决策与验证 ---
    best_practical_layout = layout_a if layout_a["count"] >= layout_b["count"] else layout_b
    theoretical_max_count = _find_max_count_recursive(sheet_w, sheet_h, item_w_bleed, item_h_bleed)
    if theoretical_max_count > best_practical_layout["count"]:
        best_practical_layout = {"type": "混合紧密布局 (Mixed Tight-Pack)", "count": theoretical_max_count}
    
    # --- 新增：计算纸张利用率 ---
    total_sheet_area = sheet_w * sheet_h
    # 产品净面积使用不含出血的原始尺寸
    net_product_area = item_w * item_h
    # 计算所有产品的总净面积
    total_net_products_area = best_practical_layout["count"] * net_product_area
    # 计算最终利用率
    efficiency = (total_net_products_area / total_sheet_area) * 100 if total_sheet_area > 0 else 0
    
    # --- 整理输出报告 (已增加利用率) ---
    result = {
        "最优合拼版数": best_practical_layout["count"],
        "布局类型": best_practical_layout["type"],
        # 新增的输出项
        "纸张利用率": f"{efficiency:.2f}% (基于产品净尺寸)",
        "输入参数": {
            "纸张尺寸": f"{sheet_w}mm x {sheet_h}mm",
            "产品原始尺寸": f"{item_w}mm x {item_h}mm",
            "出血值": f"{bleed}mm",
            "间距": f"{gutter}mm",
        }
    }
    return result

# --- 执行计算 ---
if __name__ == "__main__":
    # --- 用户输入区域 ---
    # 纸张尺寸参数 (单位: mm)
    sheet_length = 740      # 纸张长度，印刷纸张的长边尺寸
    sheet_width = 510       # 纸张宽度，印刷纸张的短边尺寸

    # 产品尺寸参数 (单位: mm)
    product_length = 204    # 产品长度，单个产品的长边尺寸
    product_width = 304     # 产品宽度，单个产品的短边尺寸

    # 印刷工艺参数 (单位: mm)
    bleed_value = 0         # 出血值，产品边缘延伸的安全距离，用于裁切误差补偿
    gutter_value = 0        # 间距值，产品之间的最小间隔距离，便于后期裁切分离
    # --- 参数修改结束 ---

    # 调用分析函数
    analysis_result = analyze_layout_simplified(
        sheet_length, sheet_width, product_length, product_width, bleed_value, gutter_value
    )

    # 打印结果
    print("--- 包装排版优化分析报告 (含利用率) ---")
    for key, value in analysis_result.items():
        if isinstance(value, dict):
            print(f"\n[ {key} ]")
            for sub_key, sub_value in value.items():
                print(f"  - {sub_key}: {sub_value}")
        else:
            print(f"- {key}: {value}")
    print("-----------------------------------")