import math

# --- 核心引擎: 启发式递归裁切算法 ---
# 职责：通过“自顶向下”的分割与探索，快速找到一个高质量的拼版方案。
# ---
memo_heuristic = {}
def _solve_heuristic_recursive(sheet_w, sheet_h, item_w, item_h):
    """
    内部启发式递归函数，用于探索理论上的最大产品安放数量。
    """
    global memo_heuristic
    # 为提高缓存命中率，使用规范化的尺寸作为键
    if sheet_w < sheet_h: [sheet_w, sheet_h] = [sheet_h, sheet_w]
    key = (sheet_w, sheet_h, item_w, item_h)
    if key in memo_heuristic: return memo_heuristic[key]

    # --- 1. 基准解：计算当前区域不切割时的最优解 ---
    max_items = 0
    item_normal = (item_w, item_h)
    item_rotated = (item_h, item_w)
    if sheet_w >= item_normal[0] and sheet_h >= item_normal[1]:
        max_items = max(max_items, math.floor(sheet_w / item_normal[0]) * math.floor(sheet_h / item_normal[1]))
    if sheet_w >= item_rotated[0] and sheet_h >= item_rotated[1]:
        max_items = max(max_items, math.floor(sheet_w / item_rotated[0]) * math.floor(sheet_h / item_rotated[1]))

    # --- 2. 递归探索：尝试分割并解决子问题 ---
    # 尝试放置一个不旋转的产品
    if sheet_w >= item_w and sheet_h >= item_h:
        res1 = _solve_heuristic_recursive(sheet_w - item_w, sheet_h, item_w, item_h) + _solve_heuristic_recursive(item_w, sheet_h - item_h, item_w, item_h)
        max_items = max(max_items, 1 + res1)
        res2 = _solve_heuristic_recursive(sheet_w, sheet_h - item_h, item_w, item_h) + _solve_heuristic_recursive(sheet_w - item_w, item_h, item_w, item_h)
        max_items = max(max_items, 1 + res2)
    # 尝试放置一个旋转的产品
    if sheet_w >= item_h and sheet_h >= item_w:
        res3 = _solve_heuristic_recursive(sheet_w - item_h, sheet_h, item_w, item_h) + _solve_heuristic_recursive(item_h, sheet_h - item_w, item_w, item_h)
        max_items = max(max_items, 1 + res3)
        res4 = _solve_heuristic_recursive(sheet_w, sheet_h - item_w, item_w, item_h) + _solve_heuristic_recursive(sheet_w - item_h, item_w, item_w, item_h)
        max_items = max(max_items, 1 + res4)

    memo_heuristic[key] = max_items
    return max_items


# --- 主分析函数：结合实用性与启发式验证 ---
def analyze_layout_with_heuristic(sheet_w, sheet_h, item_w, item_h, bleed=0, gutter=0):
    """
    使用启发式递归算法进行分析，并计算最小用纸面积。
    """
    global memo_heuristic
    memo_heuristic = {} # 每次调用都清空缓存

    # 步骤1：计算含出血的产品尺寸
    item_w_bleed = item_w + 2 * bleed
    item_h_bleed = item_h + 2 * bleed

    # 步骤2：分析两种主要的“实用网格布局”
    # 方案A: 常规网格
    cols_a = math.floor((sheet_w + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    rows_a = math.floor((sheet_h + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    layout_a = {
        "type": "常规网格 (Normal Grid)",
        "count": cols_a * rows_a,
        "min_width": cols_a * item_w_bleed + max(0, cols_a - 1) * gutter,
        "min_height": rows_a * item_h_bleed + max(0, rows_a - 1) * gutter
    }

    # 方案B: 旋转网格
    cols_b = math.floor((sheet_w + gutter) / (item_h_bleed + gutter)) if (item_h_bleed + gutter) > 0 else 0
    rows_b = math.floor((sheet_h + gutter) / (item_w_bleed + gutter)) if (item_w_bleed + gutter) > 0 else 0
    layout_b = {
        "type": "旋转网格 (Rotated Grid)",
        "count": cols_b * rows_b,
        "min_width": cols_b * item_h_bleed + max(0, cols_b - 1) * gutter,
        "min_height": rows_b * item_w_bleed + max(0, rows_b - 1) * gutter
    }

    # 步骤3：确定最佳实用方案
    best_practical_layout = layout_a if layout_a["count"] >= layout_b["count"] else layout_b

    # 步骤4：调用启发式递归算法进行验证
    heuristic_max_count = _solve_heuristic_recursive(sheet_w, sheet_h, item_w_bleed, item_h_bleed)
    
    # 步骤5：生成最终分析报告
    min_width = best_practical_layout['min_width']
    min_height = best_practical_layout['min_height']
    min_area = min_width * min_height

    result = {
        "最优合拼版数": best_practical_layout["count"],
        "布局类型": best_practical_layout["type"],
        "最小纸张尺寸": f"{min_width}mm x {min_height}mm",
        "最小用纸面积": f"{min_area} mm²", # 新增核心指标
        "启发式验证最大拼版数": heuristic_max_count,
        "输入参数": {
            "纸张尺寸": f"{sheet_w}mm x {sheet_h}mm", "产品原始尺寸": f"{item_w}mm x {item_h}mm",
            "出血": f"{bleed}mm", "间距": f"{gutter}mm",
        }
    }
    return result

# --- 执行计算 ---
if __name__ == "__main__":
    # --- 用户输入区域 ---
    sheet_length = 740
    sheet_width = 510
    product_length = 200
    product_width = 300
    bleed_value = 3
    gutter_value = 5
    # --- 参数修改结束 ---

    # 调用最终的分析函数
    final_report = analyze_layout_with_heuristic(
        sheet_length, sheet_width, product_length, product_width, bleed_value, gutter_value
    )

    # 打印格式化的报告
    print("--- 包装排版优化分析报告 (启发式递归版) ---")
    for key, value in final_report.items():
        if isinstance(value, dict):
            print(f"\n[ {key} ]")
            for sub_key, sub_value in value.items():
                print(f"  - {sub_key}: {sub_value}")
        else:
            print(f"- {key}: {value}")
    print("-----------------------------------------")