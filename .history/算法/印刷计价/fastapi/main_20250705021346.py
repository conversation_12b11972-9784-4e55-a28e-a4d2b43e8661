from fastapi import FastAP<PERSON>, Form, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
import math
from typing import List, Dict, Any

app = FastAPI(title="印刷计价系统", description="专业的印刷成本计算API")

app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

class PrintingRequest(BaseModel):
    quantity: int
    spot_colors: int = 0
    white_ink: bool = True
    white_ink_setup_cost: float = 190
    white_ink_price_per_sheet: float = 0.022

class IntervalDetail(BaseModel):
    interval_number: int
    range_start: int
    range_end: int
    unit_price: float
    quantity_in_interval: int
    interval_cost: float

class PrintingResult(BaseModel):
    quantity: int
    spot_colors: int
    white_ink: bool
    setup_cost: float
    total_setup_cost: float
    cmyk_intervals: List[IntervalDetail]
    total_cmyk_cost: float
    total_spot_color_cost: float
    white_ink_setup_cost: float
    white_ink_price_per_sheet: float
    total_white_ink_cost: float
    total_cost: float
    calculation_details: List[str]

def calculate_printing_cost_detailed(quantity, setup_cost, intervals, spot_colors=0, white_ink=True, white_ink_setup_cost=0, white_ink_price_per_sheet=0):
    """计算印刷成本，返回详细的计算结果"""

    calculation_details = []
    calculation_details.append(f"印刷数量：{quantity} 张")
    calculation_details.append(f"专色数量：{spot_colors} 个")
    calculation_details.append(f"开机费：{setup_cost} 元")
    calculation_details.append(f"是否需要白墨：{'是' if white_ink else '否'}")

    if white_ink:
        calculation_details.append(f"白墨开机费：{white_ink_setup_cost:.2f} 元")
        calculation_details.append(f"白墨单价：{white_ink_price_per_sheet:.2f} 元/张")

    # 1. 计算开机费
    total_setup_cost = setup_cost + spot_colors * (setup_cost / 2)
    calculation_details.append(f"开机总费用（含专色）：{setup_cost} + {spot_colors} * ({setup_cost:.2f} / 2) = {total_setup_cost:.2f} 元")

    # 2. 计算四色印刷费用
    total_cmyk_cost = 0
    remaining_quantity = quantity
    prev_end = 0
    cmyk_intervals = []

    calculation_details.append("--- 四色印刷费用明细 ---")
    for i, interval in enumerate(intervals):
        end = interval["end"] if interval["end"] != math.inf else 999999
        price = interval["price"]
        interval_quantity = min(remaining_quantity, end - prev_end)
        if interval_quantity < 0:
            interval_quantity = 0

        interval_cost = interval_quantity * price
        total_cmyk_cost += interval_cost

        interval_detail = IntervalDetail(
            interval_number=i+1,
            range_start=prev_end+1,
            range_end=end,
            unit_price=price,
            quantity_in_interval=interval_quantity,
            interval_cost=interval_cost
        )
        cmyk_intervals.append(interval_detail)

        calculation_details.append(f"区间 {i+1}: 数量 {prev_end+1} - {end} 张，单价 {price:.2f} 元/张，本区间费用：{interval_quantity} * {price:.2f} = {interval_cost:.2f} 元")

        remaining_quantity -= interval_quantity
        prev_end = end

        if remaining_quantity <= 0:
            break

    calculation_details.append(f"四色印刷总费用：{total_cmyk_cost:.2f} 元")

    # 3. 计算专色费用
    total_spot_color_cost = total_cmyk_cost * (spot_colors / 2) if spot_colors > 0 else 0
    calculation_details.append("--- 专色印刷费用 ---")
    if spot_colors > 0:
        calculation_details.append(f"专色印刷总费用：{total_cmyk_cost:.2f} * ({spot_colors} / 2) = {total_spot_color_cost:.2f} 元")
    else:
        calculation_details.append("无专色，专色印刷总费用：0.00 元")

    # 4. 计算白墨印刷费用
    total_white_ink_cost = 0
    calculation_details.append("--- 白墨印刷费用 ---")
    if white_ink:
        total_white_ink_cost = white_ink_setup_cost + quantity * white_ink_price_per_sheet
        calculation_details.append(f"白墨印刷总费用：{white_ink_setup_cost:.2f} + {quantity} * {white_ink_price_per_sheet:.2f} = {total_white_ink_cost:.2f} 元")
    else:
        calculation_details.append("无白墨印刷")

    # 5. 计算总价
    total_cost = total_setup_cost + total_cmyk_cost + total_spot_color_cost + total_white_ink_cost

    calculation_details.append("--- 总费用汇总 ---")
    calculation_details.append(f"总费用：{total_setup_cost:.2f} (开机费) + {total_cmyk_cost:.2f} (四色) + {total_spot_color_cost:.2f} (专色) + {total_white_ink_cost:.2f} (白墨) = {total_cost:.2f} 元")

    return PrintingResult(
        quantity=quantity,
        spot_colors=spot_colors,
        white_ink=white_ink,
        setup_cost=setup_cost,
        total_setup_cost=total_setup_cost,
        cmyk_intervals=cmyk_intervals,
        total_cmyk_cost=total_cmyk_cost,
        total_spot_color_cost=total_spot_color_cost,
        white_ink_setup_cost=white_ink_setup_cost,
        white_ink_price_per_sheet=white_ink_price_per_sheet,
        total_white_ink_cost=total_white_ink_cost,
        total_cost=total_cost,
        calculation_details=calculation_details
    )

# 价格配置
price_config = {
    "setup_cost": 270,
    "intervals": [
        {"end": 1000, "price": 0.08},
        {"end": 10000, "price": 0.08},
        {"end": math.inf, "price": 0.07},
    ],
}

white_ink_prices = {
    "setup_cost": 190,
    "price_per_sheet": 0.022
}

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/api/calculate")
async def calculate_api(request: PrintingRequest):
    """API接口：计算印刷成本"""
    try:
        result = calculate_printing_cost_detailed(
            quantity=request.quantity,
            setup_cost=price_config["setup_cost"],
            intervals=price_config["intervals"],
            spot_colors=request.spot_colors,
            white_ink=request.white_ink,
            white_ink_setup_cost=request.white_ink_setup_cost,
            white_ink_price_per_sheet=request.white_ink_price_per_sheet
        )
        return result
    except Exception as e:
        return JSONResponse(
            status_code=400,
            content={"error": f"计算错误: {str(e)}"}
        )

@app.post("/calculate")
async def calculate_form(request: Request, quantity: int = Form(...), spot_colors: int = Form(0), white_ink: bool = Form(True)):
    """表单提交接口：兼容原有的表单提交方式"""
    try:
        result = calculate_printing_cost_detailed(
            quantity=quantity,
            setup_cost=price_config["setup_cost"],
            intervals=price_config["intervals"],
            spot_colors=spot_colors,
            white_ink=white_ink,
            white_ink_setup_cost=white_ink_prices["setup_cost"],
            white_ink_price_per_sheet=white_ink_prices["price_per_sheet"]
        )
        return templates.TemplateResponse("index.html", {
            "request": request,
            "result": result,
            "total_price": result.total_cost
        })
    except Exception as e:
        return templates.TemplateResponse("index.html", {
            "request": request,
            "error": f"计算错误: {str(e)}"
        })

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
