from fastapi import FastAPI, Request, Form
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
import sys
import os

# 将父目录添加到 sys.path，以便导入击凸的加工费计算.py
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 尝试导入原始计算文件中的函数
try:
    from 击凸的加工费计算 import calculate_cost # 假设函数名为 calculate_cost
except ImportError as e:
    print(f"Error importing calculation module: {e}")
    calculate_cost = None # 如果导入失败，将函数设为 None

app = FastAPI()
templates = Jinja2Templates(directory="后加工工艺/压凹与击凸工艺/击凸/fastapi/templates")

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """
    Serve the index.html template.
    """
    return templates.TemplateResponse("index.html", {"request": request, "result": None})

@app.post("/calculate", response_class=HTMLResponse)
async def calculate_embossing_cost(
    request: Request,
    # 根据击凸的加工费计算.py中的函数参数定义Form字段
    # 这里需要根据实际函数参数进行调整
    # 假设函数需要参数 param1 (float) 和 param2 (int)
    param1: float = Form(...),
    param2: int = Form(...)
):
    """
    Receive data from the form, calculate the cost, and return the result to the template.
    """
    result = None
    error_message = None
    if calculate_cost:
        try:
            # 调用原始计算函数
            result = calculate_cost(param1, param2) # 根据实际函数调用方式调整
        except Exception as e:
            error_message = f"Calculation error: {e}"
    else:
        error_message = "Calculation module not loaded. Please check the import path and function name."

    return templates.TemplateResponse("index.html", {"request": request, "result": result, "error": error_message})

# 要运行此应用，请在终端中导航到当前目录并执行:
# uvicorn 后加工工艺.压凹与击凸工艺.击凸.fastapi.main:app --reload
