from fastapi import FastAPI, Request, Form, APIRouter
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
from typing import List, Dict, Union, Tuple, Annotated
import math
from collections import OrderedDict

app = FastAPI()

# 配置模板目录
templates = Jinja2Templates(directory="templates")

# 主页路由
@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    return templates.TemplateResponse("main_index.html", {"request": request})

# 局部UV计算 (算法/后加工工艺/局部UV/fastapi/main.py)
uv_router = APIRouter(prefix="/uv")

UV_CONFIG = {
    "startup_fee": 100.00,
    "minimum_cost": 260.00,
    "material_price_per_sqm": 4.50,
    "price_tiers": {
        (1, 1000): 0.20,
        (1001, 10000): 0.18,
        (10001, float('inf')): 0.16
    }
}

def calculate_uv_cost(print_quantity, uv_area=0, config=UV_CONFIG):
    try:
        print_quantity = int(print_quantity)
        uv_area = float(uv_area)

        if print_quantity <= 0 or uv_area < 0:
            return {"error": "输入参数必须为正数（印张数量）或非负数（UV面积）"}

        startup_fee = config["startup_fee"]
        minimum_cost = config["minimum_cost"]
        material_price = config["material_price_per_sqm"]
        price_tiers = config["price_tiers"]

        processing_cost = 0
        remaining_quantity = print_quantity

        pre_min_cost = startup_fee
        processing_details = []
        for (lower, upper), price in price_tiers.items():
            quantity_in_tier = min(remaining_quantity, upper - lower + 1)
            if quantity_in_tier > 0:
                tier_cost = quantity_in_tier * price
                processing_details.append(f"{lower}-{upper}张: {quantity_in_tier}*{price:.2f}={tier_cost:.2f}")
                pre_min_cost += tier_cost
                remaining_quantity -= quantity_in_tier

        processing_cost_before_min = pre_min_cost
        processing_cost = max(processing_cost_before_min, minimum_cost)
        processing_details.append(f"开机费+{processing_cost_before_min - startup_fee:.2f}={processing_cost_before_min:.2f}")
        processing_details.append(f"取大值({processing_cost_before_min:.2f},{minimum_cost:.2f})={processing_cost:.2f}")


        uv_material_cost = uv_area * print_quantity * material_price if uv_area > 0 else 0
        uv_material_details = []
        if uv_material_cost > 0:
             uv_material_details.append(f"{uv_area:.2f}*{print_quantity}*{material_price:.2f}={uv_material_cost:.2f}")


        total_cost = processing_cost + uv_material_cost

        return {
            "加工费": processing_cost,
            "UV用料费": uv_material_cost,
            "总费用": total_cost,
            "加工费明细": processing_details,
            "UV用料费明细": uv_material_details
        }
    except ValueError:
        return {"error": "输入参数类型错误，请检查输入是否为数字"}
    except Exception as e:
        return {"error": f"计算过程中发生错误: {e}"}

class UVInput(BaseModel):
    print_quantity: int
    uv_area: float = 0.0

@uv_router.get("/", response_class=HTMLResponse)
async def uv_root(request: Request):
    return templates.TemplateResponse("uv_index.html", {"request": request})

@uv_router.post("/calculate_uv/")
async def calculate_uv_api(input: UVInput):
    result = calculate_uv_cost(input.print_quantity, input.uv_area)
    return result

app.include_router(uv_router)

# 模切计算 (算法/后加工工艺/模切/fastapi/main.py)
die_cutting_router = APIRouter(prefix="/die_cutting")

# 配置信息（全局常量）
DC_SETUP_FEE = 90.00  # 开机费
DC_MINIMUM_CHARGE = 150.00  # 最低消费
DC_BLADE_PRICE_PER_METER = 12.00  # 刀片每米单价

# 不同数量区间的单价（使用有序字典，保持区间顺序）
DC_UNIT_PRICES = OrderedDict([
    (1000, 0.06),      # 一级单价
    (10000, 0.06),   # 二级单价
    (float('inf'), 0.05)  # 三级单价
])

def calculate_die_cutting_processing_cost(quantity):
    """使用分段叠加方式计算模切加工费。"""
    if not isinstance(quantity, (int, float)) or quantity < 0:
        return "印张数量必须为非负数。"

    processing_cost = DC_SETUP_FEE
    remaining_quantity = quantity
    details = []  # 存储每个区间的计算明细

    for upper_bound, price in DC_UNIT_PRICES.items():
        if remaining_quantity > 0:
            quantity_in_this_tier = min(remaining_quantity, upper_bound)
            tier_cost = quantity_in_this_tier * price
            processing_cost += tier_cost
            remaining_quantity -= quantity_in_this_tier
            details.append({"upper_bound": upper_bound, "price": price, "quantity_in_this_tier": quantity_in_this_tier, "tier_cost": tier_cost, "remaining_quantity": remaining_quantity}) #添加计算详情
    
    base_processing_cost = processing_cost - DC_SETUP_FEE
    processing_cost = max(processing_cost, DC_MINIMUM_CHARGE)
    return processing_cost, details, base_processing_cost


def calculate_blade_material_cost(blade_length_per_die, num_of_dies_per_sheet):
    """计算模切刀材料费。"""
    if not all(isinstance(x, (int, float)) and x >= 0 for x in [blade_length_per_die, num_of_dies_per_sheet]):
        return "刀线长度和模数必须为非负数。"

    blade_cost = blade_length_per_die * num_of_dies_per_sheet * DC_BLADE_PRICE_PER_METER
    return blade_cost


def calculate_die_cutting_total_cost(quantity, blade_length_per_die, num_of_dies_per_sheet):
    """计算模切总费用。"""
    processing_cost_result = calculate_die_cutting_processing_cost(quantity)
    if isinstance(processing_cost_result, str):
        return processing_cost_result
    processing_cost, details, base_processing_cost = processing_cost_result

    blade_cost = calculate_blade_material_cost(blade_length_per_die, num_of_dies_per_sheet)
    if isinstance(blade_cost, str):
        return blade_cost

    total_cost = processing_cost + blade_cost
    return {
        "total_cost": round(total_cost, 2),
        "processing_cost": round(processing_cost, 2),
        "blade_cost": round(blade_cost, 2),
        "details": details,
        "base_processing_cost": round(base_processing_cost, 2)
    }

@die_cutting_router.get("/", response_class=HTMLResponse)
async def die_cutting_root(request: Request):
    return templates.TemplateResponse("die_cutting_index.html", {"request": request})

@die_cutting_router.post("/calculate")
async def calculate_die_cutting_api(
    quantity: float = Form(...),
    blade_length_per_die: float = Form(...),
    num_of_dies_per_sheet: float = Form(...)
):
    result = calculate_die_cutting_total_cost(quantity, blade_length_per_die, num_of_dies_per_sheet)
    return result

app.include_router(die_cutting_router)

# 烫金工艺计算 (算法/后加工工艺/烫金工艺/fastapi/main.py)
gilding_router = APIRouter(prefix="/gilding")

# 设定值
GILDING_SETTINGS = {
    "starting_fee": 180.00,  # 开机费
    "min_consumption": 0.00,  # 最低消费
    "material_price_per_sqm": 4.00,  # 材料每平方米单价
    "mold_price_per_sqcm": 0.003,  # 模版每平方厘米单价
    "min_mold_fee": 20.00,  # 最低模版费用
    "processing_price_tiers": {  # 加工费分段价格
        1000: 0.20,
        10000: 0.18,
        float('inf'): 0.13,
    },
}

def calculate_gilding_processing_fee(print_quantity):
    """计算加工费（分段叠加）。"""
    processing_fee = 0
    previous_limit = 0
    for limit, price in sorted(GILDING_SETTINGS["processing_price_tiers"].items()):
        quantity_in_tier = max(0, min(print_quantity - previous_limit, limit - previous_limit))
        processing_fee += quantity_in_tier * price
        previous_limit = limit
        if print_quantity <= limit:
            break
    return processing_fee

def calculate_gilding_cost(print_quantity, gilding_positions):
    """计算烫金总费用。"""
    if print_quantity < 0 or not gilding_positions:
        return None

    total_cost = GILDING_SETTINGS["starting_fee"]  # 开机费

    # 1. 烫金加工费用
    processing_fee = calculate_gilding_processing_fee(print_quantity)
    total_cost += processing_fee

    # 计算每个工艺位的费用
    for pos in gilding_positions:
        w, h, m = pos.width, pos.height, pos.mold_quantity
        area = w * h

        # 2. 烫金用料费
        material_area = (w + 2) * (h + 2) / 10000
        material_fee = material_area * print_quantity * GILDING_SETTINGS["material_price_per_sqm"]
        total_cost += material_fee

        # 3. 烫金版费用
        mold_fee = max(area * m * GILDING_SETTINGS["mold_price_per_sqcm"], GILDING_SETTINGS["min_mold_fee"])
        total_cost += mold_fee

    total_cost = max(total_cost, GILDING_SETTINGS["min_consumption"])
    return total_cost

class GildingPosition(BaseModel):
    width: float
    height: float
    mold_quantity: int

class GildingRequest(BaseModel):
    print_quantity: int
    gilding_positions: List[GildingPosition]

@gilding_router.get("/", response_class=HTMLResponse)
async def gilding_root(request: Request):
    return templates.TemplateResponse("gilding_index.html", {"request": request})

@gilding_router.post("/calculate_gilding/")
async def calculate_gilding_api(request: GildingRequest):
    total_cost = calculate_gilding_cost(request.print_quantity, request.gilding_positions)
    if total_cost is None:
        return {"error": "Invalid input parameters"}
    return {"total_cost": round(total_cost, 2)}

app.include_router(gilding_router)

# 贴窗计算 (算法/后加工工艺/贴窗/fastapi/main.py)
window_patching_router = APIRouter(prefix="/window_patching")

WP_PRICING_CONFIG = {
    "tiers": {
        (0, 1000): 0.05,
        (1001, 10000): 0.05,
        (10001, float('inf')): 0.04
    },
    "fees": {
        "min_charge": 350.00,
        "startup_fee": 305.00
    },
    "materials": {
        "waste_quantity": 100
    }
}

def calculate_window_patching_processing_fee(order_quantity, config):
    """计算单面贴加工费（分段叠加式计价，区间可配置）。"""
    if not isinstance(order_quantity, int) or order_quantity < 0:
        return "订单数量必须为非负整数"

    tiers = config.get("tiers")
    if not isinstance(tiers, dict) or not tiers:
        return "配置错误：tiers 必须为非空字典"

    min_charge = config["fees"].get("min_charge")
    if not isinstance(min_charge, (int, float)):
        return "配置错误：min_charge 必须为数值类型"

    processing_fee = 0
    ordered_tiers = sorted(tiers.keys())
    remaining_quantity = order_quantity

    for lower, upper in ordered_tiers:
        if remaining_quantity <= 0:
            break

        price = tiers[(lower, upper)]
        if not isinstance(price, (int, float)):
            return "配置错误：区间价格必须为数值类型"

        quantity_in_tier = min(remaining_quantity, upper - lower + (0 if upper == float('inf') else 1))
        processing_fee += quantity_in_tier * price
        remaining_quantity -= quantity_in_tier

    return max(processing_fee, min_charge)

def calculate_window_patching_film_cost(length, width, order_quantity, film_price_per_square_meter, config):
    """计算胶片费用。"""
    if not all(isinstance(x, (int, float)) and x > 0 for x in [length, width, film_price_per_square_meter]):
        return "长度、宽度和胶片价格必须为正数"
    if not isinstance(order_quantity, int) or order_quantity < 0:
        return "订单数量必须为非负整数"

    materials = config.get("materials")
    if not materials or "waste_quantity" not in materials:
        return "配置错误：缺少 materials 信息或 waste_quantity"
    waste_quantity = materials["waste_quantity"]
    if not isinstance(waste_quantity, int):
        return "配置错误：waste_quantity 必须为整数"

    film_area = (length + 3) * (width + 3) / 10000
    film_cost = film_area * (order_quantity + waste_quantity) * film_price_per_square_meter
    return film_cost

def calculate_window_patching_total_cost(order_quantity, length, width, film_price_per_square_meter, config):
    """计算总费用。"""
    if not isinstance(film_price_per_square_meter, (int, float)):
        return "胶片价格必须为数值类型"
    fees = config.get("fees")
    if not fees or "startup_fee" not in fees:
        return "配置错误：缺少 fees 信息或 startup_fee"
    startup_fee = fees["startup_fee"]
    if not isinstance(startup_fee, (int, float)):
        return "配置错误：startup_fee 必须为数值类型"

    processing_fee = calculate_window_patching_processing_fee(order_quantity, config)
    if isinstance(processing_fee, str):
        return processing_fee

    film_cost = calculate_window_patching_film_cost(length, width, order_quantity, film_price_per_square_meter, config)
    if isinstance(film_cost, str):
        return film_cost

    total_cost = startup_fee + processing_fee + film_cost
    return total_cost

@window_patching_router.get("/", response_class=HTMLResponse)
async def window_patching_root(request: Request):
    return templates.TemplateResponse("window_patching_index.html", {"request": request})

@window_patching_router.post("/calculate")
async def calculate_window_patching_api(
    order_quantity: Annotated[int, Form()],
    length: Annotated[float, Form()],
    width: Annotated[float, Form()],
    film_price_per_square_meter: Annotated[float, Form()]
):
    total_cost = calculate_window_patching_total_cost(
        order_quantity,
        length,
        width,
        film_price_per_square_meter,
        WP_PRICING_CONFIG
    )

    if isinstance(total_cost, str):
        return {"error": total_cost}
    else:
        return {"cost": total_cost}

app.include_router(window_patching_router)

# 击凸计算 (算法/后加工工艺/压凹与击凸工艺/击凸/fastapi/main.py)
embossing_router = APIRouter(prefix="/embossing")

# 从击凸的加工费计算.py复制的计算函数和类
def calculate_tiered_price(quantity: float, tiers: List[Dict]) -> float:
    if not isinstance(quantity, (int, float)):
        raise TypeError("quantity 必须为 int 或 float 类型")
    if quantity < 0:
        raise ValueError("quantity 必须为非负数")

    if not isinstance(tiers, list):
        raise TypeError("tiers 必须为 list 类型")
    if not all(isinstance(tier, dict) and 'upper_bound' in tier and 'unit_price' in tier and isinstance(tier['unit_price'], (int, float)) for tier in tiers):
        raise ValueError("tiers 格式不正确")
    if not all(isinstance(tier.get('upper_bound'), (int, float, type(None))) for tier in tiers):
      raise TypeError("tiers 中 'upper_bound' 的值必须为 int, float 或 None")

    previous_upper_bound: Union[int, float, None] = None
    for tier in tiers:
        upper_bound = tier.get('upper_bound')
        if previous_upper_bound is not None and upper_bound is not None and previous_upper_bound >= upper_bound:
            raise ValueError("tiers 必须按 upper_bound 严格升序排列，且不能有重叠区间")
        previous_upper_bound = upper_bound

    total_price = 0.0
    remaining_quantity = quantity
    sum_of_previous_upper_bounds = 0

    for i, tier in enumerate(tiers):
        upper_bound = tier.get('upper_bound')
        unit_price = tier['unit_price']

        quantity_in_tier = min(remaining_quantity, upper_bound - (sum_of_previous_upper_bounds if upper_bound is not None and i>0 else 0) if upper_bound is not None else remaining_quantity)

        total_price += quantity_in_tier * unit_price
        remaining_quantity -= quantity_in_tier
        if upper_bound is not None:
            sum_of_previous_upper_bounds += upper_bound - (tiers[i-1].get('upper_bound',0) if i>0 else 0)

        if remaining_quantity <= 0:
            break

    return total_price

class JiTuProcessCalculator:
    def __init__(self, hit_count: int, area_list_cm2: List[float]):
        self.hit_count = hit_count
        self.area_list_cm2 = area_list_cm2

    def calculate_plate_cost(self, plate_cost_parameters: Dict) -> float:
        total_plate_cost = 0
        for area_cm2 in self.area_list_cm2:
            cost = max(area_cm2 * plate_cost_parameters['unit_price_per_cm2'], plate_cost_parameters['min_cost'])
            total_plate_cost += cost
        return total_plate_cost

    def calculate_processing_fee(self, sheet_count: int, pricing_parameters: Dict) -> float:
        """计算加工费。一个订单的一个工艺只收取一次启动费用。"""
        quantity_ranges = pricing_parameters['quantity_ranges']
        processing_fee = calculate_tiered_price(sheet_count, quantity_ranges)
        processing_fee += pricing_parameters['startup_fee']  # 只加一次启动费用
        processing_fee += pricing_parameters['hit_price'] * self.hit_count
        return processing_fee

    def calculate_total_cost(self, sheet_count: int, pricing_parameters: Dict, plate_cost_parameters: Dict) -> Tuple[float, float, float]:
        processing_fee = self.calculate_processing_fee(sheet_count, pricing_parameters)
        plate_cost = self.calculate_plate_cost(plate_cost_parameters)
        total_cost = processing_fee + plate_cost
        return processing_fee, plate_cost, total_cost

# 硬编码示例参数 (从击凸的fastapi/main.py复制)
EMBOSSING_PRICING_PARAMETERS = {
    'startup_fee': 100.00,
    'hit_price': 15.00,
    'quantity_ranges': [
        {'upper_bound': 1000, 'unit_price': 0.05},
        {'upper_bound': 10000, 'unit_price': 0.05},
        {'upper_bound': None, 'unit_price': 0.04}
    ]
}

EMBOSSING_PLATE_COST_PARAMETERS = {
    'min_cost': 35.00,
    'unit_price_per_cm2': 0.5
}

@embossing_router.get("/", response_class=HTMLResponse)
async def embossing_root(request: Request):
    return templates.TemplateResponse(
        "embossing_index.html",
        {
            "request": request,
            "processing_fee": None,
            "plate_cost": None,
            "total_cost": None,
            "error": None,
            "sheet_count": None,
            "hit_count": None,
            "area_list_cm2_str": None
        }
    )

@embossing_router.post("/calculate", response_class=HTMLResponse)
async def calculate_embossing_cost_api(
    request: Request,
    sheet_count: int = Form(...),
    hit_count: int = Form(...),
    area_list_cm2_str: str = Form(...)
):
    processing_fee = None
    plate_cost = None
    total_cost = None
    error_message = None

    try:
        area_list_cm2 = [float(area.strip()) for area in area_list_cm2_str.split(',') if area.strip()]
        
        calculator = JiTuProcessCalculator(hit_count, area_list_cm2)
        processing_fee, plate_cost, total_cost = calculator.calculate_total_cost(
            sheet_count,
            EMBOSSING_PRICING_PARAMETERS,
            EMBOSSING_PLATE_COST_PARAMETERS
        )
    except ValueError:
        error_message = "Invalid input for area list. Please enter comma-separated numbers."
    except Exception as e:
        error_message = f"Calculation error: {e}"

    return templates.TemplateResponse(
        "embossing_index.html",
        {
            "request": request,
            "processing_fee": processing_fee,
            "plate_cost": plate_cost,
            "total_cost": total_cost,
            "error": error_message,
            "sheet_count": sheet_count,
            "hit_count": hit_count,
            "area_list_cm2_str": area_list_cm2_str
        }
    )

app.include_router(embossing_router)
