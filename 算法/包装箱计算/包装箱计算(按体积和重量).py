import math

def calculate_packaging(product_length, product_width, product_thickness, product_quantity, product_material_weight, is_folding_box, max_box_weight, box_options):
    """
    计算最优的纸箱包装方案

    Args:
        product_length: 产品长度 (cm)
        product_width: 产品宽度 (cm)
        product_thickness: 产品厚度 (cm)
        product_quantity: 产品数量
        product_material_weight: 产品材质重量 (克/平方米)
        is_folding_box: 是否为折叠盒 (True/False)
        max_box_weight: 单个物流箱最大重量 (克)
        box_options:  字典列表, 每个字典包含箱子型号、长、宽、高和价格，例如：
                       [{'model': 'F5', 'length': 40, 'width': 30, 'height': 30, 'price': 5},
                        {'model': 'F7', 'length': 50, 'width': 30, 'height': 50, 'price': 7},
                        {'model': 'F8', 'length': 60, 'width': 50, 'height': 40, 'price': 8}]

    Returns:
        列表，包含每个方案的详细信息，例如：
        [{'box_model': 'F5', 'quantity_per_box': 450, 'space_utilization': 0.66, 'boxes_needed': 23, 'total_cost': 115, 'box_weight': 4.158},
         {'box_model': 'F7', 'quantity_per_box': 750, 'space_utilization': 0.528, 'boxes_needed': 14, 'total_cost': 98, 'box_weight': 6.93},
         {'box_model': 'F8', 'quantity_per_box': 1500, 'space_utilization': 0.594, 'boxes_needed': 7, 'total_cost': 56, 'box_weight': 13.86}]
    """

    product_volume = product_length * product_width * product_thickness

    # 计算产品重量
    if is_folding_box:
        product_area = product_length * product_width * 2  # 折叠盒面积 x 2
    else:
        product_area = product_length * product_width
    product_weight = product_material_weight * product_area / 10000  # 克

    total_product_volume = product_volume * product_quantity

    results = []

    for box in box_options:
        # 计算每个方向上可以放多少个产品
        count_l1 = math.floor(box['length'] / product_length)
        count_w1 = math.floor(box['width'] / product_width)
        count_h1 = math.floor(box['height'] / product_thickness)

        count_l2 = math.floor(box['length'] / product_width)
        count_w2 = math.floor(box['width'] / product_length)
        count_h2 = math.floor(box['height'] / product_thickness)

        # 计算两种摆放方式下的装箱数量和空间利用率
        quantity_per_box1 = count_l1 * count_w1 * count_h1
        if quantity_per_box1 > 0:
            space_utilization1 = (quantity_per_box1 * product_volume) / (box['length'] * box['width'] * box['height'])
        else:
            space_utilization1 = 0

        quantity_per_box2 = count_l2 * count_w2 * count_h2
        if quantity_per_box2 > 0:
            space_utilization2 = (quantity_per_box2 * product_volume) / (box['length'] * box['width'] * box['height'])
        else:
            space_utilization2 = 0

        # 选择装箱数量更多的方案
        quantity_per_box = max(quantity_per_box1, quantity_per_box2)
        space_utilization = max(space_utilization1, space_utilization2)

        # 考虑重量限制的装箱数量
        quantity_per_box_weight_limit = math.floor(max_box_weight / product_weight)
        quantity_per_box = min(quantity_per_box, quantity_per_box_weight_limit)

        # 计算所需箱子数量、总成本和每箱重量
        boxes_needed = math.ceil(product_quantity / quantity_per_box)
        total_cost = boxes_needed * box['price']
        box_weight = quantity_per_box * product_weight / 1000  # 千克

        results.append({
            'box_model': box['model'],
            'quantity_per_box': quantity_per_box,
            'space_utilization': round(space_utilization, 3),
            'boxes_needed': boxes_needed,
            'total_cost': total_cost,
            'box_weight': round(box_weight, 3)
        })

    return results


# 产品信息
product_length = 20  # cm
product_width = 15  # cm
product_thickness = 0.5  # cm
product_quantity = 3000
product_material_weight = 350  # 克/平方米
is_folding_box = True  # 是否为折叠盒
max_box_weight = 15000 # 克

# 物流箱选项
box_options = [
    {'model': 'F5', 'length': 40, 'width': 30, 'height': 30, 'price': 5},
    {'model': 'F7', 'length': 50, 'width': 30, 'height': 50, 'price': 7},
    {'model': 'F8', 'length': 60, 'width': 50, 'height': 40, 'price': 8}
]

# 计算产品重量
if is_folding_box:
    product_area = product_length * product_width * 2  # 折叠盒面积 x 2
else:
    product_area = product_length * product_width
product_weight = product_material_weight * product_area / 10000  # 克

print(f"单个产品重量: {product_weight:.2f} 克")  # 打印单个产品重量，保留两位小数

# 计算包装方案
results = calculate_packaging(product_length, product_width, product_thickness, product_quantity, product_material_weight, is_folding_box, max_box_weight, box_options)

# 打印结果
for result in results:
    print(f"箱型: {result['box_model']}")
    print(f"每箱数量: {result['quantity_per_box']}")
    print(f"空间利用率: {result['space_utilization']:.3f}")
    print(f"所需箱数: {result['boxes_needed']}")
    print(f"总成本: {result['total_cost']} 元")
    print(f"每箱重量: {result['box_weight']} 千克")
    print("-" * 20)