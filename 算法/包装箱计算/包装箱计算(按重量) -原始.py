def calculate_boxes(material_weight, box_capacity):
    """计算需要的箱子数量
    参数:
        material_weight: 总重量 (kg)
        box_capacity: 单个箱子的容量 (kg)
    返回:
        需要的箱子数量 (至少1个)
    """
    return max(1, (material_weight + box_capacity - 1) // box_capacity)


def recommend_box(item_weight, box_capacities):
    """推荐最优的物流箱使用方案
    参数:
        item_weight: 总重量 (kg)
        box_capacities: 可用的箱子容量列表 (kg)
    返回:
        results: 所有方案的详细结果
        optimal_box: 最优方案
    """
    results = []
    for capacity in box_capacities:
        # 计算需要的箱子数量
        num_boxes = calculate_boxes(item_weight, capacity)
        # 计算每箱物品数量
        items_per_box = item_count // num_boxes
        # 计算容量利用率
        utilization = (item_weight / (capacity * num_boxes)) * 100 if capacity > 0 else 0
        # 保存结果
        results.append({
            "容量 (kg)": capacity,
            "箱子数量": num_boxes,
            "每箱物品数量": items_per_box,
            "容量利用率 (%)": round(utilization, 2)
        })

    # 选择最优方案：箱子数量最少，利用率最高
    optimal_box = min(results, key=lambda x: (x["箱子数量"], -x["容量利用率 (%)"]))
    return results, optimal_box


# 示例数据
item_weight = 0.15  # 单个物品重量，单位：kg
item_count = 10000  # 物品数量
total_weight = item_weight * item_count  # 总重量
box_capacities = [10, 20, 30, 40]  # 可用的箱子容量，单位：kg

# 计算并输出结果
results, optimal_box = recommend_box(total_weight, box_capacities)
print("物流箱使用方案：")
for result in results:
    print(f"容量: {result['容量 (kg)']}kg, 箱子数量: {int(result['箱子数量'])}, 每箱物品数量: {result['每箱物品数量']}, 容量利用率: {result['容量利用率 (%)']}%")

print("\n推荐最优方案：")
print(f"容量: {optimal_box['容量 (kg)']}kg, 箱子数量: {optimal_box['箱子数量']}, 每箱物品数量: {optimal_box['每箱物品数量']}, 容量利用率: {optimal_box['容量利用率 (%)']}%")
