def calculate_boxes_by_weight(material_weight, box_weight_capacity):
    """根据重量计算需要的箱子数量
    参数:
        material_weight: 总重量 (kg)
        box_weight_capacity: 单个箱子的重量容量 (kg)
    返回:
        需要的箱子数量 (至少1个)
    """
    return max(1, (material_weight + box_weight_capacity - 1) // box_weight_capacity)


def calculate_boxes_by_volume(material_volume, box_volume_capacity):
    """根据体积计算需要的箱子数量
    参数:
        material_volume: 总体积 (m³)
        box_volume_capacity: 单个箱子的体积容量 (m³)
    返回:
        需要的箱子数量 (至少1个)
    """
    return max(1, (material_volume + box_volume_capacity - 1) // box_volume_capacity)


def calculate_boxes(material_weight, box_weight_capacity, material_volume, box_volume_capacity):
    """同时考虑重量和体积计算需要的箱子数量
    参数:
        material_weight: 总重量 (kg)
        box_weight_capacity: 单个箱子的重量容量 (kg)
        material_volume: 总体积 (m³)
        box_volume_capacity: 单个箱子的体积容量 (m³)
    返回:
        需要的箱子数量 (至少1个)
    """
    # 分别计算基于重量和体积的箱子数量
    boxes_by_weight = calculate_boxes_by_weight(material_weight, box_weight_capacity)
    boxes_by_volume = calculate_boxes_by_volume(material_volume, box_volume_capacity)

    # 取较大值，确保同时满足重量和体积约束
    return max(boxes_by_weight, boxes_by_volume)


def calculate_logistics_cost(weight, volume, box_count, logistics_params):
    """计算物流成本
    参数:
        weight: 总重量 (kg)
        volume: 总体积 (m³)
        box_count: 箱子数量
        logistics_params: 物流参数字典，包含价格系数
    返回:
        总物流成本
    """
    # 基础费用（每个箱子的基本费用）
    base_cost = box_count * logistics_params.get("base_fee", 0)

    # 重量费用（按总重量计费）
    weight_cost = weight * logistics_params.get("weight_price_per_kg", 0)

    # 体积费用（按总体积计费）
    volume_cost = volume * logistics_params.get("volume_price_per_m3", 0)

    # 计算总成本（取重量计费和体积计费的较大值，加上基础费用）
    total_cost = base_cost + max(weight_cost, volume_cost)

    # 返回计费明细和总成本
    return {
        "基础费用": round(base_cost, 2),
        "重量费用": round(weight_cost, 2),
        "体积费用": round(volume_cost, 2),
        "计费方式": "重量计费" if weight_cost > volume_cost else "体积计费",
        "总物流成本": round(total_cost, 2)
    }


def recommend_box(item_weight, item_volume, item_count, box_options, logistics_params):
    """推荐最优的物流箱使用方案，同时考虑重量、体积和物流成本
    参数:
        item_weight: 单个物品重量 (kg)
        item_volume: 单个物品体积 (m³)
        item_count: 物品数量
        box_options: 可用的箱子选项列表，每个选项是一个字典，包含重量容量、体积容量和箱子成本
                    例如：[{"weight_capacity": 10, "volume_capacity": 0.5, "name": "小箱", "box_cost": 5}, ...]
        logistics_params: 物流参数字典，包含各种价格系数
    返回:
        results: 所有方案的详细结果
        optimal_box: 最优方案
    """
    total_weight = item_weight * item_count  # 总重量
    total_volume = item_volume * item_count  # 总体积

    results = []
    for box in box_options:
        weight_capacity = box["weight_capacity"]
        volume_capacity = box["volume_capacity"]
        box_name = box.get("name", f"{weight_capacity}kg/{volume_capacity}m³")
        box_cost = box.get("box_cost", 0)  # 单个箱子的成本

        # 计算需要的箱子数量，同时考虑重量和体积约束
        num_boxes = calculate_boxes(total_weight, weight_capacity, total_volume, volume_capacity)

        # 计算每箱物品数量
        items_per_box = item_count // num_boxes if num_boxes > 0 else 0

        # 计算重量和体积的容量利用率
        weight_utilization = (total_weight / (weight_capacity * num_boxes)) * 100 if weight_capacity > 0 and num_boxes > 0 else 0
        volume_utilization = (total_volume / (volume_capacity * num_boxes)) * 100 if volume_capacity > 0 and num_boxes > 0 else 0

        # 计算综合利用率（取重量和体积利用率的平均值）
        overall_utilization = (weight_utilization + volume_utilization) / 2

        # 计算箱子总成本
        total_box_cost = box_cost * num_boxes

        # 计算物流成本
        logistics_cost_details = calculate_logistics_cost(
            total_weight,
            total_volume,
            num_boxes,
            logistics_params
        )

        # 计算总成本（箱子成本 + 物流成本）
        total_cost = total_box_cost + logistics_cost_details["总物流成本"]

        # 保存结果
        results.append({
            "箱子名称": box_name,
            "重量容量 (kg)": weight_capacity,
            "体积容量 (m³)": volume_capacity,
            "箱子数量": num_boxes,
            "每箱物品数量": items_per_box,
            "重量利用率 (%)": round(weight_utilization, 2),
            "体积利用率 (%)": round(volume_utilization, 2),
            "综合利用率 (%)": round(overall_utilization, 2),
            "限制因素": "重量" if weight_utilization > volume_utilization else "体积",
            "箱子成本": round(total_box_cost, 2),
            "物流成本": logistics_cost_details["总物流成本"],
            "物流计费方式": logistics_cost_details["计费方式"],
            "总成本": round(total_cost, 2),
            "物流成本明细": logistics_cost_details
        })

    # 选择最优方案：总成本最低
    if results:
        optimal_box = min(results, key=lambda x: x["总成本"])
    else:
        optimal_box = None

    return results, optimal_box


# 示例数据
item_weight = 0.15  # 单个物品重量，单位：kg
item_volume = 0.0005  # 单个物品体积，单位：m³
item_count = 10000  # 物品数量

# 定义可用的箱子选项，包含重量容量、体积容量和箱子成本
box_options = [
    {"name": "小箱", "weight_capacity": 10, "volume_capacity": 0.05, "box_cost": 5},
    {"name": "中箱", "weight_capacity": 20, "volume_capacity": 0.1, "box_cost": 8},
    {"name": "大箱", "weight_capacity": 30, "volume_capacity": 0.2, "box_cost": 12},
    {"name": "特大箱", "weight_capacity": 40, "volume_capacity": 0.3, "box_cost": 15}
]

# 定义物流参数
logistics_params = {
    "base_fee": 2,  # 每箱基础费用
    "weight_price_per_kg": 5,  # 每公斤重量价格
    "volume_price_per_m3": 1000  # 每立方米体积价格
}

# 计算并输出结果
results, optimal_box = recommend_box(item_weight, item_volume, item_count, box_options, logistics_params)

print("物流箱使用方案（考虑重量、体积和物流成本）：")
print("-" * 120)
print(f"{'箱子名称':<8} {'箱子数量':<8} {'重量容量':<10} {'体积容量':<10} {'重量利用率':<10} {'体积利用率':<10} {'箱子成本':<10} {'物流成本':<10} {'物流计费':<10} {'总成本':<10} {'限制因素':<8}")
print("-" * 120)

for result in results:
    print(f"{result['箱子名称']:<8} {int(result['箱子数量']):<8} {result['重量容量 (kg)']:<10} {result['体积容量 (m³)']:<10} "
          f"{result['重量利用率 (%)']:<10.2f} {result['体积利用率 (%)']:<10.2f} {result['箱子成本']:<10.2f} "
          f"{result['物流成本']:<10.2f} {result['物流计费方式']:<10} {result['总成本']:<10.2f} {result['限制因素']:<8}")

if optimal_box:
    print("\n最优包装方案：")
    print(f"✅ 最优箱子种类: {optimal_box['箱子名称']}")
    print(f"✅ 所需箱子数量: {int(optimal_box['箱子数量'])}个")
    print(f"✅ 每箱装载: {optimal_box['每箱物品数量']}个物品")
    print(f"✅ 总成本: {optimal_box['总成本']}元 (箱子成本: {optimal_box['箱子成本']}元, 物流成本: {optimal_box['物流成本']}元)")
    print(f"✅ 物流计费方式: {optimal_box['物流计费方式']}")
    print(f"✅ 限制因素: {optimal_box['限制因素']}")

    # 物流成本明细
    print("\n物流成本明细:")
    cost_details = optimal_box['物流成本明细']
    print(f"  - 基础费用: {cost_details['基础费用']}元")
    print(f"  - 重量费用: {cost_details['重量费用']}元")
    print(f"  - 体积费用: {cost_details['体积费用']}元")
else:
    print("\n没有找到合适的方案")
