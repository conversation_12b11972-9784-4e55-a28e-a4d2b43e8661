import math
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel # Import BaseModel

# from fastapi.templating import Jinja2Templates # Comment out Jinja2Templates

app = FastAPI()

# Mount static files directory
app.mount("/static", StaticFiles(directory="static"), name="static")

# Configure templates directory - No longer needed
# templates = Jinja2Templates(directory="/Users/<USER>/Documents/code-project/pack相关/模数计算/fastapi/templates")

# Define a Pydantic model for the request body
class CalculationInput(BaseModel):
    eqp_len: float
    eqp_wid: float
    prod_len: float
    prod_wid: float
    bleed: float
    margin: float

def calc_layout(eqp_len: float, eqp_wid: float, prod_len: float, prod_wid: float, bleed: float, margin: float):
    """
    计算排版，输出最大模数、方式、详细排版信息和所需纸张尺寸，考虑出血和余量。

    Args:
        eqp_len: 设备长.
        eqp_wid: 设备宽.
        prod_len: 成品长.
        prod_wid: 成品宽.
        bleed: 出血宽度 (mm).
        margin: 余量 (mm).

    Returns:
        一个包含最大模数值、排版方式、详细排版信息和所需纸张尺寸的字典.
    """

    # 考虑出血，更新成品尺寸
    prod_len += 2 * bleed
    prod_wid += 2 * bleed

    # 确保成品的长 >= 宽
    if prod_len < prod_wid:
        prod_len, prod_wid = prod_wid, prod_len

    num_hor = math.floor(eqp_len / prod_len) * math.floor(eqp_wid / prod_wid)
    num_ver = math.floor(eqp_len / prod_wid) * math.floor(eqp_wid / prod_len)

    if num_hor > num_ver:
        best = "横向"
        max_num = num_hor
        paper_len = prod_len * math.floor(eqp_len / prod_len)
        paper_wid = prod_wid * math.floor(eqp_wid / prod_wid)
    elif num_hor < num_ver:
        best = "纵向"
        max_num = num_ver
        paper_len = prod_wid * math.floor(eqp_len / prod_wid)
        paper_wid = prod_len * math.floor(eqp_wid / prod_len)
    else:
        best = "横向或纵向"
        max_num = num_hor
        paper_len = prod_len * math.floor(eqp_len / prod_len)
        paper_wid = prod_wid * math.floor(eqp_wid / prod_wid)

    # 确保纸张尺寸的长 >= 宽
    if paper_len < paper_wid:
        paper_len, paper_wid = paper_wid, paper_len

    # 添加余量
    paper_len += 2 * margin
    paper_wid += 2 * margin

    result_str = f"最大模数: {max_num}, 方式: {best}\n"
    result_str += f"横向排版: {num_hor} 个\n"
    result_str += f"纵向排版: {num_ver} 个\n"
    result_str += f"所需纸张尺寸(含{margin}mm余量): {paper_len} x {paper_wid}"

    return {
        "max_num": max_num,
        "best_method": best,
        "horizontal_layout": num_hor,
        "vertical_layout": num_ver,
        "paper_length": paper_len,
        "paper_width": paper_wid,
        "detailed_result": result_str
    }

@app.get("/", response_class=HTMLResponse)
async def read_root():
    html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>模数计算</title>
    <style>
        body { font-family: sans-serif; line-height: 1.6; margin: 20px; }
        label { display: block; margin-bottom: 5px; }
        input { margin-bottom: 10px; padding: 8px; }
        button { padding: 10px 15px; background-color: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        #results { margin-top: 20px; border-top: 1px solid #ccc; padding-top: 15px; }
    </style>
</head>
<body>
    <h1>模数计算</h1>

    <form id="calculationForm">
        <label for="eqp_len">设备长 (mm):</label>
        <input type="number" id="eqp_len" name="eqp_len" required><br>

        <label for="eqp_wid">设备宽 (mm):</label>
        <input type="number" id="eqp_wid" name="eqp_wid" required><br>

        <label for="prod_len">成品长 (mm):</label>
        <input type="number" id="prod_len" name="prod_len" required><br>

        <label for="prod_wid">成品宽 (mm):</label>
        <input type="number" id="prod_wid" name="prod_wid" required><br>

        <label for="bleed">出血宽度 (mm):</label>
        <input type="number" id="bleed" name="bleed" value="3" required><br>

        <label for="margin">余量 (mm):</label>
        <input type="number" id="margin" name="margin" value="10" required><br>

        <button type="submit">计算</button>
    </form>

    <div id="results">
        <h2>计算结果:</h2>
        <p id="max_num"></p>
        <p id="best_method"></p>
        <p id="horizontal_layout"></p>
        <p id="vertical_layout"></p>
        <p id="paper_size"></p>
        <pre id="detailed_result"></pre>
    </div>

    <script>
        document.getElementById('calculationForm').addEventListener('submit', async function(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());

            // Convert string values to numbers
            for (const key in data) {
                data[key] = parseFloat(data[key]);
            }

            try {
                const response = await fetch('/calculate/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                document.getElementById('max_num').textContent = `最大模数: ${result.max_num}`;
                document.getElementById('best_method').textContent = `方式: ${result.best_method}`;
                document.getElementById('horizontal_layout').textContent = `横向排版: ${result.horizontal_layout} 个`;
                document.getElementById('vertical_layout').textContent = `纵向排版: ${result.vertical_layout} 个`;
                document.getElementById('paper_size').textContent = `所需纸张尺寸(含余量): ${result.paper_length} x ${result.paper_width}`;
                document.getElementById('detailed_result').textContent = result.detailed_result;

            } catch (error) {
                console.error('Error:', error);
                document.getElementById('results').innerHTML = `<h2>计算结果:</h2><p style="color: red;">计算出错: ${error.message}</p>`;
            }
        });
    </script>
</body>
</html>
"""
    return HTMLResponse(content=html_content, status_code=200)

@app.post("/calculate/")
async def calculate_layout(input_data: CalculationInput): # Accept Pydantic model as request body
    result = calc_layout(
        input_data.eqp_len,
        input_data.eqp_wid,
        input_data.prod_len,
        input_data.prod_wid,
        input_data.bleed,
        input_data.margin
    )
    return result
