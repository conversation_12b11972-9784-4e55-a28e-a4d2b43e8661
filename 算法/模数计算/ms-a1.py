import math

def calc_layout(eqp_len, eqp_wid, prod_len, prod_wid):
  """
  计算排版，输出最大模数、方式和详细排版信息。

  Args:
    eqp_len: 设备长.
    eqp_wid: 设备宽.
    prod_len: 成品长.
    prod_wid: 成品宽.

  Returns:
    一个包含最大模数值和排版方式的元组.
  """

  num_hor = math.floor(eqp_len / prod_len) * math.floor(eqp_wid / prod_wid)
  num_ver = math.floor(eqp_len / prod_wid) * math.floor(eqp_wid / prod_len)

  if num_hor > num_ver:
    best = "横向"
    max_num = num_hor
  elif num_hor < num_ver:
    best = "纵向"
    max_num = num_ver
  else:
    best = "横向或纵向"
    max_num = num_hor

  result_str = f"最大模数: {max_num}, 方式: {best}\n"
  result_str += f"横向排版: {num_hor} 个\n"
  result_str += f"纵向排版: {num_ver} 个"

  return max_num, result_str


# 示例
eqp_len = 1000  # 设备长
eqp_wid = 700   # 设备宽
prod_len = 210  # 成品长
prod_wid = 285  # 成品宽

max_num, result_str = calc_layout(eqp_len, eqp_wid, prod_len, prod_wid)

print(f"最大模数: {max_num}\n")  # 仅输出最大模数，添加换行
print(result_str)  # 输出详细排版信息