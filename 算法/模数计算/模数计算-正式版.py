import math

def calc_layout(eqp_len, eqp_wid, prod_len, prod_wid, bleed, margin):
    """
    计算排版，输出最大模数、方式、详细排版信息和所需纸张尺寸，考虑出血和余量。

    Args:
        eqp_len: 设备长.
        eqp_wid: 设备宽.
        prod_len: 成品长.
        prod_wid: 成品宽.
        bleed: 出血宽度 (mm).
        margin: 余量 (mm).

    Returns:
        一个包含最大模数值、排版方式、详细排版信息和所需纸张尺寸的元组.
    """

    # 考虑出血，更新成品尺寸
    prod_len += 2 * bleed
    prod_wid += 2 * bleed

    # 确保成品的长 >= 宽
    if prod_len < prod_wid:
        prod_len, prod_wid = prod_wid, prod_len

    num_hor = math.floor(eqp_len / prod_len) * math.floor(eqp_wid / prod_wid)
    num_ver = math.floor(eqp_len / prod_wid) * math.floor(eqp_wid / prod_len)

    if num_hor > num_ver:
        best = "横向"
        max_num = num_hor
        paper_len = prod_len * math.floor(eqp_len / prod_len)
        paper_wid = prod_wid * math.floor(eqp_wid / prod_wid)
    elif num_hor < num_ver:
        best = "纵向"
        max_num = num_ver
        paper_len = prod_wid * math.floor(eqp_len / prod_wid)
        paper_wid = prod_len * math.floor(eqp_wid / prod_len)
    else:
        best = "横向或纵向"
        max_num = num_hor
        paper_len = prod_len * math.floor(eqp_len / prod_len)
        paper_wid = prod_wid * math.floor(eqp_wid / prod_wid)

    # 确保纸张尺寸的长 >= 宽
    if paper_len < paper_wid:
        paper_len, paper_wid = paper_wid, paper_len

    # 添加余量
    paper_len += 2 * margin
    paper_wid += 2 * margin

    result_str = f"最大模数: {max_num}, 方式: {best}\n"
    result_str += f"横向排版: {num_hor} 个\n"
    result_str += f"纵向排版: {num_ver} 个\n"
    result_str += f"所需纸张尺寸(含{margin}mm余量): {paper_len} x {paper_wid}"

    return max_num, result_str, paper_len, paper_wid


# 示例
eqp_len = 1000  # 设备长
eqp_wid = 700   # 设备宽
prod_len = 456  # 成品长
prod_wid = 290  # 成品宽
bleed = 3  # 出血宽度 3mm
margin = 10 # 余量 10mm

max_num, result_str, paper_len, paper_wid = calc_layout(eqp_len, eqp_wid, prod_len, prod_wid, bleed, margin)

print(f"最大模数: {max_num}")  # 仅输出最大模数
print(result_str)  # 输出详细排版信息

# 修改余量再次测试
# margin = 20
# max_num, result_str, paper_len, paper_wid = calc_layout(eqp_len, eqp_wid, prod_len, prod_wid, bleed, margin)
# print("\n修改余量后的结果：")
# print(result_str)