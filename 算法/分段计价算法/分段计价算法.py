from typing import List, Dict, Union

def calculate_tiered_price(quantity: float, tiers: List[Dict]) -> float:
    """根据分段价格计算总价。

    Args:
        quantity: 产品数量（必须为非负数）。
        tiers: 分段价格列表，每个元素是一个字典，包含 'upper_bound' 和 'unit_price'。
               'upper_bound' 为 None 表示最后一个区间无上限。tiers 列表必须按 upper_bound 严格升序排列，且不能有重叠区间。

    Returns:
        总价。

    Raises:
        TypeError: 如果 quantity 或 tiers 类型不正确。
        ValueError: 如果 quantity 为负数或 tiers 格式不正确或区间定义错误。
    """

    if not isinstance(quantity, (int, float)):
        raise TypeError("quantity 必须为 int 或 float 类型")
    if quantity < 0:
        raise ValueError("quantity 必须为非负数")

    if not isinstance(tiers, list):
        raise TypeError("tiers 必须为 list 类型")
    if not all(isinstance(tier, dict) and 'upper_bound' in tier and 'unit_price' in tier and isinstance(tier['unit_price'], (int, float)) for tier in tiers):
        raise ValueError("tiers 格式不正确")
    if not all(isinstance(tier.get('upper_bound'), (int, float, type(None))) for tier in tiers):
      raise TypeError("tiers 中 'upper_bound' 的值必须为 int, float 或 None")

    # 验证区间是否按严格升序排列且无重叠
    previous_upper_bound: Union[int, float, None] = None
    for tier in tiers:
        upper_bound = tier.get('upper_bound')
        if previous_upper_bound is not None and upper_bound is not None and previous_upper_bound >= upper_bound:
            raise ValueError("tiers 必须按 upper_bound 严格升序排列，且不能有重叠区间")
        previous_upper_bound = upper_bound

    total_price = 0.0
    remaining_quantity = quantity
    sum_of_previous_upper_bounds = 0

    for i, tier in enumerate(tiers):
        upper_bound = tier.get('upper_bound')
        unit_price = tier['unit_price']

        # 核心逻辑：修正后的计算方式
        quantity_in_tier = min(remaining_quantity, upper_bound - (sum_of_previous_upper_bounds if upper_bound is not None and i>0 else 0) if upper_bound is not None else remaining_quantity)

        total_price += quantity_in_tier * unit_price
        remaining_quantity -= quantity_in_tier
        if upper_bound is not None:
            sum_of_previous_upper_bounds += upper_bound - (tiers[i-1].get('upper_bound',0) if i>0 else 0)

        if remaining_quantity <= 0:
            break

    return total_price

# 示例数据和测试用例
test_cases = [
    {"quantity": 500, "tiers": [{'upper_bound': 1000, 'unit_price': 0.05}, {'upper_bound': None, 'unit_price': 0.04}], "expected": 25.0},
    {"quantity": 1500, "tiers": [{'upper_bound': 1000, 'unit_price': 0.05}, {'upper_bound': None, 'unit_price': 0.04}], "expected": 70.0},
    {"quantity": 18001, "tiers": [{'upper_bound': 1000, 'unit_price': 0.05}, {'upper_bound': 10000, 'unit_price': 0.05}, {'upper_bound': None, 'unit_price': 0.04}], "expected": 820.04},
    {"quantity": 0, "tiers": [{'upper_bound': 1000, 'unit_price': 0.05}, {'upper_bound': None, 'unit_price': 0.04}], "expected": 0.0},
    {"quantity": 10000, "tiers": [{'upper_bound': 1000, 'unit_price': 0.05}, {'upper_bound': 10000, 'unit_price': 0.05}, {'upper_bound': None, 'unit_price': 0.04}], "expected": 500.0},
    {"quantity": 10001, "tiers": [{'upper_bound': 1000, 'unit_price': 0.05}, {'upper_bound': 10000, 'unit_price': 0.05}, {'upper_bound': None, 'unit_price': 0.04}], "expected": 500.04},
    {"quantity": 2500, "tiers": [{'upper_bound': 1000, 'unit_price': 0.1}, {'upper_bound': 2000, 'unit_price': 0.08}, {'upper_bound': None, 'unit_price': 0.06}], "expected": 180.0}, #新增测试用例
]

# 运行测试用例并输出结果
for i, case in enumerate(test_cases):
    quantity = case["quantity"]
    tiers = case["tiers"]
    expected = case["expected"]
    result = calculate_tiered_price(quantity, tiers)
    print(f"测试用例 {i+1}:")
    print(f"  数量: {quantity}")
    print(f"  区间: {tiers}")
    print(f"  预期结果: {expected}")
    print(f"  实际结果: {result}")
    if abs(result - expected) < 1e-6: # 使用容差比较浮点数
        print("  测试通过!")
    else:
        print("  测试失败!")
    print("-" * 20)
