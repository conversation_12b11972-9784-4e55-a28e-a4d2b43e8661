from typing import List, Dict, Union

def calculate_tiered_price(quantity: float, tiers: List[Dict]) -> float:
    """根据分段价格计算总价。

    Args:
        quantity: 产品数量（必须为非负数）。
        tiers: 分段价格列表，每个元素是一个字典，包含 'upper_bound' 和 'unit_price'。
               'upper_bound' 为 None 表示最后一个区间无上限。tiers 列表必须按 upper_bound 严格升序排列，且不能有重叠区间。

    Returns:
        总价。

    Raises:
        TypeError: 如果 quantity 或 tiers 类型不正确。
        ValueError: 如果 quantity 为负数或 tiers 格式不正确或区间定义错误。
    """

    if not isinstance(quantity, (int, float)):
        raise TypeError("quantity 必须为 int 或 float 类型")
    if quantity < 0:
        raise ValueError("quantity 必须为非负数")

    if not isinstance(tiers, list):
        raise TypeError("tiers 必须为 list 类型")
    if not all(isinstance(tier, dict) and 'upper_bound' in tier and 'unit_price' in tier and isinstance(tier['unit_price'], (int, float)) for tier in tiers):
        raise ValueError("tiers 格式不正确")
    if not all(isinstance(tier.get('upper_bound'), (int, float, type(None))) for tier in tiers):
      raise TypeError("tiers 中 'upper_bound' 的值必须为 int, float 或 None")

    # 验证区间是否按严格升序排列且无重叠
    previous_upper_bound: Union[int, float, None] = None
    for tier in tiers:
        upper_bound = tier.get('upper_bound')
        if previous_upper_bound is not None and upper_bound is not None and previous_upper_bound >= upper_bound:
            raise ValueError("tiers 必须按 upper_bound 严格升序排列，且不能有重叠区间")
        # 检查最后一个区间的 upper_bound 是否为 None
        if tiers and tiers[-1].get('upper_bound') is not None:
             raise ValueError("最后一个区间的 upper_bound 必须为 None")
        previous_upper_bound = upper_bound


    total_price = 0.0
    previous_tier_upper_bound = 0.0

    for tier in tiers:
        current_tier_upper_bound = tier.get('upper_bound')
        unit_price = tier['unit_price']

        tier_lower_bound = previous_tier_upper_bound

        # 如果数量小于等于当前区间的下限，则无需再计算后续区间
        if quantity <= tier_lower_bound:
            break

        # 计算当前区间能容纳的最大数量
        if current_tier_upper_bound is None:
            tier_capacity = float('inf')
        else:
            tier_capacity = current_tier_upper_bound - tier_lower_bound
            if tier_capacity < 0: # 进一步校验，理论上不应发生，因为前面已校验排序
                 raise ValueError(f"区间定义错误: 区间下限 {tier_lower_bound} 大于等于上限 {current_tier_upper_bound}")


        # 计算实际落入当前区间的数量
        quantity_in_tier = min(quantity - tier_lower_bound, tier_capacity)

        # 累加价格
        total_price += quantity_in_tier * unit_price

        # 更新上一个区间的上限，为下一次迭代准备
        if current_tier_upper_bound is not None:
            previous_tier_upper_bound = current_tier_upper_bound
        else:
            # 如果当前是最后一个无限区间，计算完即可退出
            break

        # 如果总数量已全部分配完毕，提前退出
        if quantity <= current_tier_upper_bound:
             break


    return total_price

# 示例数据和测试用例
test_cases = [
    {"quantity": 500, "tiers": [{'upper_bound': 1000, 'unit_price': 0.05}, {'upper_bound': None, 'unit_price': 0.04}], "expected": 25.0},
    {"quantity": 1500, "tiers": [{'upper_bound': 1000, 'unit_price': 0.05}, {'upper_bound': None, 'unit_price': 0.04}], "expected": 70.0},
    {"quantity": 18001, "tiers": [{'upper_bound': 1000, 'unit_price': 0.05}, {'upper_bound': 10000, 'unit_price': 0.05}, {'upper_bound': None, 'unit_price': 0.04}], "expected": 820.04},
    {"quantity": 0, "tiers": [{'upper_bound': 1000, 'unit_price': 0.05}, {'upper_bound': None, 'unit_price': 0.04}], "expected": 0.0},
    {"quantity": 10000, "tiers": [{'upper_bound': 1000, 'unit_price': 0.05}, {'upper_bound': 10000, 'unit_price': 0.05}, {'upper_bound': None, 'unit_price': 0.04}], "expected": 500.0},
    {"quantity": 10001, "tiers": [{'upper_bound': 1000, 'unit_price': 0.05}, {'upper_bound': 10000, 'unit_price': 0.05}, {'upper_bound': None, 'unit_price': 0.04}], "expected": 500.04},
    {"quantity": 2500, "tiers": [{'upper_bound': 1000, 'unit_price': 0.1}, {'upper_bound': 2000, 'unit_price': 0.08}, {'upper_bound': None, 'unit_price': 0.06}], "expected": 180.0}, #新增测试用例
]

# 运行测试用例并输出结果
for i, case in enumerate(test_cases):
    quantity = case["quantity"]
    tiers = case["tiers"]
    expected = case["expected"]
    result = calculate_tiered_price(quantity, tiers)
    print(f"测试用例 {i+1}:")
    print(f"  数量: {quantity}")
    print(f"  区间: {tiers}")
    print(f"  预期结果: {expected}")
    print(f"  实际结果: {result}")
    if abs(result - expected) < 1e-6: # 使用容差比较浮点数
        print("  测试通过!")
    else:
        print("  测试失败!")
    print("-" * 20)
