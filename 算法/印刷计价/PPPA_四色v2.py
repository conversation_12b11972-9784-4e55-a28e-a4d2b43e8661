def calculate_cmyk_printing_cost(quantity, setup_cost, intervals):
    """
    计算四色印刷成本，并打印计算明细。

    Args:
        quantity: 印刷数量。
        setup_cost: 开机费。
        intervals: 价格区间列表。

    Returns:
        总费用。
        如果输入无效，则返回错误信息。
    """

    print(f"开始计算，印刷数量：{quantity}，开机费：{setup_cost}") # 打印初始信息

    if not isinstance(quantity, (int, float)) or quantity < 0:
        return "印刷数量必须为非负数"

    if not isinstance(setup_cost, (int, float)) or setup_cost < 0:
        return "开机费必须为非负数"

    if not isinstance(intervals, list) or not all(isinstance(interval, dict) and "上限" in interval and "单价" in interval for interval in intervals):
      return "价格区间格式不正确，应为包含'上限'和'单价'键的字典列表"

    total_cost = setup_cost
    print(f"初始总费用（仅包含开机费）：{total_cost}")

    for i, interval in enumerate(intervals):
        upper_limit = interval["上限"]
        price = interval["单价"]

        if not isinstance(upper_limit, (int, float)) or upper_limit < 0:
            return f"第{i+1}个区间的上限必须为非负数"
        if not isinstance(price, (int, float)) or price < 0:
            return f"第{i+1}个区间的单价必须为非负数"

        print(f"检查区间 {i+1}：上限 {upper_limit}，单价 {price}")

        if quantity <= upper_limit:
            interval_cost = quantity * price
            total_cost += interval_cost
            print(f"数量 {quantity} 落在区间 {i+1} 内，此区间费用：{interval_cost}")
            print(f"当前总费用：{total_cost}")
            return total_cost

    # 如果所有区间都不适用，则使用最后一个区间的单价（假设最后一个区间是“无限大”）
    if intervals and quantity > intervals[-1]["上限"]:
      interval_cost = quantity * intervals[-1]["单价"]
      total_cost += interval_cost
      print(f"数量 {quantity} 大于所有区间上限，使用最后一个区间单价，此区间费用：{interval_cost}")
      print(f"当前总费用：{total_cost}")
      return total_cost
    
    return "未找到适用的价格区间"

# 示例数据
price_intervals = [
    {"上限": 1000, "单价": 0.08},
    {"上限": 10000, "单价": 0.08},
    {"上限": float('inf'), "单价": 0.07},
]

setup_fee = 270

# 测试
quantities_to_print = [500, 8000, 12000]

for quantity_to_print in quantities_to_print:
    cost = calculate_cmyk_printing_cost(quantity_to_print, setup_fee, price_intervals)
    if isinstance(cost, str):
        print(cost)
    else:
        print(f"最终总费用：{cost}\n") #每个结果之间添加一个空行
