import math

def calculate_printing_cost(quantity, setup_cost, intervals, spot_colors=0):
    """计算印刷成本，并输出详细计算过程。"""

    print("--- 成本计算明细 ---")
    print(f"印刷数量：{quantity} 张")
    print(f"专色数量：{spot_colors} 个")
    print(f"开机费：{setup_cost} 元")

    # 1. 计算开机费
    total_setup_cost = setup_cost + spot_colors * (setup_cost / 2)
    print(f"开机总费用（含专色）：{setup_cost} + {spot_colors} * ({setup_cost:.2f} / 2) = {total_setup_cost:.2f} 元") #修正了这里

    # 2. 计算四色印刷费用
    total_cmyk_cost = 0
    remaining_quantity = quantity
    prev_end = 0
    print("\n--- 四色印刷费用 ---")
    for i, interval in enumerate(intervals):
        end = interval["end"]
        price = interval["price"]
        interval_quantity = min(remaining_quantity, end - prev_end)
        if interval_quantity < 0:
            interval_quantity = 0

        interval_cost = interval_quantity * price
        total_cmyk_cost += interval_cost

        print(f"区间 {i+1}: 数量 {prev_end+1} - {end} 张，单价 {price:.2f} 元/张，本区间费用：{interval_quantity} * {price:.2f} = {interval_cost:.2f} 元")

        remaining_quantity -= interval_quantity
        prev_end = end

        if remaining_quantity <= 0:
            break
    print(f"四色印刷总费用：{total_cmyk_cost:.2f} 元")

    # 3. 计算专色费用 (修改后的逻辑)
    total_spot_color_cost = total_cmyk_cost * (spot_colors / 2) if spot_colors > 0 else 0 # 只有当专色数量大于0时才计算专色费用
    print("\n--- 专色印刷费用 ---")
    print(f"专色印刷总费用：{total_cmyk_cost:.2f} * ({spot_colors} / 2) = {total_spot_color_cost:.2f} 元" if spot_colors > 0 else "无专色，专色印刷总费用：0.00 元") # 打印更清晰的信息

    # 4. 计算总价
    total_cost = total_setup_cost + total_cmyk_cost + total_spot_color_cost

    print("\n--- 总费用 ---")
    print(f"总费用：{total_setup_cost:.2f} (开机费) + {total_cmyk_cost:.2f} (四色) + {total_spot_color_cost:.2f} (专色) = {total_cost:.2f} 元")

    return total_cost

# 示例配置
price_config = {
    "setup_cost": 270,
    "intervals": [
        {"end": 1000, "price": 0.08},
        {"end": 10000, "price": 0.08},
        {"end": math.inf, "price": 0.07},
    ],
}

# 用户输入
quantity = int(input("请输入印刷数量："))
spot_colors = int(input("请输入专色数量："))

# 计算总价
total_price = calculate_printing_cost(quantity, price_config["setup_cost"], price_config["intervals"], spot_colors)