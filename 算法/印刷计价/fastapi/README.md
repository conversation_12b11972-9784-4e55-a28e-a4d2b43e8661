# 印刷计价系统

这是一个基于FastAPI的印刷成本计算系统，提供了现代化的Web界面和API接口。

## 功能特点

- 🎨 现代化的响应式Web界面
- 📊 详细的成本计算明细
- 🔄 实时AJAX计算，无需页面刷新
- 📱 移动端友好的设计
- 🚀 高性能的FastAPI后端
- 📖 自动生成的API文档

## 安装依赖

```bash
pip install -r requirements.txt
```

## 启动服务

### 方法1：使用启动脚本
```bash
python run.py
```

### 方法2：使用uvicorn直接启动
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## 访问地址

- **Web界面**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **ReDoc文档**: http://localhost:8000/redoc

## API接口

### POST /api/calculate

计算印刷成本的API接口

**请求体示例**:
```json
{
    "quantity": 5000,
    "spot_colors": 2,
    "white_ink": true,
    "white_ink_setup_cost": 190,
    "white_ink_price_per_sheet": 0.022
}
```

**响应示例**:
```json
{
    "quantity": 5000,
    "spot_colors": 2,
    "white_ink": true,
    "setup_cost": 270,
    "total_setup_cost": 540,
    "total_cmyk_cost": 400,
    "total_spot_color_cost": 400,
    "total_white_ink_cost": 300,
    "total_cost": 1640,
    "calculation_details": [
        "印刷数量：5000 张",
        "专色数量：2 个",
        "..."
    ]
}
```

## 计算规则

### 开机费用
- 基础开机费：270元
- 专色开机费：每个专色增加基础开机费的50%

### 四色印刷费用
- 1-1000张：0.08元/张
- 1001-10000张：0.08元/张  
- 10000张以上：0.07元/张

### 专色印刷费用
- 专色费用 = 四色印刷费用 × (专色数量 / 2)

### 白墨印刷费用
- 白墨开机费：190元（可调整）
- 白墨单价：0.022元/张（可调整）

## 项目结构

```
fastapi/
├── main.py              # 主应用文件
├── run.py               # 启动脚本
├── requirements.txt     # 依赖包列表
├── README.md           # 说明文档
└── templates/
    └── index.html      # Web界面模板
```

## 开发说明

- 使用FastAPI框架提供高性能的API服务
- 前端使用原生JavaScript实现AJAX交互
- 响应式CSS设计，支持移动端访问
- 支持表单提交和JSON API两种方式

## 技术栈

- **后端**: FastAPI, Python 3.7+
- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **服务器**: Uvicorn ASGI服务器
- **模板引擎**: Jinja2
