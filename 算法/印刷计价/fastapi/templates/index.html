<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>印刷计价系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .form-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .form-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .form-group input[type="number"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input[type="number"]:focus {
            outline: none;
            border-color: #4facfe;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: 20px;
            height: 20px;
            accent-color: #4facfe;
        }

        .advanced-options {
            margin-top: 20px;
            padding: 15px;
            background: #fff;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .advanced-options h3 {
            color: #666;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .result-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .result-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .total-cost {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }

        .total-cost h3 {
            font-size: 2em;
            margin-bottom: 5px;
        }

        .cost-breakdown {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .cost-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .cost-item:last-child {
            border-bottom: none;
            font-weight: 600;
            color: #333;
        }

        .details-section {
            margin-top: 20px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .details-section h4 {
            color: #666;
            margin-bottom: 15px;
        }

        .detail-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
            color: #666;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>印刷计价系统</h1>
            <p>专业的印刷成本计算工具</p>
        </div>

        <div class="content">
            <div class="form-section">
                <h2>计算参数</h2>

                {% if error %}
                <div class="error">{{ error }}</div>
                {% endif %}

                <form id="calculationForm">
                    <div class="form-group">
                        <label for="quantity">印刷数量（张）</label>
                        <input type="number" id="quantity" name="quantity" value="500" min="1" required>
                    </div>

                    <div class="form-group">
                        <label for="spot_colors">专色数量（个）</label>
                        <input type="number" id="spot_colors" name="spot_colors" value="0" min="0" max="10">
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="white_ink" name="white_ink" checked>
                            <label for="white_ink">需要白墨印刷</label>
                        </div>
                    </div>

                    <div class="advanced-options" id="whiteInkOptions">
                        <h3>白墨参数设置</h3>
                        <div class="form-group">
                            <label for="white_ink_setup_cost">白墨开机费（元）</label>
                            <input type="number" id="white_ink_setup_cost" name="white_ink_setup_cost" value="190" min="0" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="white_ink_price_per_sheet">白墨单价（元/张）</label>
                            <input type="number" id="white_ink_price_per_sheet" name="white_ink_price_per_sheet" value="0.022" min="0" step="0.001">
                        </div>
                    </div>

                    <button type="submit" class="btn">计算成本</button>
                </form>

                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>正在计算中...</p>
                </div>
            </div>

            <div class="result-section">
                <h2>计算结果</h2>

                <div id="results" style="display: none;">
                    <div class="total-cost">
                        <h3 id="totalCost">¥ 0.00</h3>
                        <p>总计费用</p>
                    </div>

                    <div class="cost-breakdown">
                        <div class="cost-item">
                            <span>开机费用：</span>
                            <span id="setupCost">¥ 0.00</span>
                        </div>
                        <div class="cost-item">
                            <span>四色印刷：</span>
                            <span id="cmykCost">¥ 0.00</span>
                        </div>
                        <div class="cost-item">
                            <span>专色印刷：</span>
                            <span id="spotColorCost">¥ 0.00</span>
                        </div>
                        <div class="cost-item">
                            <span>白墨印刷：</span>
                            <span id="whiteInkCost">¥ 0.00</span>
                        </div>
                        <div class="cost-item">
                            <span><strong>总计：</strong></span>
                            <span id="finalTotal"><strong>¥ 0.00</strong></span>
                        </div>
                    </div>

                    <div class="details-section">
                        <h4>计算明细</h4>
                        <div id="calculationDetails"></div>
                    </div>
                </div>

                {% if result %}
                <div id="serverResults">
                    <div class="total-cost">
                        <h3>¥ {{ "%.2f"|format(result.total_cost) }}</h3>
                        <p>总计费用</p>
                    </div>

                    <div class="cost-breakdown">
                        <div class="cost-item">
                            <span>开机费用：</span>
                            <span>¥ {{ "%.2f"|format(result.total_setup_cost) }}</span>
                        </div>
                        <div class="cost-item">
                            <span>四色印刷：</span>
                            <span>¥ {{ "%.2f"|format(result.total_cmyk_cost) }}</span>
                        </div>
                        <div class="cost-item">
                            <span>专色印刷：</span>
                            <span>¥ {{ "%.2f"|format(result.total_spot_color_cost) }}</span>
                        </div>
                        <div class="cost-item">
                            <span>白墨印刷：</span>
                            <span>¥ {{ "%.2f"|format(result.total_white_ink_cost) }}</span>
                        </div>
                        <div class="cost-item">
                            <span><strong>总计：</strong></span>
                            <span><strong>¥ {{ "%.2f"|format(result.total_cost) }}</strong></span>
                        </div>
                    </div>

                    <div class="details-section">
                        <h4>计算明细</h4>
                        {% for detail in result.calculation_details %}
                        <div class="detail-item">{{ detail }}</div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script>
        // 白墨选项显示/隐藏控制
        const whiteInkCheckbox = document.getElementById('white_ink');
        const whiteInkOptions = document.getElementById('whiteInkOptions');

        function toggleWhiteInkOptions() {
            whiteInkOptions.style.display = whiteInkCheckbox.checked ? 'block' : 'none';
        }

        whiteInkCheckbox.addEventListener('change', toggleWhiteInkOptions);
        toggleWhiteInkOptions(); // 初始化显示状态

        // 表单提交处理
        document.getElementById('calculationForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = {
                quantity: parseInt(formData.get('quantity')),
                spot_colors: parseInt(formData.get('spot_colors')),
                white_ink: formData.get('white_ink') === 'on',
                white_ink_setup_cost: parseFloat(formData.get('white_ink_setup_cost')) || 190,
                white_ink_price_per_sheet: parseFloat(formData.get('white_ink_price_per_sheet')) || 0.022
            };

            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            const serverResults = document.getElementById('serverResults');
            if (serverResults) serverResults.style.display = 'none';

            try {
                const response = await fetch('/api/calculate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (response.ok) {
                    displayResults(result);
                } else {
                    throw new Error(result.error || '计算失败');
                }
            } catch (error) {
                alert('计算错误: ' + error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        });

        function displayResults(result) {
            // 更新总费用
            document.getElementById('totalCost').textContent = `¥ ${result.total_cost.toFixed(2)}`;
            document.getElementById('setupCost').textContent = `¥ ${result.total_setup_cost.toFixed(2)}`;
            document.getElementById('cmykCost').textContent = `¥ ${result.total_cmyk_cost.toFixed(2)}`;
            document.getElementById('spotColorCost').textContent = `¥ ${result.total_spot_color_cost.toFixed(2)}`;
            document.getElementById('whiteInkCost').textContent = `¥ ${result.total_white_ink_cost.toFixed(2)}`;
            document.getElementById('finalTotal').innerHTML = `<strong>¥ ${result.total_cost.toFixed(2)}</strong>`;

            // 更新计算明细
            const detailsContainer = document.getElementById('calculationDetails');
            detailsContainer.innerHTML = '';
            result.calculation_details.forEach(detail => {
                const div = document.createElement('div');
                div.className = 'detail-item';
                div.textContent = detail;
                detailsContainer.appendChild(div);
            });

            // 显示结果
            document.getElementById('results').style.display = 'block';
        }
    </script>
</body>
</html>
