# 印刷计价系统 (Printing Cost Calculator)

这是一个基于FastAPI的现代化印刷成本计算系统，使用uv进行包管理。

## 功能特点

- 🎨 现代化的响应式Web界面
- 📊 详细的成本计算明细
- 🔄 实时AJAX计算，无需页面刷新
- 📱 移动端友好的设计
- 🚀 高性能的FastAPI后端
- 📖 自动生成的API文档
- ⚡ 使用uv进行快速包管理

## 安装和运行

### 使用uv（推荐）

```bash
# 安装依赖
uv sync

# 启动开发服务器
uv run start

# 或者直接运行
uv run python main.py
```

### 传统方式

```bash
# 安装依赖
pip install -e .

# 启动服务器
python main.py
```

## 访问地址

- **Web界面**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **ReDoc文档**: http://localhost:8000/redoc

## API接口

### POST /api/calculate

计算印刷成本的API接口

**请求体示例**:
```json
{
    "quantity": 5000,
    "spot_colors": 2,
    "white_ink": true,
    "white_ink_setup_cost": 190,
    "white_ink_price_per_sheet": 0.022
}
```

**响应示例**:
```json
{
    "quantity": 5000,
    "spot_colors": 2,
    "white_ink": true,
    "setup_cost": 270,
    "total_setup_cost": 540,
    "total_cmyk_cost": 400,
    "total_spot_color_cost": 400,
    "total_white_ink_cost": 300,
    "total_cost": 1640,
    "calculation_details": [
        "印刷数量：5000 张",
        "专色数量：2 个",
        "..."
    ]
}
```

## 计算规则

### 开机费用
- 基础开机费：270元
- 专色开机费：每个专色增加基础开机费的50%

### 四色印刷费用
- 1-1000张：0.08元/张
- 1001-10000张：0.08元/张
- 10000张以上：0.07元/张

### 专色印刷费用
- 专色费用 = 四色印刷费用 × (专色数量 / 2)

### 白墨印刷费用
- 白墨开机费：190元（可调整）
- 白墨单价：0.022元/张（可调整）

## 开发

### 添加依赖

```bash
# 添加运行时依赖
uv add package-name

# 添加开发依赖
uv add --dev package-name
```

### 运行测试

```bash
uv run pytest
```

## 技术栈

- **后端**: FastAPI, Python 3.9+
- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **服务器**: Uvicorn ASGI服务器
- **模板引擎**: Jinja2
- **包管理**: uv