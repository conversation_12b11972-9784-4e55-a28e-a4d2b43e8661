[project]
name = "printing-cost-calculator"
version = "0.1.0"
description = "专业的印刷成本计算系统，基于FastAPI构建"
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    "jinja2>=3.1.2",
    "requests>=2.32.4",
]

[project.scripts]
start = "printing_cost_calculator.main:start_server"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "httpx>=0.24.0",
]
