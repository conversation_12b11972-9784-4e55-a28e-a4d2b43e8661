#!/usr/bin/env python3
"""
简单的API测试脚本
"""

import requests
import json

def test_api():
    """测试印刷计价API"""
    base_url = "http://localhost:8002"
    
    # 测试数据
    test_cases = [
        {
            "name": "基础测试 - 500张，无专色，有白墨",
            "data": {
                "quantity": 500,
                "spot_colors": 0,
                "white_ink": True,
                "white_ink_setup_cost": 190,
                "white_ink_price_per_sheet": 0.022
            }
        },
        {
            "name": "中等数量 - 5000张，1个专色，有白墨",
            "data": {
                "quantity": 5000,
                "spot_colors": 1,
                "white_ink": True,
                "white_ink_setup_cost": 190,
                "white_ink_price_per_sheet": 0.022
            }
        },
        {
            "name": "大数量 - 15000张，2个专色，无白墨",
            "data": {
                "quantity": 15000,
                "spot_colors": 2,
                "white_ink": False,
                "white_ink_setup_cost": 190,
                "white_ink_price_per_sheet": 0.022
            }
        }
    ]
    
    print("🧪 开始测试印刷计价API...")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        print("-" * 40)
        
        try:
            # 发送POST请求
            response = requests.post(
                f"{base_url}/api/calculate",
                headers={"Content-Type": "application/json"},
                json=test_case["data"],
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 请求成功")
                print(f"📊 印刷数量: {result['quantity']} 张")
                print(f"🎨 专色数量: {result['spot_colors']} 个")
                print(f"🖨️ 白墨印刷: {'是' if result['white_ink'] else '否'}")
                print(f"💰 总费用: ¥{result['total_cost']:.2f}")
                print(f"   - 开机费: ¥{result['total_setup_cost']:.2f}")
                print(f"   - 四色印刷: ¥{result['total_cmyk_cost']:.2f}")
                print(f"   - 专色印刷: ¥{result['total_spot_color_cost']:.2f}")
                print(f"   - 白墨印刷: ¥{result['total_white_ink_cost']:.2f}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print(f"🌐 Web界面: {base_url}")
    print(f"📖 API文档: {base_url}/docs")

if __name__ == "__main__":
    test_api()
