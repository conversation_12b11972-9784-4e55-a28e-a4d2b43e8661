#!/usr/bin/env python3
"""
印刷计价系统启动脚本
支持多种启动方式
"""

import sys
import subprocess
import argparse
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(description='启动印刷计价系统')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=8000, help='服务器端口 (默认: 8000)')
    parser.add_argument('--reload', action='store_true', help='启用自动重载 (开发模式)')
    parser.add_argument('--workers', type=int, default=1, help='工作进程数量')
    
    args = parser.parse_args()
    
    # 构建uvicorn命令
    cmd = [
        'uv', 'run', 'uvicorn', 
        'printing_cost_calculator.main:app',
        '--host', args.host,
        '--port', str(args.port)
    ]
    
    if args.reload:
        cmd.append('--reload')
    
    if args.workers > 1:
        cmd.extend(['--workers', str(args.workers)])
    
    print(f"🚀 启动印刷计价系统...")
    print(f"📍 地址: http://{args.host}:{args.port}")
    print(f"📖 API文档: http://{args.host}:{args.port}/docs")
    print(f"🔄 自动重载: {'开启' if args.reload else '关闭'}")
    print(f"👥 工作进程: {args.workers}")
    print("-" * 50)
    
    try:
        # 执行命令
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
        sys.exit(0)

if __name__ == "__main__":
    main()
