def calculate_printing_cost_single_dict(N, price_config):
    """
    计算印刷成本（不包含纸张费用，所有配置信息整合到一个字典中）

    Args:
        N: 印刷数量
        price_config: 价格配置，包含开机费和区间价格信息

    Returns:
        总价
    """
    setup_cost = price_config["setup_cost"]  # 获取开机费
    intervals = price_config["intervals"]  # 获取区间价格信息

    # 如果印刷数量小于第一个区间的最小值，则按照第一个区间的最小值计算。
    if N < intervals[0]["end"]:
        N = intervals[0]["end"]

    total_cost = setup_cost  # 初始化总价，首先加上开机费
    prev_end = 0  # 上一个区间的结束值，初始为 0

    for interval in intervals:
        end = interval["end"]  # 当前区间的结束值
        price = interval["price"]  # 当前区间的印刷单价

        if N < end:
            # 如果印刷数量小于当前区间的结束值，则计算当前区间的费用，并跳出循环
            total_cost += (N - prev_end) * price
            break
        else:
            # 如果印刷数量大于等于当前区间的结束值，则计算当前区间的全部费用
            total_cost += (end - prev_end) * price
            prev_end = end  # 更新上一个区间的结束值为当前区间的结束值

    return total_cost


# 整合后的价格配置示例
price_config_single_dict = {
    "setup_cost": 350,  # 开机费
    "intervals": [
        {"end": 500, "price": 0.2},  # 0-500张，单价0.2
        {"end": 2000, "price": 0.15},  # 500-2000张，单价0.15
        {"end": 5000, "price": 0.1},  # 2000-5000张，单价0.1
        {"end": float('inf'), "price": 0.07}  # 5000张以上，单价0.07
    ]
}

# 用户输入印刷数量
quantity = int(input("请输入印刷数量："))

# 调用函数计算总价
total_price = calculate_printing_cost_single_dict(quantity, price_config_single_dict)

# 输出结果
print(f"印刷 {quantity} 张的总价为：{total_price:.2f} 元")