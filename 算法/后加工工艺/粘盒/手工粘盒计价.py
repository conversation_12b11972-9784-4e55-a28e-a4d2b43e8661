def calculate_manual_sticking_fee(quantity, num_extra_sticking_positions, config):
    """
    使用分段叠加算法计算手工粘盒费用

    Args:
        quantity: 订单数量 (整数)
        num_extra_sticking_positions: 额外粘位数量 (整数)
        config: 配置字典，包含各项费用参数 (字典)

    Returns:
        一个字典，包含 "手工粘盒费"、"额外粘位费" 和 "计算明细" (字典)
    """

    # 计算手工粘盒费
    sticking_fee = config["opening_fee"]  # 初始化费用为开机费
    remaining_quantity = quantity  # 剩余未计算数量
    
    details = []  # 用于存储计算明细
    details.append(f"开机费: ￥{config['opening_fee']:.2f}")

    # 遍历价格区间，进行分段叠加计算
    for tier_limit, unit_price in config["price_tiers"]:
        if remaining_quantity <= 0:
            break  # 如果剩余数量已为 0，则跳出循环

        if remaining_quantity <= tier_limit:
            # 如果剩余数量小于等于当前区间上限，则使用当前区间单价计算费用
            fee_for_tier = remaining_quantity * unit_price
            sticking_fee += fee_for_tier
            details.append(f"数量 {quantity - remaining_quantity + 1} - {quantity}: 单价 ￥{unit_price:.3f}, 费用 ￥{fee_for_tier:.2f}")
            remaining_quantity = 0  # 剩余数量置为 0
        else:
            # 如果剩余数量大于当前区间上限，则使用当前区间单价计算当前区间内的费用
            fee_for_tier = tier_limit * unit_price
            sticking_fee += fee_for_tier
            details.append(f"数量 {quantity - remaining_quantity + 1} - {quantity - remaining_quantity + tier_limit}: 单价 ￥{unit_price:.3f}, 费用 ￥{fee_for_tier:.2f}")
            remaining_quantity -= tier_limit  # 更新剩余数量

    # 最终费用与最低消费比较，取较大值
    details.append(f"小计: ￥{sticking_fee:.2f}")
    if sticking_fee < config["min_consumption"]:
        details.append(f"低于最低消费, 按最低消费计算: ￥{config['min_consumption']:.2f}")
        sticking_fee = config["min_consumption"]

    # 计算额外粘位费 (假设每个额外粘位独立计算最低消费)
    extra_sticking_fee = num_extra_sticking_positions * max(
        quantity * config["extra_sticking_unit_price"], config["extra_sticking_min_consumption"]
    )
    extra_sticking_details = []
    if num_extra_sticking_positions > 0:
        extra_sticking_details.append(f"额外粘位费用:")
        for i in range(num_extra_sticking_positions):
          extra_sticking_details.append(f"  额外粘位 {i+1}: 数量 {quantity} * 单价 ￥{config['extra_sticking_unit_price']:.3f} = ￥{quantity * config['extra_sticking_unit_price']:.2f} (最低 ￥{config['extra_sticking_min_consumption']:.2f})")
        extra_sticking_details.append(f"额外粘位费小计: ￥{extra_sticking_fee:.2f}")

    return {
        "手工粘盒费": sticking_fee,
        "额外粘位费": extra_sticking_fee,
        "计算明细": details + extra_sticking_details
    }


# 默认配置
default_config = {
    "opening_fee": 50.00,  # 开机费
    "min_consumption": 200.00,  # 最低消费
    "price_tiers": [  # 价格区间列表，每个元素是一个元组 (数量上限, 单价)
        (1000, 0.150),
        (10000, 0.140),
        (float("inf"), 0.130),  # 使用 float("inf") 表示无穷大
    ],
    "extra_sticking_unit_price": 0.100,  # 额外粘位单价
    "extra_sticking_min_consumption": 100.00,  # 额外粘位最低消费
}

# 示例：使用默认配置
quantity = 12500  # 订单数量
num_extra_sticking_positions = 2  # 额外粘位数量

# 使用默认配置计算费用
result = calculate_manual_sticking_fee(
    quantity, num_extra_sticking_positions, default_config
)

# 打印结果
print(f"订单数量: {quantity}")
print(f"额外粘位数量: {num_extra_sticking_positions}")
print(f"手工粘盒费: ￥{result['手工粘盒费']:.2f}")
print(f"额外粘位费: ￥{result['额外粘位费']:.2f}")
print(f"总费用: ￥{result['手工粘盒费'] + result['额外粘位费']:.2f}")

print("\n计算明细:")
for detail in result["计算明细"]:
    print(detail)