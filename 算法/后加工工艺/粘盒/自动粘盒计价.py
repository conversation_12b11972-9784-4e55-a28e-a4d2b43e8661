class GluingFeeConfig:
    """粘盒费用配置类"""
    def __init__(self):
        # 基础费用配置
        self.setup_fee = 100.00      # 开机费
        self.min_charge = 120.00     # 最低消费
        
        # 数量区间和单价配置
        self.tiers = [
            {'limit': 1000, 'price': 0.025},    # 第一区间: 0-1000, ¥0.025/个
            {'limit': 10000, 'price': 0.020},   # 第二区间: 1001-10000, ¥0.020/个
            {'limit': float('inf'), 'price': 0.015}  # 第三区间: 10001+, ¥0.015/个
        ]

class GluingFeeCalculator:
    """粘盒费用计算器类"""
    def __init__(self, config: GluingFeeConfig):
        self.config = config
    
    def calculate(self, quantity: int) -> float:
        """
        计算粘盒费用
        
        Args:
            quantity: 订单数量
            
        Returns:
            float: 计算后的费用
        """
        if quantity <= 0:
            return 0
            
        total_fee = self.config.setup_fee
        processed_quantity = 0
        
        for i, tier in enumerate(self.config.tiers):
            # 计算当前区间的数量
            if i == 0:
                current_tier_quantity = min(quantity, tier['limit'])
            else:
                previous_limit = self.config.tiers[i-1]['limit']
                if quantity <= previous_limit:
                    break
                current_tier_quantity = min(quantity - previous_limit, 
                                          tier['limit'] - previous_limit)
            
            # 计算当前区间的费用
            tier_fee = current_tier_quantity * tier['price']
            total_fee += tier_fee
            
            processed_quantity += current_tier_quantity
            if processed_quantity >= quantity:
                break
        
        return max(total_fee, self.config.min_charge)

def format_fee(fee: float) -> str:
    """格式化费用显示"""
    return f"¥{fee:.2f}元"

def format_calculation_details(quantity: int, calculator: GluingFeeCalculator) -> str:
    """格式化计算详情"""
    config = calculator.config
    details = []
    processed_quantity = 0
    subtotal = config.setup_fee
    
    details.append(f"开机费: {format_fee(config.setup_fee)}")
    
    for i, tier in enumerate(config.tiers):
        if i == 0:
            current_tier_quantity = min(quantity, tier['limit'])
        else:
            previous_limit = config.tiers[i-1]['limit']
            if quantity <= previous_limit:
                break
            current_tier_quantity = min(quantity - previous_limit, 
                                      tier['limit'] - previous_limit)
        
        if current_tier_quantity > 0:
            tier_fee = current_tier_quantity * tier['price']
            subtotal += tier_fee
            details.append(
                f"第{i+1}区间({current_tier_quantity}个 × {tier['price']:.3f}): "
                f"{format_fee(tier_fee)}"
            )
        
        processed_quantity += current_tier_quantity
        if processed_quantity >= quantity:
            break
    
    details.append(f"小计: {format_fee(subtotal)}")
    
    final_fee = max(subtotal, config.min_charge)
    if final_fee > subtotal:
        details.append(f"低于最低消费{format_fee(config.min_charge)}，按最低消费计算")
    
    details.append(f"最终费用: {format_fee(final_fee)}")
    
    return "\n".join(details)

# 测试代码
def main():
    # 创建配置和计算器
    config = GluingFeeConfig()
    calculator = GluingFeeCalculator(config)
    
    # 测试不同数量的订单
    test_quantities = [800, 5000, 12000]
    
    print("【详细计算示例】")
    for qty in test_quantities:
        print(f"\n订单数量: {qty}个")
        print("-" * 40)
        print(format_calculation_details(qty, calculator))
        print("-" * 40)

if __name__ == "__main__":
    main()