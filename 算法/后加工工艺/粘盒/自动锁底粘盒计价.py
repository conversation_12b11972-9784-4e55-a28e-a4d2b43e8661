# ----------------------------------------------------------------------
# 计价参数设定 (可自由修改)
# ----------------------------------------------------------------------

STARTUP_FEE = 360  # 开机费
MIN_COST = 400  # 最低消费
PRICE_TIERS = [
    (1000, 0.040),  # (数量上限, 单价)
    (10000, 0.035),
    (float('inf'), 0.030),  # 使用无限大表示最后一个区间的上限
]

# ----------------------------------------------------------------------
# 计算函数
# ----------------------------------------------------------------------

def calculate_cost(order_quantity, startup_fee=STARTUP_FEE, min_cost=MIN_COST, price_tiers=PRICE_TIERS):
    """
    根据订单数量计算自动锁底粘盒费（分段叠加方式）

    Args:
        order_quantity: 订单数量
        startup_fee: 开机费 (默认为全局变量 STARTUP_FEE)
        min_cost: 最低消费 (默认为全局变量 MIN_COST)
        price_tiers: 价格区间和单价的列表 (默认为全局变量 PRICE_TIERS)

    Returns:
        总费用
    """

    print("-" * 30)
    print(f"订单数量: {order_quantity}")
    print("计价明细:")

    total_cost = startup_fee
    remaining_quantity = order_quantity

    print(f"  开机费: {startup_fee:.2f} 元")

    # 分段叠加计算
    for i, (upper_limit, unit_price) in enumerate(price_tiers):
        if remaining_quantity <= 0:
            break

        # 计算当前区间的数量
        if upper_limit != float('inf'):
            quantity_in_tier = min(remaining_quantity, upper_limit - (sum([p[0] for p in price_tiers if p[0] < upper_limit]) if any([p[0] < upper_limit for p in price_tiers]) else 0))
        else:
            quantity_in_tier = remaining_quantity
        
        # 计算当前区间的费用
        tier_cost = quantity_in_tier * unit_price
        total_cost += tier_cost

        # 打印当前区间的信息
        tier_name = f"区间 {i + 1}"
        if upper_limit == float('inf'):
            tier_range = f"{price_tiers[i-1][0] + 1} 个以上"
        else:
            tier_range = f"{ (sum([p[0] for p in price_tiers if p[0] < upper_limit]) if any([p[0] < upper_limit for p in price_tiers]) else 0) + 1} - {upper_limit} 个"
        
        print(f"  {tier_name} ({tier_range}, 单价: {unit_price:.4f} 元/个): 数量 {quantity_in_tier} 个, 费用 {tier_cost:.2f} 元")

        # 更新剩余数量
        remaining_quantity -= quantity_in_tier

    # 与最低消费比较，取较大值
    final_cost = max(total_cost, min_cost)

    print(f"  小计: {total_cost:.2f} 元")
    print(f"  最低消费: {min_cost:.2f} 元")
    print(f"总费用: {final_cost:.2f} 元")
    print("-" * 30)

    return final_cost

# ----------------------------------------------------------------------
# 测试用例
# ----------------------------------------------------------------------

test_cases = [500, 5000, 12000, 25000]
for quantity in test_cases:
    calculate_cost(quantity)

# ----------------------------------------------------------------------
# 演示修改计价参数
# ----------------------------------------------------------------------

# 修改开机费为500，其他参数保持默认
calculate_cost(15000, startup_fee=500)

# 修改价格区间，增加一个区间
new_price_tiers = [
    (500, 0.045),
    (5000, 0.040),
    (10000, 0.035),
    (float('inf'), 0.030),
]
calculate_cost(15000, price_tiers=new_price_tiers)