# 参数配置（保持不变）
gluing_config = {
    "opening_fee": 380.00,
    "minimum_fee": 450.00,
    "unit_prices": {
        (0, 1000): 0.070,
        (1001, 10000): 0.065,
        (10001, float('inf')): 0.060,
    },
    "double_sided_tape_price": 12.00
}

def calculate_gluing_fee(quantity, config, use_double_sided_tape=False, tape_area=0):
    """
    使用分段叠加方式计算平粘盒粘费，并打印详细成本组成。

    Args:
        quantity: 订单数量。
        config: 配置字典。
        use_double_sided_tape: 是否使用双面胶。
        tape_area: 双面胶使用面积。

    Returns:
        总费用，或错误信息。
    """

    if not isinstance(quantity, int) or quantity < 0:
        return "订单数量必须为非负整数。"

    if use_double_sided_tape and (not isinstance(tape_area, (int, float)) or tape_area < 0):
        return "双面胶面积必须为非负数。"

    opening_fee = config.get("opening_fee")
    minimum_fee = config.get("minimum_fee")
    unit_prices = config.get("unit_prices")
    double_sided_tape_price = config.get("double_sided_tape_price")

    if not all([opening_fee, minimum_fee, unit_prices, double_sided_tape_price]):
        return "配置信息不完整。"

    total_fee_by_quantity = 0  # 用于存储按数量分段计算的总费用
    remaining_quantity = quantity # 剩余待计算的数量

    print("----- 按数量分段计算 -----") # 打印分段计算过程

    for (lower, upper), price in sorted(unit_prices.items()):  # 按照区间下限排序，确保计算顺序正确
        if remaining_quantity > 0:
            amount_in_range = min(remaining_quantity, upper - lower + 1 if upper != float('inf') else remaining_quantity) # 计算当前区间内的数量。注意无穷大的处理
            fee_in_range = amount_in_range * price
            total_fee_by_quantity += fee_in_range
            remaining_quantity -= amount_in_range
            print(f"区间 {lower}-{upper if upper != float('inf') else '以上'}：{amount_in_range}个 x ￥{price:.3f}/个 = ￥{fee_in_range:.2f}")

    print("--------------------")

    total_fee = opening_fee + total_fee_by_quantity

    # 打印成本组成 (修改部分)
    print("----- 成本组成 -----")
    print(f"开机费：￥{opening_fee:.2f}")
    print(f"按数量计算总费用：￥{total_fee_by_quantity:.2f}") # 修改了这一行
    if total_fee < minimum_fee:
        print(f"低于最低消费，按最低消费计算：￥{minimum_fee:.2f}")
        total_fee = minimum_fee
    else:
        print(f"小计：￥{total_fee:.2f}")

    if use_double_sided_tape:
        tape_material_fee = double_sided_tape_price * tape_area
        print(f"双面胶材料费 ({tape_area:.2f}平方米 x ￥{double_sided_tape_price:.2f}/平方米)：￥{tape_material_fee:.2f}")
        total_fee += tape_material_fee

    print(f"总费用：￥{total_fee:.2f}")
    print("--------------------")

    return total_fee

# 使用示例
quantity = 15000
use_double_sided_tape = True
tape_area = 5

# final_fee = calculate_gluing_fee(quantity, gluing_config, use_double_sided_tape, tape_area)

# quantity = 500
final_fee = calculate_gluing_fee(quantity, gluing_config, use_double_sided_tape, tape_area)