import os
from pathlib import Path
from fastapi import FastAPI, Request, Form
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from collections import OrderedDict

# 配置信息（全局常量）
SETUP_FEE = 90.00  # 开机费
MINIMUM_CHARGE = 150.00  # 最低消费
BLADE_PRICE_PER_METER = 12.00  # 刀片每米单价

# 不同数量区间的单价（使用有序字典，保持区间顺序）
UNIT_PRICES = OrderedDict([
    (1000, 0.06),      # 一级单价
    (10000, 0.06),   # 二级单价
    (float('inf'), 0.05)  # 三级单价
])

# 将原始计算函数复制到这里
def calculate_die_cutting_processing_cost(quantity):
    """使用分段叠加方式计算模切加工费。"""
    if not isinstance(quantity, (int, float)) or quantity < 0:
        return "印张数量必须为非负数。"

    processing_cost = SETUP_FEE
    remaining_quantity = quantity
    details = []  # 存储每个区间的计算明细

    for upper_bound, price in UNIT_PRICES.items():
        if remaining_quantity > 0:
            quantity_in_this_tier = min(remaining_quantity, upper_bound)
            tier_cost = quantity_in_this_tier * price
            processing_cost += tier_cost
            remaining_quantity -= quantity_in_this_tier
            details.append({"upper_bound": upper_bound, "price": price, "quantity_in_this_tier": quantity_in_this_tier, "tier_cost": tier_cost, "remaining_quantity": remaining_quantity}) #添加计算详情
    
    base_processing_cost = processing_cost - SETUP_FEE
    processing_cost = max(processing_cost, MINIMUM_CHARGE)
    return processing_cost, details, base_processing_cost


def calculate_blade_material_cost(blade_length_per_die, num_of_dies_per_sheet):
    """计算模切刀材料费。"""
    if not all(isinstance(x, (int, float)) and x >= 0 for x in [blade_length_per_die, num_of_dies_per_sheet]):
        return "刀线长度和模数必须为非负数。"

    blade_cost = blade_length_per_die * num_of_dies_per_sheet * BLADE_PRICE_PER_METER
    return blade_cost


def calculate_die_cutting_total_cost(quantity, blade_length_per_die, num_of_dies_per_sheet):
    """计算模切总费用。"""
    processing_cost_result = calculate_die_cutting_processing_cost(quantity)
    if isinstance(processing_cost_result, str):
        return processing_cost_result
    processing_cost, details, base_processing_cost = processing_cost_result

    blade_cost = calculate_blade_material_cost(blade_length_per_die, num_of_dies_per_sheet)
    if isinstance(blade_cost, str):
        return blade_cost

    total_cost = processing_cost + blade_cost
    return {
        "total_cost": round(total_cost, 2),
        "processing_cost": round(processing_cost, 2),
        "blade_cost": round(blade_cost, 2),
        "details": details,
        "base_processing_cost": round(base_processing_cost, 2)
    }


app = FastAPI()

# 配置模板文件目录
BASE_DIR = Path(__file__).resolve().parent
templates = Jinja2Templates(directory=BASE_DIR / "templates")

# 配置静态文件目录 (如果需要)
# app.mount("/static", StaticFiles(directory=BASE_DIR / "static"), name="static")

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """提供前端 HTML 页面。"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/calculate/")
async def calculate_die_cutting(
    quantity: float = Form(...),
    blade_length_per_die: float = Form(...),
    num_of_dies_per_sheet: float = Form(...)
):
    """接收前端数据并计算模切总费用。"""
    result = calculate_die_cutting_total_cost(quantity, blade_length_per_die, num_of_dies_per_sheet)
    return result
