# 配置信息（全局常量）
SETUP_FEE = 90.00  # 开机费
MINIMUM_CHARGE = 150.00  # 最低消费
BLADE_PRICE_PER_METER = 12.00  # 刀片每米单价

# 不同数量区间的单价（使用有序字典，保持区间顺序）
from collections import OrderedDict

UNIT_PRICES = OrderedDict([
    (1000, 0.06),      # 一级单价
    (10000, 0.06),   # 二级单价
    (float('inf'), 0.05)  # 三级单价
])


def calculate_die_cutting_processing_cost(quantity):
    """使用分段叠加方式计算模切加工费。"""
    if not isinstance(quantity, (int, float)) or quantity < 0:
        return "印张数量必须为非负数。"

    processing_cost = SETUP_FEE
    remaining_quantity = quantity
    details = []  # 存储每个区间的计算明细

    for upper_bound, price in UNIT_PRICES.items():
        if remaining_quantity > 0:
            quantity_in_this_tier = min(remaining_quantity, upper_bound)
            tier_cost = quantity_in_this_tier * price
            processing_cost += tier_cost
            remaining_quantity -= quantity_in_this_tier
            details.append((upper_bound, price, quantity_in_this_tier, tier_cost, remaining_quantity)) #添加计算详情
    
    base_processing_cost = processing_cost - SETUP_FEE
    processing_cost = max(processing_cost, MINIMUM_CHARGE)
    return processing_cost, details, base_processing_cost


def calculate_blade_material_cost(blade_length_per_die, num_of_dies_per_sheet):
    """计算模切刀材料费。"""
    if not all(isinstance(x, (int, float)) and x >= 0 for x in [blade_length_per_die, num_of_dies_per_sheet]):
        return "刀线长度和模数必须为非负数。"

    blade_cost = blade_length_per_die * num_of_dies_per_sheet * BLADE_PRICE_PER_METER
    return blade_cost


def calculate_die_cutting_total_cost(quantity, blade_length_per_die, num_of_dies_per_sheet):
    """计算模切总费用。"""
    processing_cost_result = calculate_die_cutting_processing_cost(quantity)
    if isinstance(processing_cost_result, str):
        return processing_cost_result
    processing_cost, details, base_processing_cost = processing_cost_result

    blade_cost = calculate_blade_material_cost(blade_length_per_die, num_of_dies_per_sheet)
    if isinstance(blade_cost, str):
        return blade_cost

    total_cost = processing_cost + blade_cost
    return total_cost, processing_cost, blade_cost, details, base_processing_cost



# 示例用法
test_cases = [
    (500, 1.5, 2),
    (1500, 1.5, 2),
    (5000, 1.5, 2),
    (12000, 1.5, 2),
    (25000, 1.5, 2)
]

for quantity, blade_length_per_die, num_of_dies_per_sheet in test_cases:
    total_cost_result = calculate_die_cutting_total_cost(quantity, blade_length_per_die, num_of_dies_per_sheet)

    if isinstance(total_cost_result, str):
        print(total_cost_result)
    else:
        total_cost, processing_cost, blade_cost, details, base_processing_cost = total_cost_result
        print(f"印张数量：{quantity}")
        print(f"每模刀线长度：{blade_length_per_die} 米")
        print(f"每个印张模切位数：{num_of_dies_per_sheet}")
        print("模切加工费计算明细：")
        for upper_bound, price, quantity_in_this_tier, tier_cost, remaining_quantity in details:
            print(f"  数量小于等于 {upper_bound} 的 {quantity_in_this_tier} 张，单价 ¥{price:.2f}/张，本区间费用 ¥{tier_cost:.2f}，剩余 {remaining_quantity} 张")
        print(f"未考虑最低消费的加工费：¥{base_processing_cost:.2f}")
        print(f"模切加工费：¥{processing_cost:.2f}")
        print(f"模切刀材料费：¥{blade_cost:.2f}")
        print(f"总费用为：¥{total_cost:.2f}")
    print("-" * 20)