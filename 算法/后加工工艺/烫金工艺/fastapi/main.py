from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
from typing import List, Dict

# 设定值 (从普通烫金v2.py复制)
SETTINGS = {
    "starting_fee": 180.00,  # 开机费
    "min_consumption": 0.00,  # 最低消费
    "material_price_per_sqm": 4.00,  # 材料每平方米单价
    "mold_price_per_sqcm": 0.003,  # 模版每平方厘米单价
    "min_mold_fee": 20.00,  # 最低模版费用
    "processing_price_tiers": {  # 加工费分段价格
        1000: 0.20,
        10000: 0.18,
        float('inf'): 0.13,
    },
}

# 计算加工费函数 (从普通烫金v2.py复制)
def calculate_processing_fee(print_quantity):
    """计算加工费（分段叠加）。"""
    processing_fee = 0
    previous_limit = 0
    for limit, price in sorted(SETTINGS["processing_price_tiers"].items()):
        quantity_in_tier = max(0, min(print_quantity - previous_limit, limit - previous_limit))
        processing_fee += quantity_in_tier * price
        previous_limit = limit
        if print_quantity <= limit:
            break
    return processing_fee

# 计算总费用函数 (从普通烫金v2.py复制)
def calculate_gilding_cost(print_quantity, gilding_positions):
    """计算烫金总费用。"""
    if print_quantity < 0 or not gilding_positions:
        return None

    total_cost = SETTINGS["starting_fee"]  # 开机费

    # 1. 烫金加工费用
    processing_fee = calculate_processing_fee(print_quantity)
    total_cost += processing_fee

    # 计算每个工艺位的费用
    for pos in gilding_positions:
        w, h, m = pos.width, pos.height, pos.mold_quantity
        area = w * h

        # 2. 烫金用料费
        material_area = (w + 2) * (h + 2) / 10000
        material_fee = material_area * print_quantity * SETTINGS["material_price_per_sqm"]
        total_cost += material_fee

        # 3. 烫金版费用
        mold_fee = max(area * m * SETTINGS["mold_price_per_sqcm"], SETTINGS["min_mold_fee"])
        total_cost += mold_fee

    total_cost = max(total_cost, SETTINGS["min_consumption"])
    return total_cost

# 定义烫金工艺位的数据模型
class GildingPosition(BaseModel):
    width: float
    height: float
    mold_quantity: int

# 定义请求体的数据模型
class GildingRequest(BaseModel):
    print_quantity: int
    gilding_positions: List[GildingPosition]

app = FastAPI()
templates = Jinja2Templates(directory="templates")

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/calculate_gilding/")
async def calculate_gilding(request: GildingRequest):
    total_cost = calculate_gilding_cost(request.print_quantity, request.gilding_positions)
    if total_cost is None:
        return {"error": "Invalid input parameters"}
    return {"total_cost": round(total_cost, 2)}
