def calculate_gilding_cost(quantity, area, num_of_positions, num_of_molds,
                           startup_fee, price_levels, material_price_per_sqm,
                           mold_fixed_cost, mold_price_per_sqcm, area_padding_cm):
    """
    计算烫金总费用（完全参数化，输出明细）。

    Args:
        quantity: 印张数量。
        area: 单个烫金位的面积，单位平方厘米。
        num_of_positions: 工艺位数量。
        num_of_molds: 烫金版数量（模数）。
        startup_fee: 开机费。
        price_levels: 价格区间和单价列表，例如 [(1000, 0.20), (10000, 0.18), (float('inf'), 0.13)]。
        material_price_per_sqm: 用料单价，单位元/平方米。
        mold_fixed_cost: 烫金版固定费用。
        mold_price_per_sqcm: 烫金版每平方厘米单价。
        area_padding_cm: 烫金位面积计算时，长宽各增加的厘米数。

    Returns:
        一个包含所有费用明细的字典，如果输入参数无效则返回 None。
    """

    # 参数有效性检查
    if not all(isinstance(x, (int, float)) and x >= 0 for x in
               [quantity, area, num_of_positions, num_of_molds, startup_fee,
                material_price_per_sqm, mold_fixed_cost, mold_price_per_sqcm, area_padding_cm]):
        return None

    if not isinstance(price_levels, list) or not all(
            isinstance(level, tuple) and len(level) == 2 and isinstance(level[0], (int, float)) and isinstance(level[1], (int, float)) for level in price_levels):
        return None

    # 加工费计算
    processing_fee = startup_fee
    processing_fee_details = {"开机费": startup_fee}  # 存储加工费明细
    remaining_quantity = quantity
    for upper_limit, price in price_levels:
        if remaining_quantity <= 0:
            break
        quantity_in_level = min(remaining_quantity, upper_limit)
        level_cost = quantity_in_level * price
        processing_fee += level_cost
        processing_fee_details[f"数量在 {upper_limit} 以内的费用"] = level_cost  # 添加明细
        remaining_quantity -= quantity_in_level

    # 用料费计算
    padded_area_cm = (area**0.5 + area_padding_cm)**2
    material_cost = padded_area_cm * quantity * num_of_positions * 0.0001 * material_price_per_sqm
    material_details = {
        "单个烫金位加边后面积(平方厘米)": padded_area_cm,
        "用料总面积(平方米)": padded_area_cm * quantity * num_of_positions * 0.0001,
        "用料单价(元/平方米)": material_price_per_sqm,
        "用料总费用": material_cost
    }

    # 版费计算
    mold_cost = area * num_of_molds * mold_price_per_sqcm + num_of_molds * mold_fixed_cost
    mold_details = {
        "单个烫金版面积(平方厘米)": area,
        "烫金版每平方厘米单价(元)": mold_price_per_sqcm,
        "烫金版固定费用(每个)":mold_fixed_cost,
        "版费总费用": mold_cost
    }

    # 总费用计算
    total_cost = processing_fee + material_cost + mold_cost

    # 构建返回的字典
    cost_details = {
        "加工费": processing_fee,
        "加工费明细": processing_fee_details,
        "用料费": material_cost,
        "用料费明细": material_details,
        "版费": mold_cost,
        "版费明细":mold_details,
        "总费用": total_cost
    }

    return cost_details


# 设置所有变量值 (保持不变)
startup_fee_val = 180.00
price_levels_val = [(1000, 0.20), (10000, 0.18), (float('inf'), 0.13)]
material_price_per_sqm_val = 4.00
mold_fixed_cost_val = 20.00
mold_price_per_sqcm_val = 0.30
area_padding_cm_val = 2.0

# 示例使用
quantity = 12000
area = 5 * 8  # 5cm x 8cm
num_of_positions = 1
num_of_molds = 1

cost_details = calculate_gilding_cost(quantity, area, num_of_positions, num_of_molds,
                                     startup_fee_val, price_levels_val, material_price_per_sqm_val,
                                     mold_fixed_cost_val, mold_price_per_sqcm_val, area_padding_cm_val)

if cost_details is not None:
    for key, value in cost_details.items():  # 遍历字典并打印
        print(f"{key}: {value}")
else:
    print("输入参数无效，请检查输入。")

# print("-----------------------------------")
# quantity = 500
# area = 10 * 10
# num_of_positions = 2
# num_of_molds = 2
# cost_details = calculate_gilding_cost(quantity, area, num_of_positions, num_of_molds,
#                                      startup_fee_val, price_levels_val, material_price_per_sqm_val,
#                                      mold_fixed_cost_val, mold_price_per_sqcm_val, area_padding_cm_val)

# if cost_details is not None:
#     for key, value in cost_details.items():  # 遍历字典并打印
#         print(f"{key}: {value}")
# else:
#     print("输入参数无效，请检查输入。")