# 设定值
SETTINGS = {
    "starting_fee": 180.00,  # 开机费
    "min_consumption": 0.00,  # 最低消费
    "material_price_per_sqm": 4.00,  # 材料每平方米单价
    "mold_price_per_sqcm": 0.003,  # 模版每平方厘米单价
    "min_mold_fee": 20.00,  # 最低模版费用
    "processing_price_tiers": {  # 加工费分段价格
        1000: 0.20,
        10000: 0.18,
        float('inf'): 0.13,
    },
}

def calculate_processing_fee(print_quantity):
    """计算加工费（分段叠加）。"""
    print("\n--- 计算烫金加工费用 ---")
    processing_fee = 0
    previous_limit = 0
    print(f"初始加工费：￥{processing_fee:.2f}")
    print(f"初始前一个区间的上限：{previous_limit}")
    for limit, price in sorted(SETTINGS["processing_price_tiers"].items()):
        print(f"\n当前区间上限：{limit}，单价：￥{price:.2f}")
        quantity_in_tier = max(0, min(print_quantity - previous_limit, limit - previous_limit))
        print(f"当前区间计算数量：{print_quantity} - {previous_limit} 和 {limit} - {previous_limit} 的最小值，结果为：{quantity_in_tier}")
        processing_fee += quantity_in_tier * price
        print(f"当前区间费用：{quantity_in_tier} * ￥{price:.2f} = ￥{quantity_in_tier * price:.2f}")
        print(f"累计加工费：￥{processing_fee:.2f}")
        previous_limit = limit
        print(f"更新前一个区间的上限为：{previous_limit}")
        if print_quantity <= limit:
            print(f"印张数量 {print_quantity} 小于等于当前区间上限 {limit}，加工费计算完成。")
            break
    print(f"最终烫金加工费用：￥{processing_fee:.2f}")
    return processing_fee

def calculate_gilding_cost(print_quantity, gilding_positions):
    """计算烫金总费用。"""
    if print_quantity < 0 or not gilding_positions:
        return None

    total_cost = SETTINGS["starting_fee"]  # 开机费
    print(f"\n--- 计算总费用 ---")
    print(f"初始总费用（开机费）：￥{total_cost:.2f}")

    # 1. 烫金加工费用
    processing_fee = calculate_processing_fee(print_quantity)
    total_cost += processing_fee
    print(f"加上烫金加工费用后总费用：￥{total_cost:.2f}")

    # 计算每个工艺位的费用
    for i, pos in enumerate(gilding_positions):
        w, h, m = pos["width"], pos["height"], pos["mold_quantity"]
        area = w * h
        print(f"\n--- 工艺位 {i+1} ---")
        print(f"单模的烫金面积：{area} 平方厘米")
        print(f"模数：{m}")

        # 2. 烫金用料费
        material_area = (w + 2) * (h + 2) / 10000
        material_fee = material_area * print_quantity * SETTINGS["material_price_per_sqm"]
        total_cost += material_fee
        print(f"烫金用料费：￥{material_fee:.2f} (计算方式：({w} + 2) * ({h} + 2) / 10000 * {print_quantity} * {SETTINGS['material_price_per_sqm']:.2f})")
        print(f"加上烫金用料费后总费用：￥{total_cost:.2f}")

        # 3. 烫金版费用
        mold_fee = max(area * m * SETTINGS["mold_price_per_sqcm"], SETTINGS["min_mold_fee"])
        total_cost += mold_fee
        print(f"烫金版费用：￥{mold_fee:.2f} (计算方式：max({area} * {m} * {SETTINGS['mold_price_per_sqcm']:.3f}, {SETTINGS['min_mold_fee']:.2f}))")
        print(f"加上烫金版费用后总费用：￥{total_cost:.2f}")

    total_cost = max(total_cost, SETTINGS["min_consumption"])
    print(f"\n计算最低消费后总费用：￥{total_cost:.2f}")
    return total_cost

def print_gilding_details(print_quantity, gilding_positions):
    """打印烫金费用的详细信息。"""
    if print_quantity < 0 or not gilding_positions:
        print("输入参数错误")
        return

    print(f"\n--- 烫金费用详细计算 ---")
    print(f"印张数量：{print_quantity}")
    print(f"开机费：￥{SETTINGS['starting_fee']:.2f}")

    total_cost = calculate_gilding_cost(print_quantity, gilding_positions)

    print(f"\n最终结算费用：￥{total_cost:.2f}")


# 示例 (单个，详细)
print_quantity = 15000
gilding_positions = [{"width": 5, "height": 10, "mold_quantity": 1},
                     {"width": 6, "height": 10, "mold_quantity": 1},]
print_gilding_details(print_quantity, gilding_positions)

print("\n---------------------\n")

# print_quantity = 500
# gilding_positions = [{"width": 5, "height": 10, "mold_quantity": 2}]
# SETTINGS["min_consumption"] = 500 #设定最低消费为500
# print_gilding_details(print_quantity, gilding_positions)
# SETTINGS["min_consumption"] = 0 #还原最低消费为0