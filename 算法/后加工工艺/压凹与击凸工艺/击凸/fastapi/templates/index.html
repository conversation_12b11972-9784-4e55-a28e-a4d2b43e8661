<!DOCTYPE html>
<html>
<head>
    <title>击凸加工费计算</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        form div { margin-bottom: 10px; }
        label { display: inline-block; width: 150px; }
        .result { margin-top: 20px; border-top: 1px solid #ccc; padding-top: 10px; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>击凸加工费计算</h1>

    <form action="/calculate" method="post">
        <div>
            <label for="sheet_count">数量 (张):</label>
            <input type="number" id="sheet_count" name="sheet_count" value="{{ sheet_count if sheet_count is not none else '' }}" required>
        </div>
        <div>
            <label for="hit_count">击凸次数:</label>
            <input type="number" id="hit_count" name="hit_count" value="{{ hit_count if hit_count is not none else '' }}" required>
        </div>
        <div>
            <label for="area_list_cm2_str">击凸面积列表 (cm², 逗号分隔):</label>
            <input type="text" id="area_list_cm2_str" name="area_list_cm2_str" value="{{ area_list_cm2_str if area_list_cm2_str is not none else '' }}" required>
        </div>
        <div>
            <button type="submit">计算</button>
        </div>
    </form>

    {% if error %}
        <div class="error">
            <h2>错误:</h2>
            <p>{{ error }}</p>
        </div>
    {% elif processing_fee is not none and plate_cost is not none and total_cost is not none %}
        <div class="result">
            <h2>计算结果:</h2>
            <p>加工费: {{ "%.2f"|format(processing_fee) }}</p>
            <p>击凸版费: {{ "%.2f"|format(plate_cost) }}</p>
            <p>总价: {{ "%.2f"|format(total_cost) }}</p>
        </div>
    {% endif %}
</body>
</html>
