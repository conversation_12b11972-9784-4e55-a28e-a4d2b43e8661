from fastapi import FastAPI, Request, Form
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
import sys
import os
from typing import List

# 将父目录添加到 sys.path，以便导入击凸的加工费计算.py
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 尝试导入原始计算文件中的类
try:
    from 击凸的加工费计算 import JiTuProcessCalculator
except ImportError as e:
    print(f"Error importing calculation module: {e}")
    JiTuProcessCalculator = None # 如果导入失败，将类设为 None

app = FastAPI()
templates = Jinja2Templates(directory="后加工工艺/压凹与击凸工艺/击凸/fastapi/templates")

# 硬编码示例参数
pricing_parameters = {
    'startup_fee': 100.00,
    'hit_price': 15.00,
    'quantity_ranges': [
        {'upper_bound': 1000, 'unit_price': 0.05},
        {'upper_bound': 10000, 'unit_price': 0.05},
        {'upper_bound': None, 'unit_price': 0.04}
    ]
}

plate_cost_parameters = {
    'min_cost': 35.00,
    'unit_price_per_cm2': 0.5
}


@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """
    Serve the index.html template.
    """
    return templates.TemplateResponse(
        "index.html",
        {
            "request": request,
            "processing_fee": None,
            "plate_cost": None,
            "total_cost": None,
            "error": None,
            "sheet_count": None,
            "hit_count": None,
            "area_list_cm2_str": None
        }
    )

@app.post("/calculate", response_class=HTMLResponse)
async def calculate_embossing_cost(
    request: Request,
    sheet_count: int = Form(...),
    hit_count: int = Form(...),
    area_list_cm2_str: str = Form(...)
):
    """
    Receive data from the form, calculate the cost, and return the result to the template.
    """
    processing_fee = None
    plate_cost = None
    total_cost = None
    error_message = None

    if JiTuProcessCalculator:
        try:
            # 解析 area_list_cm2_str 为浮点数列表
            area_list_cm2 = [float(area.strip()) for area in area_list_cm2_str.split(',') if area.strip()]
            
            calculator = JiTuProcessCalculator(hit_count, area_list_cm2)
            processing_fee, plate_cost, total_cost = calculator.calculate_total_cost(
                sheet_count,
                pricing_parameters,
                plate_cost_parameters
            )
        except ValueError:
            error_message = "Invalid input for area list. Please enter comma-separated numbers."
        except Exception as e:
            error_message = f"Calculation error: {e}"
    else:
        error_message = "Calculation module not loaded. Please check the import path and class name."

    return templates.TemplateResponse(
        "index.html",
        {
            "request": request,
            "processing_fee": processing_fee,
            "plate_cost": plate_cost,
            "total_cost": total_cost,
            "error": error_message,
            "sheet_count": sheet_count, # Pass back inputs to retain form values
            "hit_count": hit_count,
            "area_list_cm2_str": area_list_cm2_str
        }
    )

# 要运行此应用，请在终端中导航到当前目录并执行:
# uvicorn 后加工工艺.压凹与击凸工艺.击凸.fastapi.main:app --reload
