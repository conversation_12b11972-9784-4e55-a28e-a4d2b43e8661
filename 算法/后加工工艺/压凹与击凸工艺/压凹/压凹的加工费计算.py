from typing import List, Dict, Union, <PERSON><PERSON>

def calculate_tiered_price(quantity: float, tiers: List[Dict]) -> float:
    # ... (分段计价函数代码保持不变)
    if not isinstance(quantity, (int, float)):
        raise TypeError("quantity 必须为 int 或 float 类型")
    if quantity < 0:
        raise ValueError("quantity 必须为非负数")

    if not isinstance(tiers, list):
        raise TypeError("tiers 必须为 list 类型")
    if not all(isinstance(tier, dict) and 'upper_bound' in tier and 'unit_price' in tier and isinstance(tier['unit_price'], (int, float)) for tier in tiers):
        raise ValueError("tiers 格式不正确")
    if not all(isinstance(tier.get('upper_bound'), (int, float, type(None))) for tier in tiers):
      raise TypeError("tiers 中 'upper_bound' 的值必须为 int, float 或 None")

    # 验证区间是否按严格升序排列且无重叠
    previous_upper_bound: Union[int, float, None] = None
    for tier in tiers:
        upper_bound = tier.get('upper_bound')
        if previous_upper_bound is not None and upper_bound is not None and previous_upper_bound >= upper_bound:
            raise ValueError("tiers 必须按 upper_bound 严格升序排列，且不能有重叠区间")
        previous_upper_bound = upper_bound

    total_price = 0.0
    remaining_quantity = quantity
    sum_of_previous_upper_bounds = 0

    for i, tier in enumerate(tiers):
        upper_bound = tier.get('upper_bound')
        unit_price = tier['unit_price']

        # 核心逻辑：修正后的计算方式
        quantity_in_tier = min(remaining_quantity, upper_bound - (sum_of_previous_upper_bounds if upper_bound is not None and i>0 else 0) if upper_bound is not None else remaining_quantity)

        total_price += quantity_in_tier * unit_price
        remaining_quantity -= quantity_in_tier
        if upper_bound is not None:
            sum_of_previous_upper_bounds += upper_bound - (tiers[i-1].get('upper_bound',0) if i>0 else 0)

        if remaining_quantity <= 0:
            break

    return total_price

class YaOuProcessCalculator:
    def __init__(self, hit_count: int, area_list_cm2: List[float]):
        self.hit_count = hit_count
        self.area_list_cm2 = area_list_cm2

    def calculate_plate_cost(self, plate_cost_parameters: Dict) -> float:
        # ... (版费计算函数代码保持不变)
        total_plate_cost = 0
        for area_cm2 in self.area_list_cm2:
            cost = max(area_cm2 * plate_cost_parameters['unit_price_per_cm2'], plate_cost_parameters['min_cost'])
            total_plate_cost += cost
        return total_plate_cost

    def calculate_processing_fee(self, sheet_count: int, pricing_parameters: Dict) -> float:
        """计算加工费。一个订单的一个工艺只收取一次启动费用。"""
        quantity_ranges = pricing_parameters['quantity_ranges']
        processing_fee = calculate_tiered_price(sheet_count, quantity_ranges)
        processing_fee += pricing_parameters['startup_fee']  # 只加一次启动费用
        processing_fee += pricing_parameters['hit_price'] * self.hit_count
        return processing_fee

    def calculate_total_cost(self, sheet_count: int, pricing_parameters: Dict, plate_cost_parameters: Dict) -> Tuple[float, float, float]:
        processing_fee = self.calculate_processing_fee(sheet_count, pricing_parameters)
        plate_cost = self.calculate_plate_cost(plate_cost_parameters)
        total_cost = processing_fee + plate_cost
        return processing_fee, plate_cost, total_cost

# 示例数据 (保持不变)
sheet_count = 550
hit_count = 2
area_list_cm2 = [20.0, 120.0]

pricing_parameters = {
    'startup_fee': 100.00,
    'hit_price': 15.00,
    'quantity_ranges': [
        {'upper_bound': 1000, 'unit_price': 0.05},
        {'upper_bound': 10000, 'unit_price': 0.05},
        {'upper_bound': None, 'unit_price': 0.04}
    ]
}

plate_cost_parameters = {
    'min_cost': 35.00,
    'unit_price_per_cm2': 0.5
}

# 使用示例数据进行计算
calculator = YaOuProcessCalculator(hit_count, area_list_cm2)
processing_fee, plate_cost, total_cost = calculator.calculate_total_cost(sheet_count, pricing_parameters, plate_cost_parameters)

# 打印结果
print(f"数量: {sheet_count}")
print(f"加工费: {processing_fee:.2f}")
print(f"压凹版费: {plate_cost:.2f}")
print(f"总价: {total_cost:.2f}")

print("\n每个压凹位的版费：")
individual_plate_costs = [max(area_cm2 * plate_cost_parameters['unit_price_per_cm2'], plate_cost_parameters['min_cost']) for area_cm2 in calculator.area_list_cm2]
for i, cost in enumerate(individual_plate_costs):
    print(f"第 {i+1} 个压凹位版费：{cost:.2f}")