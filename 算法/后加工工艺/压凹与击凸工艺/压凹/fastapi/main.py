from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from typing import List, Dict, Union, Tuple
from pydantic import BaseModel

# 从原始文件复制的计算逻辑
def calculate_tiered_price(quantity: float, tiers: List[Dict]) -> float:
    if not isinstance(quantity, (int, float)):
        raise TypeError("quantity 必须为 int 或 float 类型")
    if quantity < 0:
        raise ValueError("quantity 必须为非负数")

    if not isinstance(tiers, list):
        raise TypeError("tiers 必须为 list 类型")
    if not all(isinstance(tier, dict) and 'upper_bound' in tier and 'unit_price' in tier and isinstance(tier['unit_price'], (int, float)) for tier in tiers):
        raise ValueError("tiers 格式不正确")
    if not all(isinstance(tier.get('upper_bound'), (int, float, type(None))) for tier in tiers):
      raise TypeError("tiers 中 'upper_bound' 的值必须为 int, float 或 None")

    previous_upper_bound: Union[int, float, None] = None
    for tier in tiers:
        upper_bound = tier.get('upper_bound')
        if previous_upper_bound is not None and upper_bound is not None and previous_upper_bound >= upper_bound:
            raise ValueError("tiers 必须按 upper_bound 严格升序排列，且不能有重叠区间")
        previous_upper_bound = upper_bound

    total_price = 0.0
    remaining_quantity = quantity
    sum_of_previous_upper_bounds = 0

    for i, tier in enumerate(tiers):
        upper_bound = tier.get('upper_bound')
        unit_price = tier['unit_price']

        quantity_in_tier = min(remaining_quantity, upper_bound - (sum_of_previous_upper_bounds if upper_bound is not None and i>0 else 0) if upper_bound is not None else remaining_quantity)

        total_price += quantity_in_tier * unit_price
        remaining_quantity -= quantity_in_tier
        if upper_bound is not None:
            sum_of_previous_upper_bounds += upper_bound - (tiers[i-1].get('upper_bound',0) if i>0 else 0)

        if remaining_quantity <= 0:
            break

    return total_price

class YaOuProcessCalculator:
    def __init__(self, hit_count: int, area_list_cm2: List[float]):
        self.hit_count = hit_count
        self.area_list_cm2 = area_list_cm2

    def calculate_plate_cost(self, plate_cost_parameters: Dict) -> float:
        total_plate_cost = 0
        for area_cm2 in self.area_list_cm2:
            cost = max(area_cm2 * plate_cost_parameters['unit_price_per_cm2'], plate_cost_parameters['min_cost'])
            total_plate_cost += cost
        return total_plate_cost

    def calculate_processing_fee(self, sheet_count: int, pricing_parameters: Dict) -> float:
        """计算加工费。一个订单的一个工艺只收取一次启动费用。"""
        quantity_ranges = pricing_parameters['quantity_ranges']
        processing_fee = calculate_tiered_price(sheet_count, quantity_ranges)
        processing_fee += pricing_parameters['startup_fee']  # 只加一次启动费用
        processing_fee += pricing_parameters['hit_price'] * self.hit_count
        return processing_fee

    def calculate_total_cost(self, sheet_count: int, pricing_parameters: Dict, plate_cost_parameters: Dict) -> Tuple[float, float, float]:
        processing_fee = self.calculate_processing_fee(sheet_count, pricing_parameters)
        plate_cost = self.calculate_plate_cost(plate_cost_parameters)
        total_cost = processing_fee + plate_cost
        return processing_fee, plate_cost, total_cost

# FastAPI 应用设置
app = FastAPI()
templates = Jinja2Templates(directory="templates")

# 定义请求体的数据模型
class PricingParameters(BaseModel):
    startup_fee: float
    hit_price: float
    quantity_ranges: List[Dict]

class PlateCostParameters(BaseModel):
    min_cost: float
    unit_price_per_cm2: float

class CalculationRequest(BaseModel):
    sheet_count: int
    hit_count: int
    area_list_cm2: List[float]
    pricing_parameters: PricingParameters
    plate_cost_parameters: PlateCostParameters

# 根路由，返回 HTML 页面
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

# 计算路由
@app.post("/calculate")
async def calculate_yaou_cost(request_data: CalculationRequest):
    try:
        calculator = YaOuProcessCalculator(request_data.hit_count, request_data.area_list_cm2)
        processing_fee, plate_cost, total_cost = calculator.calculate_total_cost(
            request_data.sheet_count,
            request_data.pricing_parameters.model_dump(), # 使用 model_dump() 获取字典形式的数据
            request_data.plate_cost_parameters.model_dump() # 使用 model_dump() 获取字典形式的数据
        )
        return {
            "processing_fee": round(processing_fee, 2),
            "plate_cost": round(plate_cost, 2),
            "total_cost": round(total_cost, 2)
        }
    except (TypeError, ValueError) as e:
        return {"error": str(e)}
    except Exception as e:
        return {"error": "An unexpected error occurred: " + str(e)}
