# 压凹工艺计算程序变量说明文档

## 类属性变量

### 1. num_hits (int)
- **描述**：击位数量/压位数
- **用途**：表示压凹工艺中的压位数量
- **限制**：必须为正整数

### 2. areas (list)
- **描述**：每个压位的面积列表
- **单位**：平方厘米
- **限制**：必须为包含正数的非空列表

## 计费参数变量 (pricing_params)

### 1. min_cost (float)
- **描述**：最低消费额
- **包含**：首次击位费用
- **示例值**：80.00

### 2. hit_price (float)
- **描述**：额外击位单价
- **示例值**：15.00

### 3. quantity_ranges (list)
- **描述**：数量区间和单价列表
- **结构**：包含字典元素，每个字典有两个键：
  - `upper` (float/int)：区间上限
  - `unit_price` (float)：该区间的单价
- **限制**：必须按upper值升序排列

## 压凹版费参数变量 (plate_cost_params)

### 1. base_cost (float)
- **描述**：每个压位的最低消费
- **示例值**：35.00

### 2. unit_price (float)
- **描述**：每平方厘米的单价
- **示例值**：0.64

## 其他重要变量

### 1. NSP (int)
- **描述**：印张数量
- **用途**：用于计算加工费
- **示例值**：19000

### 2. processing_fee (float)
- **描述**：加工费
- **计算**：根据印张数量和计费参数计算得出

### 3. plate_cost (float)
- **描述**：压凹版费
- **计算**：根据压位面积和版费参数计算得出

### 4. total_cost (float)
- **描述**：总价
- **计算**：加工费 + 压凹版费

## 注意事项
这些变量构成了完整的压凹工艺计价系统，通过合理设置这些参数，可以灵活地计算不同情况下的压凹加工费用。
