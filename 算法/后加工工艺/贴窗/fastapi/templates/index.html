<!DOCTYPE html>
<html>
<head>
    <title>贴窗计算</title>
</head>
<body>
    <h1>贴窗加工费计算</h1>
    <form id="calculationForm">
        <label for="order_quantity">订单数量:</label><br>
        <input type="number" id="order_quantity" name="order_quantity" required><br>

        <label for="length">长度 (mm):</label><br>
        <input type="number" id="length" name="length" step="0.01" required><br>

        <label for="width">宽度 (mm):</label><br>
        <input type="number" id="width" name="width" step="0.01" required><br>

        <label for="film_price_per_square_meter">胶片每平方米价格:</label><br>
        <input type="number" id="film_price_per_square_meter" name="film_price_per_square_meter" step="0.01" required><br><br>

        <input type="submit" value="计算">
    </form>

    <div id="result">
        <!-- 计算结果将显示在这里 -->
    </div>

    <script>
        document.getElementById('calculationForm').addEventListener('submit', async function(event) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());

            // 将字符串类型的数字转换为浮点数或整数
            data.order_quantity = parseInt(data.order_quantity);
            data.length = parseFloat(data.length);
            data.width = parseFloat(data.width);
            data.film_price_per_square_meter = parseFloat(data.film_price_per_square_meter);


            const response = await fetch('/calculate', {
                method: 'POST',
                body: new URLSearchParams(formData) // 使用 URLSearchParams 发送表单数据
            });

            const result = await response.json();
            if (result.error) {
                document.getElementById('result').innerText = '计算出错: ' + result.error;
            } else {
                document.getElementById('result').innerText = '总费用: ' + result.cost.toFixed(2);
            }
        });
    </script>
</body>
</html>
