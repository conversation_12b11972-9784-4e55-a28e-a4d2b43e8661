# 变量定义区域 (保持不变)
UV_CONFIG = {
    "startup_fee": 100.00,
    "minimum_cost": 260.00,
    "material_price_per_sqm": 4.50,
    "price_tiers": {
        (1, 1000): 0.20,
        (1001, 10000): 0.18,
        (10001, float('inf')): 0.16
    }
}

def calculate_uv_cost(print_quantity, uv_area=0, config=UV_CONFIG):
    """
    计算局部UV后加工的费用，并打印详细计算明细。精简输出。

    Args:
        print_quantity: 印张数量。
        uv_area: 每张印品的UV工艺面积（平方米）。
        config: UV配置字典。

    Returns:
        一个字典，包含各项费用和总费用；如果输入参数有误，则返回错误信息。
    """
    try:
        print_quantity = int(print_quantity)
        uv_area = float(uv_area)

        if print_quantity <= 0 or uv_area < 0:
            return "输入参数必须为正数（印张数量）或非负数（UV面积）"

        startup_fee = config["startup_fee"]
        minimum_cost = config["minimum_cost"]
        material_price = config["material_price_per_sqm"]
        price_tiers = config["price_tiers"]

        processing_cost = 0
        remaining_quantity = print_quantity

        print("加工费明细:")
        for (lower, upper), price in price_tiers.items():
            quantity_in_tier = min(remaining_quantity, upper - lower + 1)
            if quantity_in_tier > 0:
                tier_cost = quantity_in_tier * price
                print(f"{lower}-{upper}张: {quantity_in_tier}*{price:.2f}={tier_cost:.2f}")
                processing_cost += tier_cost
                remaining_quantity -= quantity_in_tier
        pre_min_cost = startup_fee + processing_cost
        print(f"开机费+{processing_cost:.2f}={pre_min_cost:.2f}")
        processing_cost = max(pre_min_cost, minimum_cost)
        print(f"取大值({pre_min_cost:.2f},{minimum_cost:.2f})={processing_cost:.2f}")

        uv_material_cost = uv_area * print_quantity * material_price if uv_area > 0 else 0
        if uv_material_cost > 0:
            print("UV用料费明细:")
            print(f"{uv_area:.2f}*{print_quantity}*{material_price:.2f}={uv_material_cost:.2f}")

        total_cost = processing_cost + uv_material_cost

        return {
            "加工费": processing_cost,
            "UV用料费": uv_material_cost,
            "总费用": total_cost
        }
    except ValueError:
        return "输入参数类型错误，请检查输入是否为数字"

# 测试用例 (保持不变)
test_cases = [
    (500, 0.5),
    (1500, 0.3),
    (12000, 0.1)
]

for print_quantity, uv_area in test_cases:
    result = calculate_uv_cost(print_quantity, uv_area)
    print(f"\n印张数量：{print_quantity}, UV面积：{uv_area}")
    print(result)
    print("---")