<!DOCTYPE html>
<html>
<head>
    <title>局部UV费用计算</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        label { display: block; margin-bottom: 5px; }
        input { margin-bottom: 10px; padding: 8px; }
        button { padding: 10px 15px; background-color: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        #result { margin-top: 20px; border: 1px solid #ccc; padding: 15px; }
        #result h3 { margin-top: 0; }
    </style>
</head>
<body>
    <h1>局部UV费用计算</h1>

    <form id="uvForm">
        <label for="print_quantity">印张数量:</label>
        <input type="number" id="print_quantity" name="print_quantity" required><br>

        <label for="uv_area">UV面积 (平方米/张):</label>
        <input type="number" id="uv_area" name="uv_area" step="0.01" value="0.0" required><br>

        <button type="submit">计算</button>
    </form>

    <div id="result">
        <h3>计算结果:</h3>
        <p id="total_cost"></p>
        <p id="processing_cost"></p>
        <p id="material_cost"></p>
        <div id="details"></div>
    </div>

    <script>
        document.getElementById('uvForm').addEventListener('submit', async function(event) {
            event.preventDefault();

            const printQuantity = document.getElementById('print_quantity').value;
            const uvArea = document.getElementById('uv_area').value;

            const response = await fetch('/calculate_uv/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    print_quantity: parseInt(printQuantity),
                    uv_area: parseFloat(uvArea)
                })
            });

            const result = await response.json();
            const resultDiv = document.getElementById('result');
            const totalCostP = document.getElementById('total_cost');
            const processingCostP = document.getElementById('processing_cost');
            const materialCostP = document.getElementById('material_cost');
            const detailsDiv = document.getElementById('details');

            detailsDiv.innerHTML = ''; // Clear previous details

            if (result.error) {
                totalCostP.textContent = `错误: ${result.error}`;
                processingCostP.textContent = '';
                materialCostP.textContent = '';
            } else {
                totalCostP.textContent = `总费用: ${result.总费用.toFixed(2)} 元`;
                processingCostP.textContent = `加工费: ${result.加工费.toFixed(2)} 元`;
                materialCostP.textContent = `UV用料费: ${result.UV用料费.toFixed(2)} 元`;

                if (result.加工费明细 && result.加工费明细.length > 0) {
                    const processingDetailsTitle = document.createElement('p');
                    processingDetailsTitle.innerHTML = '<strong>加工费明细:</strong>';
                    detailsDiv.appendChild(processingDetailsTitle);
                    const processingList = document.createElement('ul');
                    result.加工费明细.forEach(item => {
                        const li = document.createElement('li');
                        li.textContent = item;
                        processingList.appendChild(li);
                    });
                    detailsDiv.appendChild(processingList);
                }

                 if (result.UV用料费明细 && result.UV用料费明细.length > 0) {
                    const materialDetailsTitle = document.createElement('p');
                    materialDetailsTitle.innerHTML = '<strong>UV用料费明细:</strong>';
                    detailsDiv.appendChild(materialDetailsTitle);
                    const materialList = document.createElement('ul');
                    result.UV用料费明细.forEach(item => {
                        const li = document.createElement('li');
                        li.textContent = item;
                        materialList.appendChild(li);
                    });
                    detailsDiv.appendChild(materialList);
                }
            }
        });
    </script>
</body>
</html>
