def calculate_dkg(w: float) -> str | int:
    """
    根据包装盒宽度(W)计算吊口高(DKG)。

    Args:
        w: 包装盒宽度 (mm)

    Returns:
        DKG尺寸(mm)或表示未定义的字符串。
    """
    # 1. 优先处理无效输入范围
    if w < 25:
        return "[未定义]"
    
    # 2. 简化的条件判断链
    elif w <= 30:
        return 30
    elif w <= 60:
        return 35
    elif w <= 90:
        return 37
    elif w <= 120:
        return 40
    elif w <= 180:
        return 43
    else:  # w > 180
        return 45

# --- 测试用例 ---
# 验证修复的逻辑缺陷
print(f"W=24.9, DKG={calculate_dkg(24.9)}") # 预期: [未定义]
print(f"W=10, DKG={calculate_dkg(10)}")     # 预期: [未定义]

# 验证原始逻辑的边界和中间值
print(f"W=25, DKG={calculate_dkg(25)}")     # 预期: 30
print(f"W=30, DKG={calculate_dkg(30)}")     # 预期: 30
print(f"W=30.1, DKG={calculate_dkg(30.1)}") # 预期: 35
print(f"W=90, DKG={calculate_dkg(90)}")     # 预期: 37
print(f"W=180, DKG={calculate_dkg(180)}")   # 预期: 43
print(f"W=200, DKG={calculate_dkg(200)}")   # 预期: 45