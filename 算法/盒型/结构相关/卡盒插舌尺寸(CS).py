def calculate_cs(l: float, w: float) -> float:
    """
    根据包装盒长度(L)和宽度(W)计算卡盒插舌尺寸(CS)。

    Args:
        l: 包装盒长度 (mm)
        w: 包装盒宽度 (mm)

    Returns:
        CS尺寸(mm)。
    """
    # 健壮性检查，避免除以零
    if w <= 0:
        # 或者可以根据业务需求返回一个错误/默认值
        raise ValueError("宽度(W)必须是正数")

    # 1. 最高优先级的特殊情况
    if l <= 25 and w <= 25:
        return 10

    # 2. W <= 40 的情况，结果受长宽比影响
    elif w <= 40:
        aspect_ratio = l / w
        if w <= 20:
            return 12 if aspect_ratio > 3 else 10
        elif w <= 30:
            return 13.5 if aspect_ratio > 3 else 12
        else:  # 30 < w <= 40
            return 13.5 if aspect_ratio > 3 else 13

    # 3. W > 40 的情况，结果仅与 W 相关
    elif w <= 65:
        return 15
    elif w <= 80:
        return 16
    elif w <= 100:
        return 17
    elif w <= 180:
        return 18
    else:  # w > 180
        return 19

# --- 测试用例 ---
# 特殊情况
print(f"L=20, W=20 -> CS={calculate_cs(20, 20)}")     # 预期: 10
# W <= 20 分支
print(f"L=70, W=20 -> CS={calculate_cs(70, 20)}")     # 预期: 12 (L/W=3.5 > 3)
print(f"L=50, W=20 -> CS={calculate_cs(50, 20)}")     # 预期: 10 (L/W=2.5 <= 3)
# 20 < W <= 30 分支
print(f"L=100, W=25 -> CS={calculate_cs(100, 25)}")   # 预期: 13.5 (L/W=4 > 3)
print(f"L=60, W=25 -> CS={calculate_cs(60, 25)}")     # 预期: 12 (L/W=2.4 <= 3)
# 30 < W <= 40 分支
print(f"L=150, W=35 -> CS={calculate_cs(150, 35)}")   # 预期: 13.5 (L/W > 3)
print(f"L=100, W=35 -> CS={calculate_cs(100, 35)}")   # 预期: 13 (L/W <= 3)
# W > 40 分支
print(f"L=200, W=50 -> CS={calculate_cs(200, 50)}")   # 预期: 15
print(f"L=200, W=70 -> CS={calculate_cs(200, 70)}")   # 预期: 16
print(f"L=200, W=90 -> CS={calculate_cs(200, 90)}")   # 预期: 17
print(f"L=200, W=150 -> CS={calculate_cs(200, 150)}") # 预期: 18
print(f"L=200, W=200 -> CS={calculate_cs(200, 200)}") # 预期: 19