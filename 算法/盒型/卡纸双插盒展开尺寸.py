def calculate_cs(L, W):
  """
  计算插舌宽度

  Args:
    L (float): 盒子的长度
    W (float): 盒子的宽度

  Returns:
    float: 插舌的宽度
  """
  ratio = L / W
  if (L <= 25) and (W <= 25):
    CS = 10
  elif (W <= 20) and (ratio > 3):
    CS = 12
  elif (W <= 20) and (ratio <= 3):
    CS = 10
  elif (20 < W <= 30) and (ratio > 3):
    CS = 13.5
  elif (20 < W <= 30) and (ratio <= 3):
    CS = 12
  elif (30 < W <= 40) and (ratio > 3):
    CS = 13.5
  elif (30 < W <= 40) and (ratio <= 3):
    CS = 14
  elif (40 < W <= 65):
    CS = 15
  elif (65 < W <= 80):
    CS = 16
  elif (80 < W <= 100):
    CS = 17
  elif (100 < W <= 180):
    CS = 18
  elif (180 < W):
    CS = 19
  return CS


def calculate_unfold_size(L, W, H,Z):
  """
  计算卡纸双插盒展开尺寸

  Args:
    L (float): 盒子的长度
    W (float): 盒子的宽度
    H (float): 盒子的高度

  Returns:
    tuple: 盒子展开后的长和宽
  """


    

  # 计算插舌宽度
  CS = calculate_cs(L, W)

  unfold_length = L * 2 + W * 2 + Z
  unfold_width = H + W * 2 + CS * 2

  return unfold_length, unfold_width


# 测试用例
L = 100
W = 50
H = 30
Z = 10   # 卡纸包装的粘位宽度 Z

unfold_length, unfold_width = calculate_unfold_size(L, W, H, Z)

print(f"卡纸双插盒展开尺寸：长 = {unfold_length}, 宽 = {unfold_width}")