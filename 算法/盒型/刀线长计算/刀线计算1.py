# -*- coding: utf-8 -*-

def calculate_die_line_rte(L, W, H, CS, constant=50):
    """
    计算标准反向插入盒 (RTE) 的刀线长度。
    
    该函数使用一个基于经验的参数化估算模型。
    系数（如 12, 6, 4）是从标准RTE盒型展开图中总结出的经验值。

    Args:
        L (float): 包装盒长度 (mm)
        W (float): 包装盒宽度 (mm)
        H (float): 包装盒高度 (mm)
        CS (float): 插入舌的长度 (mm)
        constant (float, optional): 用于补偿圆角、锁口等杂项的常数修正项。默认为 50。

    Returns:
        float: 估算的刀线总长度 (mm)
    """
    # 公式解读:
    # L*12 + W*12: 综合估算所有与长、宽相关的水平线及盖板轮廓线。
    # H*6: 估算5条主要的垂直折线并包含一个修正系数。
    # CS*4: 估算上下两个插入舌的主要轮廓线。
    die_line_length = (L * 12) + (W * 12) + (H * 6) + (CS * 4) + constant
    return die_line_length

def calculate_die_line_ste(L, W, H, CS, constant=30):
    """
    计算同向插入盒 (STE) 的刀线长度。

    该函数同样使用参数化估算模型，但其系数与RTE盒型不同，
    以反映STE展开图的独特几何形状。

    Args:
        L (float): 包装盒长度 (mm)
        W (float): 包装盒宽度 (mm)
        H (float): 包装盒高度 (mm)
        CS (float): 插入舌的长度 (mm)
        constant (float, optional): 常数修正项。默认为 30。

    Returns:
        float: 估算的刀线总长度 (mm)
    """
    # 公式解读:
    # (10*L + 12*W): 估算STE盒型所有水平线的总和。
    # (4*H + 4*CS): 估算所有垂直线的总和。
    horizontal_lines = 10 * L + 12 * W
    vertical_lines = 4 * H + 4 * CS
    die_line_length = horizontal_lines + vertical_lines + constant
    return die_line_length

def calculate_die_line_rsc(L, W, H):
    """
    计算标准运输箱 (RSC) 的刀线长度。

    该函数基于RSC箱展开图的精确几何构成，因此准确度很高。

    Args:
        L (float): 纸箱长度 (mm)
        W (float): 纸箱宽度 (mm)
        H (float): 纸箱高度 (mm)

    Returns:
        float: 计算出的刀线总长度 (mm)
    """
    # 公式解读:
    # (2*L + 2*W) * 4: 展开图上下轮廓线 + 主体与盖板间的两条折线。
    # 4*H: 4条分隔主面板的垂直折线。
    # 3*W: 分隔8个盖板的6条垂直切线（每条长W/2，共3W）。
    die_line_length = (2 * L + 2 * W) * 4 + (4 * H) + (3 * W)
    return die_line_length

# --- 示例：如何使用以上函数 ---

if __name__ == "__main__":
    # 设定一个包装盒的输入值
    box_L = 100  # 长度 (mm)
    box_W = 50   # 宽度 (mm)
    box_H = 100  # 高度 (mm)

    # 对于RTE和STE盒，我们需要插入舌(CS)的尺寸
    # 在实际应用中，CS的值可能由另一个函数根据W计算得出
    box_CS = 15  # 插入舌长度 (mm)

    print("========= 刀线长度计算示例 =========")
    print(f"输入尺寸: L={box_L}mm, W={box_W}mm, H={box_H}mm, CS={box_CS}mm\n")

    # 1. 计算RTE盒型的刀线长度
    rte_length = calculate_die_line_rte(box_L, box_W, box_H, box_CS)
    print(f"标准反向插入盒 (RTE) 的刀线长度估算为: {rte_length:.2f} mm")

    # 2. 计算STE盒型的刀线长度
    ste_length = calculate_die_line_ste(box_L, box_W, box_H, box_CS)
    print(f"同向插入盒 (STE) 的刀线长度估算为: {ste_length:.2f} mm")

    # 3. 计算RSC运输箱的刀线长度 (注意：它不需要CS参数)
    rsc_length = calculate_die_line_rsc(box_L, box_W, box_H)
    print(f"标准运输箱 (RSC) 的刀线长度计算为: {rsc_length:.2f} mm")
    
    # 转换为米进行成本核算
    print("\n--- 长度单位转换为米 ---")
    print(f"RTE 盒型: {rte_length / 1000:.3f} m")
    print(f"STE 盒型: {ste_length / 1000:.3f} m")
    print(f"RSC 盒型: {rsc_length / 1000:.3f} m")