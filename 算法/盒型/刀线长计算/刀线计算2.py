import math
import matplotlib.pyplot as plt
from matplotlib.patches import PathPatch
from matplotlib.path import Path

class STE_Dieline_Generator:
    """
    同向插入式卡纸盒 (Straight Tuck End - STE) 刀线生成器
    """
    def __init__(self, L, W, H, T, params=None):
        self.L = L  # 长度
        self.W = W  # 宽度
        self.H = H  # 高度
        self.T = T  # 纸板厚度
        
        # 加载默认参数或用户自定义参数
        default_params = {
            "glue_flap_width": 15,
            "glue_flap_taper_angle": 10,
            "tuck_flap_height_ratio": 0.25,
            "tuck_flap_tolerance": 1.0, # 单边公差
            "tuck_flap_corner_radius": 5.0,
            "dust_flap_ratio": 0.9,
            "dust_flap_taper_angle": 15,
        }
        if params:
            default_params.update(params)
        self.params = default_params

        self.lines = [] # 存储所有线段: {'start': (x,y), 'end': (x,y), 'type': 'CUT'/'CREASE'}

    def generate(self):
        """主生成函数"""
        self._calculate_dimensions()
        self._generate_creases()
        self._generate_glue_flap()
        self._generate_top_structure()
        self._generate_bottom_structure()
        return self.lines

    def _calculate_dimensions(self):
        """计算所有关键尺寸和坐标"""
        # 1. 补偿尺寸
        self.L_comp = self.L + self.T / 2
        self.W_comp = self.W + self.T / 2
        
        # 2. 派生参数
        p = self.params
        self.tf_h = self.H * p['tuck_flap_height_ratio']
        self.df_h = self.W * p['dust_flap_ratio']
        self.df_taper = self.df_h * math.tan(math.radians(p['dust_flap_taper_angle']))

        # 3. 主体压线坐标
        self.x = [0] * 5
        self.x[0] = p['glue_flap_width']
        self.x[1] = self.x[0] + self.L_comp
        self.x[2] = self.x[1] + self.W_comp
        self.x[3] = self.x[2] + self.L_comp
        self.x[4] = self.x[3] + self.W_comp

        self.y_bottom = self.tf_h + self.W
        self.y_top = self.y_bottom + self.H

    def _add_line(self, p1, p2, line_type):
        """辅助函数，添加线段"""
        self.lines.append({'start': p1, 'end': p2, 'type': line_type.upper()})

    def _generate_creases(self):
        """生成主体部分的压痕线"""
        # 垂直压线
        for i in range(4):
            self._add_line((self.x[i], self.y_bottom), (self.x[i], self.y_top), 'CREASE')
        # 水平压线
        self._add_line((self.x[0], self.y_bottom), (self.x[4], self.y_bottom), 'CREASE')
        self._add_line((self.x[0], self.y_top), (self.x[4], self.y_top), 'CREASE')

    def _generate_glue_flap(self):
        """生成胶水边"""
        taper = self.H * math.tan(math.radians(self.params['glue_flap_taper_angle']))
        p1 = (0, self.y_bottom + taper)
        p2 = (self.x[0], self.y_bottom)
        p3 = (self.x[0], self.y_top)
        p4 = (0, self.y_top - taper)
        self._add_line(p1, p2, 'CUT')
        self._add_line(p3, p4, 'CUT')
        self._add_line(p1, p4, 'CUT')
        
    def _generate_tuck_flap(self, panel_x_start, panel_y_start, direction=1):
        """生成插入舌和对应的主盖板 (direction=1 向上, -1 向下)"""
        p = self.params
        tol = p['tuck_flap_tolerance']
        r = p['tuck_flap_corner_radius']
        
        # 主盖板
        mf_y_end = panel_y_start + direction * self.W
        self._add_line((panel_x_start, panel_y_start), (panel_x_start, mf_y_end), 'CUT')
        self._add_line((panel_x_start + self.L_comp, panel_y_start), (panel_x_start + self.L_comp, mf_y_end), 'CUT')
        self._add_line((panel_x_start, mf_y_end), (panel_x_start + self.L_comp, mf_y_end), 'CREASE')
        
        # 插入舌
        tf_y_end = mf_y_end + direction * self.tf_h
        p1 = (panel_x_start + tol, mf_y_end)
        p2 = (panel_x_start + tol + r, mf_y_end)
        p3 = (panel_x_start + self.L_comp - tol - r, mf_y_end)
        p4 = (panel_x_start + self.L_comp - tol, mf_y_end)
        
        p5 = (panel_x_start + tol, tf_y_end - direction * r)
        p6 = (panel_x_start + self.L_comp - tol, tf_y_end - direction * r)

        p7 = (panel_x_start + tol + r, tf_y_end)
        p8 = (panel_x_start + self.L_comp - tol - r, tf_y_end)

        self._add_line(p1, p5, 'CUT') # 左侧直线
        self._add_line(p4, p6, 'CUT') # 右侧直线
        self._add_line(p7, p8, 'CUT') # 顶部直线
        # 绘制圆角 (使用多段线近似)
        self._add_arc(p2, r, 180 if direction==1 else 90, 90)
        self._add_arc(p3, r, 90 if direction==1 else 0, 90)

    def _add_arc(self, center, radius, start_angle, angle_span, segments=8):
        """用多段线绘制圆弧"""
        theta = math.radians(start_angle)
        delta_theta = math.radians(angle_span) / segments
        
        p_start = (center[0] - radius, center[1])
        for i in range(segments + 1):
            x = center[0] + radius * math.cos(theta)
            y = center[1] + radius * math.sin(theta)
            p_end = (x, y)
            if i > 0:
                self._add_line(p_start, p_end, 'CUT')
            p_start = p_end
            theta += delta_theta
            
    def _generate_top_structure(self):
        """生成顶部所有盖板结构"""
        # 后面板 (x0-x1): 主盖板
        self._add_line((self.x[0], self.y_top), (self.x[0], self.y_top + self.W), 'CUT')
        self._add_line((self.x[1], self.y_top), (self.x[1], self.y_top + self.W), 'CUT')
        self._add_line((self.x[0], self.y_top + self.W), (self.x[1], self.y_top + self.W), 'CUT')
        
        # 侧面板 (x1-x2): 防尘翼
        self._add_line((self.x[1], self.y_top), (self.x[1] + self.df_taper, self.y_top + self.df_h), 'CUT')
        self._add_line((self.x[2], self.y_top), (self.x[2] - self.df_taper, self.y_top + self.df_h), 'CUT')
        self._add_line((self.x[1] + self.df_taper, self.y_top + self.df_h), (self.x[2] - self.df_taper, self.y_top + self.df_h), 'CUT')

        # 前面板 (x2-x3): 主盖板 + 插入舌
        self._generate_tuck_flap(self.x[2], self.y_top, direction=1)
        
        # 另一侧面板 (x3-x4): 防尘翼
        self._add_line((self.x[3], self.y_top), (self.x[3] + self.df_taper, self.y_top + self.df_h), 'CUT')
        self._add_line((self.x[4], self.y_top), (self.x[4] - self.df_taper, self.y_top + self.df_h), 'CUT')
        self._add_line((self.x[3] + self.df_taper, self.y_top + self.df_h), (self.x[4] - self.df_taper, self.y_top + self.df_h), 'CUT')
        self._add_line((self.x[4], self.y_top), (self.x[4], self.y_bottom), 'CUT') # 右侧外轮廓

    def _generate_bottom_structure(self):
        """生成底部所有盖板结构"""
        # 后面板 (x0-x1): 主盖板 + 插入舌
        self._generate_tuck_flap(self.x[0], self.y_bottom, direction=-1)
        
        # 侧面板 (x1-x2): 防尘翼
        self._add_line((self.x[1], self.y_bottom), (self.x[1] + self.df_taper, self.y_bottom - self.df_h), 'CUT')
        self._add_line((self.x[2], self.y_bottom), (self.x[2] - self.df_taper, self.y_bottom - self.df_h), 'CUT')
        self._add_line((self.x[1] + self.df_taper, self.y_bottom - self.df_h), (self.x[2] - self.df_taper, self.y_bottom - self.df_h), 'CUT')

        # 前面板 (x2-x3): 主盖板
        self._add_line((self.x[2], self.y_bottom), (self.x[2], self.y_bottom - self.W), 'CUT')
        self._add_line((self.x[3], self.y_bottom), (self.x[3], self.y_bottom - self.W), 'CUT')
        self._add_line((self.x[2], self.y_bottom - self.W), (self.x[3], self.y_bottom - self.W), 'CUT')
        
        # 另一侧面板 (x3-x4): 防尘翼
        self._add_line((self.x[3], self.y_bottom), (self.x[3] + self.df_taper, self.y_bottom - self.df_h), 'CUT')
        self._add_line((self.x[4], self.y_bottom), (self.x[4] - self.df_taper, self.y_bottom - self.df_h), 'CUT')
        self._add_line((self.x[3] + self.df_taper, self.y_bottom - self.df_h), (self.x[4] - self.df_taper, self.y_bottom - self.df_h), 'CUT')

    def plot(self):
        """使用 Matplotlib 可视化刀线图"""
        fig, ax = plt.subplots(figsize=(12, 12))
        
        for line in self.lines:
            p1 = line['start']
            p2 = line['end']
            
            if line['type'] == 'CUT':
                color = 'red'
                linestyle = '-'
            else: # CREASE
                color = 'blue'
                linestyle = '--'
            
            ax.plot([p1[0], p2[0]], [p1[1], p2[1]], color=color, linestyle=linestyle, linewidth=1)

        ax.set_aspect('equal', adjustable='box')
        ax.set_title('STE Dieline (Cut: Red, Crease: Blue-- )')
        ax.set_xlabel('Width (mm)')
        ax.set_ylabel('Height (mm)')
        plt.grid(True, linestyle=':', alpha=0.5)
        plt.show()

# --- 主程序入口 ---
if __name__ == '__main__':
    # 1. 定义盒子尺寸
    box_L = 80  # 长度
    box_W = 50  # 宽度
    box_H = 120 # 高度
    paper_T = 0.5 # 纸板厚度

    # 2. (可选) 自定义参数
    custom_params = {
        "tuck_flap_corner_radius": 8.0,
        "glue_flap_width": 18
    }

    # 3. 创建生成器实例
    generator = STE_Dieline_Generator(L=box_L, W=box_W, H=box_H, T=paper_T, params=custom_params)

    # 4. 生成刀线数据
    dieline_data = generator.generate()

    # 5. 打印部分数据进行检查
    print(f"成功生成 {len(dieline_data)} 条线段。")
    print("前5条线段数据:")
    for i in range(min(5, len(dieline_data))):
        print(dieline_data[i])

    # 6. 可视化刀线图
    generator.plot()