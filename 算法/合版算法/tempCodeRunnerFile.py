def calculate_dynamic_value(product_unfolded_area, machine_processing_area, min_size, max_area_ratio, module_count_adjustment_factor):
    """
    根据成品展开面积、设备加工面积、最小尺寸、最大面积比和模数调整系数计算动态反馈值或参考值。

    Args:
        product_unfolded_area: 成品展开面积。
        machine_processing_area: 设备加工面积。
        min_size: 最小尺寸，用于限制单个产品的最小面积。
        max_area_ratio: 最大面积比 (0-1之间)，用于计算条件 2 的上限，超过此比例则无法拼版。
        module_count_adjustment_factor: 模数调整系数 (0-1)，用于调整动态反馈值/参考值。

    Returns:
        反馈值或参考值 (保留三位小数)。

    Raises:
        ValueError: 如果 max_area_ratio 或 module_count_adjustment_factor 不在 0-1 之间。
        ValueError: 如果最小尺寸小于等于 0。
    """

    if not 0 <= max_area_ratio <= 1:
        raise ValueError("最大面积比 max_area_ratio 必须在 0-1 之间")
    if not 0 <= module_count_adjustment_factor <= 1:
        raise ValueError("模数调整系数 module_count_adjustment_factor 必须在 0-1 之间")
    if min_size <= 0:
        raise ValueError("最小尺寸必须大于零")

    # 1. 计算最小起始尺寸 (使用最小尺寸)
    min_start_size = min_size
    print(f"最小起始尺寸: {min_start_size:.3f}")

    # 2. 计算最大尺寸限制 (设备加工面积 * 最大面积比)
    max_size_limit = machine_processing_area * max_area_ratio
    print(f"最大尺寸限制: {max_size_limit:.3f}")

    # 3. 计算调整后的设定总模数（作为参考值的上限）
    # 使用最小尺寸计算调整后的设定总模数
    adjusted_set_total_module_count = machine_processing_area / min_size * module_count_adjustment_factor  

    # 4. 条件判断和参考值/反馈值计算
    if product_unfolded_area <= min_start_size:
        # 条件 1: 成品展开面积 <= 最小起始尺寸
        print(f"满足条件 1: 成品展开面积({product_unfolded_area:.3f}) <= 最小起始尺寸({min_start_size:.3f})")
        feedback_value = adjusted_set_total_module_count
        print(f"反馈值: {feedback_value:.1f}")
        return round(feedback_value, 3)

    elif min_start_size < product_unfolded_area < max_size_limit:
        # 条件 2: 最小起始尺寸 < 成品展开面积 < 最大尺寸限制
        print(f"满足条件 2: 最小起始尺寸({min_start_size:.3f}) < 成品展开面积({product_unfolded_area:.3f}) < 上限({max_size_limit:.3f})")
        
        # 简化公式，提取中间变量
        area_ratio_in_range = (product_unfolded_area - min_start_size) / (max_size_limit - min_start_size) 
        reference_value = adjusted_set_total_module_count - area_ratio_in_range * (adjusted_set_total_module_count - 1)
        
        reference_value = max(1, reference_value)  # 确保至少为 1
        print(f"参考值: {reference_value:.1f}")
        return round(reference_value, 3)

    else:  # product_unfolded_area >= max_size_limit
        # 条件 3: 成品展开面积 >= 最大尺寸限制
        print(f"满足条件 3: 成品展开面积({product_unfolded_area:.3f}) >= 上限({max_size_limit:.3f})")
        return 1.000


# 输入值：
product_unfolded_area = 0.06  # 成品展开面积

# 设定值：
min_size = 0.01  # 最小尺寸，用于设定最小起始尺寸
machine_processing_area = 0.7  # 设备加工面积，用于设定设备最大加工尺寸

max_area_ratio = 1  # 最大面积比，用于设定最大尺寸限制
module_count_adjustment_factor = 1  # 模数调整系数

try:
    result = calculate_dynamic_value(product_unfolded_area, machine_processing_area, min_size, max_area_ratio, module_count_adjustment_factor)

    print(f"最小尺寸: {min_size:.3f}")
    print(f"最大面积比: {max_area_ratio}")
    print(f"模数调整系数: {module_count_adjustment_factor}")

    if isinstance(result, float):
        if result == 1.000:
            print(f"反馈值: {result:.3f}")
        else:
            print(f"参考值或反馈值: {result:.3f}")
    else:
        print(f"反馈值: {result:.3f}")

except ValueError as e:
    print(f"错误: {e}")