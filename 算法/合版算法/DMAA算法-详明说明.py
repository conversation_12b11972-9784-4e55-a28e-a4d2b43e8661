def calculate_feedback_value(product_unfolded_area, machine_processing_area, practical_module_count, set_total_module_count):
  """
  根据成品展开面积、设备加工面积、实用模数和设定总模数计算反馈值或参考值。

  此函数实现了分段函数，根据成品展开面积的不同情况返回不同的值，用于优化包装合版过程中的排版方案和材料利用率。

  Args:
    product_unfolded_area: 成品展开面积，即产品完全展开后的面积，例如包装盒展开后的平面面积。
    machine_processing_area: 设备加工面积，即设备一次可以加工的最大面积，例如模切机或印刷机的有效工作面积。
    practical_module_count: 实用模数，即实际使用的模数，也就是在一个设备加工面积内实际排放的产品数量。
    set_total_module_count: 设定总模数，即在一个设备加工面积内理论上最多可以排放的产品数量。

  Returns:
    反馈值或参考值，用于优化拼版方案和生产效率，保留小数点后三位。

  Raises:
    ValueError: 当实用模数的取值不在 1 到 设定总模数 之间时，抛出 ValueError 异常。
  """

  # 限制实用模数的取值范围
  if not 1 <= practical_module_count <= set_total_module_count:
    raise ValueError("实用模数的取值范围必须在 1 到 设定总模数 之间")

  # 条件 1：
  # 当“成品展开面积” 小于等于 “设备加工面积” / “设定总模数” 时，
  # 反馈值 = 等于 ”实用模数“
  # 
  # 例如，如果设备加工面积为 1 平方米，设定总模数为 10，
  # 那么单个产品理论上可以占用的面积为 0.1 平方米。
  # 如果成品展开面积小于等于 0.1 平方米，则可以使用最大实用模数进行拼版。
  if product_unfolded_area <= machine_processing_area / set_total_module_count:
    return round(practical_module_count, 3)  # 保留小数点后三位

  # 条件 2：
  # 当“成品展开面积”大于( “设备加工面积” / “设定总模数” )，
  # 且小于 “设备加工面积” * (实用模数 + 1) / “设定总模数” 时，
  # 参考值 = 实用模数 * (1 - 成品展开尺寸 / 设备加工面积)
  #
  # 例如，如果设备加工面积为 1 平方米，设定总模数为 10，实用模数为 5，
  # 那么单个产品理论上可以占用的面积为 0.1 平方米，
  # 按实用模数 + 1 计算的单个产品占用面积为 0.6 平方米。
  # 如果成品展开面积大于 0.1 平方米，小于 0.6 平方米，
  # 则需要根据公式计算参考值，按比例减少实用模数。
  elif product_unfolded_area > machine_processing_area / set_total_module_count and \
       product_unfolded_area < machine_processing_area * (practical_module_count + 1) / set_total_module_count:
    result = practical_module_count * (1 - product_unfolded_area / machine_processing_area)
    return round(result, 3)  # 保留小数点后三位

  # 条件 3：
  # “成品展开面积” 大于等于 “设备加工面积” * (实用模数 + 1) / “设定总模数” 时，
  # 反馈值 = 1
  #
  # 例如，如果设备加工面积为 1 平方米，设定总模数为 10，实用模数为 5，
  # 那么按实用模数 + 1 计算的单个产品占用面积为 0.6 平方米。
  # 如果成品展开面积大于等于 0.6 平方米，则反馈值为 1。
  else:
    return 1  


# 参考数据
product_unfolded_area = 0.060
machine_processing_area = 0.700
practical_module_count = 4.00
set_total_module_count = 8

# 计算反馈值或参考值
result = calculate_feedback_value(product_unfolded_area, machine_processing_area, practical_module_count, set_total_module_count)

# 输出结果
print("计算结果：", result)