def calculate_feedback_value(product_unfolded_area, machine_processing_area, practical_module_count, set_total_module_count):
  """
  根据成品展开面积、设备加工面积、实用模数和设定总模数计算反馈值或参考值。

  此函数实现了分段函数，根据成品展开面积的不同情况返回不同的值。

  Args:
    product_unfolded_area: 成品展开面积，即产品完全展开后的面积，例如包装盒展开后的平面面积。
    machine_processing_area: 设备加工面积，即设备一次可以加工的最大面积。
    practical_module_count: 实用模数，即实际使用的模数，也就是在一个设备加工面积内实际排放的产品数量。
    set_total_module_count: 设定总模数，即在一个设备加工面积内理论上最多可以排放的产品数量。

  Returns:
    反馈值或参考值，用于优化拼版方案和生产效率。
  """

  # 条件 1：
  # 当“成品展开面积” 小于等于 “设备加工面积” / “设定总模数” 时，
  # 反馈值 = 等于 ”实用模数“
  if product_unfolded_area <= machine_processing_area / set_total_module_count:
    return practical_module_count  

  # 条件 2：
  # 当“成品展开面积”大于( “设备加工面积” / “设定总模数” )，
  # 且小于“（实用模数+1）/总模数”*设备加工尺寸时，
  # 参考值 = 实用模数 - 成品展开尺寸 / (设备加工面积 / 设定总模数) * 实用模数 / 设定总模数
  elif product_unfolded_area > machine_processing_area / set_total_module_count and \
       product_unfolded_area < (practical_module_count + 1) / set_total_module_count * machine_processing_area:
    return practical_module_count - product_unfolded_area / (machine_processing_area / set_total_module_count) * practical_module_count / set_total_module_count

  # 条件 3：
  # “成品展开面积”大于 “设备加工面积” * （（实用模数+1）/ “设定总模数”）时，
  # 反馈值 = 1
  else:
    return 1  


# 参考数据
product_unfolded_area = 0.350
machine_processing_area = 0.700
practical_module_count = 6.00
set_total_module_count = 8

# 计算反馈值或参考值
result = calculate_feedback_value(product_unfolded_area, machine_processing_area, practical_module_count, set_total_module_count)

# 输出结果
print(f"计算结果：{round(result, 3):.3f}")