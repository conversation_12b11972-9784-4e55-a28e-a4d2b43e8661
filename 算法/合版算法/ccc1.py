def calculate_total_module_count(machine_processing_area, min_size=0.06):
    """
    根据设备加工面积和最小尺寸计算总模数。

    Args:
        machine_processing_area: 设备加工面积。
        min_size: 最小尺寸，默认为 0.06。

    Returns:
        总模数。
    """
    if min_size <= 0:
        raise ValueError("最小尺寸必须大于零")
    total_module_count = machine_processing_area / min_size
    return total_module_count

def calculate_dynamic_value(product_unfolded_area, machine_processing_area, set_total_module_count, size_ratio_threshold, dynamic_module_ratio):
    """
    根据成品展开面积、设备加工面积、设定总模数、尺寸比例阈值和动态模数比例计算动态反馈值或参考值。

    Args:
        product_unfolded_area: 成品展开面积。
        machine_processing_area: 设备加工面积。
        set_total_module_count: 设定总模数，用于计算最小起始尺寸。
        size_ratio_threshold: 尺寸比例阈值 (0-1之间)，用于计算条件 2 的上限。
        dynamic_module_ratio: 动态模数比例 (0-1)，用于调整动态反馈值/参考值。

    Returns:
        反馈值或参考值 (保留三位小数)。

    Raises:
        ValueError: 如果 size_ratio_threshold 或 dynamic_module_ratio 不在 0-1 之间。
    """

    if not 0 <= size_ratio_threshold <= 1:
        raise ValueError("尺寸比例阈值 size_ratio_threshold 必须在 0-1 之间")
    if not 0 <= dynamic_module_ratio <= 1:
        raise ValueError("动态模数比例 dynamic_module_ratio 必须在 0-1 之间")

    # 1. 计算最小起始尺寸 (设备加工面积 / 设定总模数)
    min_start_size = machine_processing_area / set_total_module_count
    print(f"最小起始尺寸: {min_start_size:.3f}")

    # 2. 计算最大尺寸限制 (设备加工面积 * 尺寸比例阈值)
    max_size_limit = machine_processing_area * size_ratio_threshold
    print(f"最大尺寸限制: {max_size_limit:.3f}")

    # 3. 计算调整后的设定总模数（作为参考值的上限）
    adjusted_set_total_module_count = set_total_module_count * dynamic_module_ratio

    # 4. 条件判断和参考值/反馈值计算
    if product_unfolded_area <= min_start_size:
        # 条件 1: 成品展开面积 <= 最小起始尺寸
        print(f"满足条件 1: 成品展开面积({product_unfolded_area:.3f}) <= 最小起始尺寸({min_start_size:.3f})")
        feedback_value = adjusted_set_total_module_count
        print(f"反馈值: {feedback_value:.1f}")
        return round(feedback_value, 3)

    elif min_start_size < product_unfolded_area < max_size_limit:
        # 条件 2: 最小起始尺寸 < 成品展开面积 < 最大尺寸限制
        print(f"满足条件 2: 最小起始尺寸({min_start_size:.3f}) < 成品展开面积({product_unfolded_area:.3f}) < 上限({max_size_limit:.3f})")
        reference_value = adjusted_set_total_module_count - (product_unfolded_area - min_start_size) / (max_size_limit - min_start_size) * (adjusted_set_total_module_count - 1)
        reference_value = max(1, reference_value)  # 确保至少为 1
        print(f"参考值: {reference_value:.1f}")
        return round(reference_value, 3)

    else:  # product_unfolded_area >= max_size_limit
        # 条件 3: 成品展开面积 >= 最大尺寸限制
        print(f"满足条件 3: 成品展开面积({product_unfolded_area:.3f}) >= 上限({max_size_limit:.3f})")
        return 1.000


# 输入值：
product_unfolded_area = 0.2 # 成品展开面积，成品展开面积

# 设定值：
min_size = 0.06  # 最小尺寸，用于设定最小起始尺寸
machine_processing_area = 0.7  # 设备加工面积，用于设定设备最大加工尺寸及计算设定总模数
set_total_module_count = calculate_total_module_count(machine_processing_area, min_size)
print(f"计算出的设定总模数：{set_total_module_count:.3f}")

size_ratio_threshold = 0.85  # 尺寸比例阈值，用于设定最大尺寸限制，以避免成品展开面积与设备加工面积的比值过大。
dynamic_module_ratio = 0.45  # 动态模数比例，用于调整动态反馈值/参考值，参考值为设定总模数

try:
    result = calculate_dynamic_value(product_unfolded_area, machine_processing_area, set_total_module_count, size_ratio_threshold, dynamic_module_ratio)

    print(f"设定总模数: {set_total_module_count:.1f}")  # 保留一位小数
    print(f"尺寸比例阈值: {size_ratio_threshold}")
    print(f"动态模数比例: {dynamic_module_ratio}")

    if isinstance(result, float):
        if result == 1.000:
            print(f"反馈值: {result:.3f}")
        else:
            print(f"参考值或反馈值: {result:.3f}")
    else:
        print(f"反馈值: {result:.3f}")

except ValueError as e:
    print(f"错误: {e}")