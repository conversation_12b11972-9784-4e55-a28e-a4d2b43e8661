def calculate_set_total_module_count(product_unfolded_area, machine_processing_area, area_factor=1.0):
  """
  根据成品展开面积、设备加工面积和面积系数计算设定总模数。

  Args:
    product_unfolded_area: 成品展开面积。
    machine_processing_area: 设备加工面积。
    area_factor: 面积系数，用于调整设定总模数的大小，默认为 1.0。

  Returns:
    设定总模数。
  """
  # 计算设定总模数
  set_total_module_count = machine_processing_area / product_unfolded_area * area_factor
  return int(set_total_module_count)


def calculate_practical_module_count(product_unfolded_area, machine_processing_area, set_total_module_count, safety_factor):
  """
  根据成品展开面积、设备加工面积和设定总模数计算实用模数。

  Args:
    product_unfolded_area: 成品展开面积。
    machine_processing_area: 设备加工面积。
    set_total_module_count: 设定总模数。
    safety_factor: 安全系数，用于防止用料过度。取值范围应在 0 到 1 之间。

  Returns:
    实用模数。

  Raises:
    ValueError: 当安全系数不在 0 到 1 之间时，抛出 ValueError 异常。
  """

  # 限制安全系数的取值范围
  if not 0 <= safety_factor <= 1:
    raise ValueError("安全系数的取值范围必须在 0 到 1 之间")

  area_ratio = product_unfolded_area / machine_processing_area

  if area_ratio <= 1 / set_total_module_count:
    practical_module_count = set_total_module_count * safety_factor
    return max(1, practical_module_count)  # 保证实用模数至少为 1

  elif area_ratio < safety_factor * (practical_module_count / set_total_module_count):  
    practical_module_count = set_total_module_count * (1 - area_ratio) * safety_factor
    return max(1, practical_module_count)  # 保证实用模数至少为 1

  else:
    return 1


def calculate_feedback_value(product_unfolded_area, machine_processing_area, area_factor, safety_factor, area_ratio_threshold):
  """
  根据成品展开面积、设备加工面积、面积系数、安全系数和面积比阈值计算反馈值或参考值。

  Args:
    product_unfolded_area: 成品展开面积。
    machine_processing_area: 设备加工面积。
    area_factor: 面积系数，用于调整设定总模数的大小。
    safety_factor: 安全系数，用于防止用料过度。取值范围应在 0 到 1 之间。
    area_ratio_threshold: 面积比阈值，用于判断何时将反馈值设置为 1。取值范围应在 0 到 1 之间。

  Returns:
    反馈值或参考值。

  Raises:
    ValueError: 当安全系数或面积比阈值不在 0 到 1 之间时，抛出 ValueError 异常。
  """

  # 限制安全系数和面积比阈值的取值范围
  if not 0 <= safety_factor <= 1:
    raise ValueError("安全系数的取值范围必须在 0 到 1 之间")
  if not 0 <= area_ratio_threshold <= 1:
    raise ValueError("面积比阈值的取值范围必须在 0 到 1 之间")

  # 计算设定总模数
  set_total_module_count = calculate_set_total_module_count(product_unfolded_area, machine_processing_area, area_factor)

  # 计算实用模数
  practical_module_count = calculate_practical_module_count(
      product_unfolded_area,
      machine_processing_area,
      set_total_module_count,
      safety_factor
  )

  area_ratio = product_unfolded_area / machine_processing_area

  # 打印重要数据和信息
  print(f"成品展开面积：{product_unfolded_area}")
  print(f"设备加工面积：{machine_processing_area}")
  print(f"面积系数：{area_factor}")
  print(f"设定总模数：{set_total_module_count}")
  print(f"安全系数：{safety_factor}")
  print(f"面积比阈值：{area_ratio_threshold}")
  print(f"实用模数：{practical_module_count}")

  if area_ratio <= 1 / set_total_module_count:
    return practical_module_count  # 直接返回实用模数

  elif area_ratio < area_ratio_threshold:
    reference_value = practical_module_count * (1 - area_ratio)
    return max(1, reference_value)  # 限制参考值不小于 1

  else:  # area_ratio >= area_ratio_threshold
    return 1

# 参考数据
product_unfolded_area = 0.300
machine_processing_area = 0.700

# 设定安全系数、面积比阈值和面积系数
safety_factor = 0.7  
area_ratio_threshold = 0.85 
area_factor = 1.0  # 可以根据需要调整

# 计算反馈值或参考值
result = calculate_feedback_value(
    product_unfolded_area, 
    machine_processing_area, 
    area_factor,  # 传入面积系数
    safety_factor,  # 传入安全系数
    area_ratio_threshold  # 传入面积比阈值
)

# 输出结果
print("最终计算结果：", result)  # 输出结果：1.75