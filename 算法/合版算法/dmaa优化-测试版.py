def calculate_practical_module_count(product_unfolded_area, machine_processing_area, set_total_module_count, safety_factor):
  """
  根据成品展开面积、设备加工面积和设定总模数计算实用模数。

  实用模数指的是在一个设备加工面积内实际排放的产品数量，
  它受成品展开面积、设备加工面积、设定总模数和安全系数的影响。

  Args:
    product_unfolded_area: 成品展开面积，即产品完全展开后的面积，例如包装盒展开后的平面面积。
    machine_processing_area: 设备加工面积，即设备一次可以加工的最大面积，例如模切机或印刷机的有效工作面积。
    set_total_module_count: 设定总模数，即在一个设备加工面积内理论上最多可以排放的产品数量，
                             用于限制最小使用面积，防止尺寸过小导致拼版数过多。
    safety_factor: 安全系数，用于防止用料过度。取值范围应在 0 到 1 之间。
                   例如，如果希望更保守，可以使用更小的安全系数，例如 0.7。

  Returns:
    实用模数，一个数值，表示实际排放的产品数量。

  Raises:
    ValueError: 当安全系数不在 0 到 1 之间时，抛出 ValueError 异常。
  """

  # 限制安全系数的取值范围
  if not 0 <= safety_factor <= 1:
    raise ValueError("安全系数的取值范围必须在 0 到 1 之间")

  # 计算成品展开面积与设备加工面积的比值
  area_ratio = product_unfolded_area / machine_processing_area

  # 条件 1：
  # 当“成品展开面积” 小于等于 “设备加工面积” / “设定总模数” 时，
  # 即单个产品面积小于等于理论单个产品占用面积时，
  # 实用模数 = 设定总模数 * 安全系数
  if area_ratio <= 1 / set_total_module_count:
    practical_module_count = set_total_module_count * safety_factor
    return max(1, practical_module_count)  # 保证实用模数至少为 1，避免出现 0 或负数

  # 条件 2：
  # 当 “成品展开面积” 大于 (“设备加工面积” / “设定总模数”)，
  # 且小于 safety_factor 时，
  # 实用模数 = 设定总模数 * (1 - 面积比值) * 安全系数
  elif area_ratio < safety_factor:
    practical_module_count = set_total_module_count * (1 - area_ratio) * safety_factor
    return max(1, practical_module_count)  # 保证实用模数至少为 1，避免出现 0 或负数

  # 条件 3：
  # “成品展开面积” 大于等于 safety_factor 时，
  # 实用模数 = 1
  else:
    return 1


def calculate_feedback_value(product_unfolded_area, machine_processing_area, set_total_module_count, safety_factor, area_ratio_threshold):
  """
  根据成品展开面积、设备加工面积和设定总模数计算反馈值或参考值。

  此函数实现了分段函数，根据成品展开面积的不同情况返回不同的值，
  用于优化包装合版过程中的排版方案和材料利用率。

  Args:
    product_unfolded_area: 成品展开面积，即产品完全展开后的面积，例如包装盒展开后的平面面积。
    machine_processing_area: 设备加工面积，即设备一次可以加工的最大面积，例如模切机或印刷机的有效工作面积。
    set_total_module_count: 设定总模数，即在一个设备加工面积内理论上最多可以排放的产品数量。
    safety_factor: 安全系数，用于防止用料过度。取值范围应在 0 到 1 之间。
    area_ratio_threshold: 面积比阈值，用于判断何时将反馈值设置为 1。取值范围应在 0 到 1 之间。

  Returns:
    反馈值或参考值，用于优化拼版方案和生产效率。

  Raises:
    ValueError: 当安全系数或面积比阈值不在 0 到 1 之间时，抛出 ValueError 异常。
  """

  # 限制安全系数和面积比阈值的取值范围
  if not 0 <= safety_factor <= 1:
    raise ValueError("安全系数的取值范围必须在 0 到 1 之间")
  if not 0 <= area_ratio_threshold <= 1:
    raise ValueError("面积比阈值的取值范围必须在 0 到 1 之间")

  # 计算实用模数
  practical_module_count = calculate_practical_module_count(
      product_unfolded_area, 
      machine_processing_area, 
      set_total_module_count, 
      safety_factor  # 使用同一个 safety_factor 变量
  )

  # 计算成品展开面积与设备加工面积的比值
  area_ratio = product_unfolded_area / machine_processing_area

  # 打印重要数据和信息
  print(f"成品展开面积：{product_unfolded_area}")
  print(f"设备加工面积：{machine_processing_area}")
  print(f"设定总模数：{set_total_module_count}")
  print(f"安全系数：{safety_factor}")
  print(f"面积比阈值：{area_ratio_threshold}")
  print(f"实用模数：{practical_module_count}")

  # 条件 1：
  # 当面积比值小于等于 1 / 设定总模数时，反馈值等于实用模数
  if area_ratio <= 1 / set_total_module_count:
    return practical_module_count  # 直接返回实用模数

  # 条件 2：
  # 当面积比值大于 1 / 设定总模数，且小于面积比阈值时，
  # 参考值 = 实用模数 * (1 - 面积比值)
  elif area_ratio < area_ratio_threshold:
    reference_value = practical_module_count * (1 - area_ratio)  # 使用实用模数计算参考值
    return max(1, reference_value)  # 限制参考值不小于 1，避免出现 0 或负数

  # 条件 3：
  # 当面积比值大于等于面积比阈值时，反馈值 = 1
  else:  # area_ratio >= area_ratio_threshold
    return 1


# 参考数据
product_unfolded_area = 0.51 #成品展开面积
machine_processing_area = 0.700 #设备加工面积
set_total_module_count = 8 #设定总模数

# 设定安全系数和面积比阈值
safety_factor = 0.8  # 安全系数
area_ratio_threshold = 0.85  # 面积比阈值

# 计算反馈值或参考值
result = calculate_feedback_value(
    product_unfolded_area, 
    machine_processing_area, 
    set_total_module_count, 
    safety_factor,  # 传入安全系数
    area_ratio_threshold  # 传入面积比阈值
)

# 输出结果
print("最终计算结果：", result)  # 输出结果：1.75