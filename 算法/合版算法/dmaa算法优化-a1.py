def calculate_practical_module_count(product_unfolded_area, machine_processing_area, set_total_module_count, safety_factor=0.8):
  """
  根据成品展开面积、设备加工面积和设定总模数计算实用模数。

  实用模数指的是在一个设备加工面积内实际排放的产品数量，
  它受成品展开面积、设备加工面积、设定总模数和安全系数的影响。

  Args:
    product_unfolded_area: 成品展开面积。
    machine_processing_area: 设备加工面积。
    set_total_module_count: 设定总模数。
    safety_factor: 安全系数，用于防止用料过度，默认为 0.8。

  Returns:
    实用模数。
  """

  area_ratio = product_unfolded_area / machine_processing_area

  if area_ratio <= 1 / set_total_module_count:
    return int(set_total_module_count * safety_factor)  # 加入安全系数

  elif area_ratio < 0.8:  # 合并条件 2 和条件 3
    temp_module_count = int(set_total_module_count * (1 - area_ratio) * safety_factor)  # 计算临时实用模数
    return max(1, temp_module_count)  # 保证实用模数至少为 1

  else:
    return 1


def calculate_feedback_value(product_unfolded_area, machine_processing_area, set_total_module_count, safety_factor=0.8):
  """
  根据成品展开面积、设备加工面积和设定总模数计算反馈值或参考值。

  此函数实现了分段函数，根据成品展开面积的不同情况返回不同的值，
  用于优化包装合版过程中的排版方案和材料利用率。

  Args:
    product_unfolded_area: 成品展开面积，即产品完全展开后的面积，例如包装盒展开后的平面面积。
    machine_processing_area: 设备加工面积，即设备一次可以加工的最大面积，例如模切机或印刷机的有效工作面积。
    set_total_module_count: 设定总模数，即在一个设备加工面积内理论上最多可以排放的产品数量。
    safety_factor: 安全系数，用于防止用料过度，默认为 0.8。可以根据实际情况调整，例如，
                   如果希望更保守，可以使用更小的安全系数，例如 0.7。

  Returns:
    反馈值或参考值，用于优化拼版方案和生产效率。
  """

  # 计算实用模数
  practical_module_count = calculate_practical_module_count(
      product_unfolded_area, 
      machine_processing_area, 
      set_total_module_count, 
      safety_factor
  )

  area_ratio = product_unfolded_area / machine_processing_area

  # 条件 1：
  if area_ratio <= 1 / set_total_module_count:
    return practical_module_count  # 直接返回实用模数

  # 条件 2：
  elif area_ratio < 0.85:  # 移除冗余条件
    # 使用实用模数计算参考值
    reference_value = practical_module_count * (1 - area_ratio)  
    return reference_value

  # 条件 3：
  else:  # area_ratio >= 0.85 
    return 1


# 参考数据
product_unfolded_area = 0.100
machine_processing_area = 0.700
set_total_module_count = 8

# 计算反馈值或参考值
result = calculate_feedback_value(product_unfolded_area, machine_processing_area, set_total_module_count)

# 输出结果
print("计算结果：", result)  # 输出结果：2.5714285714285716