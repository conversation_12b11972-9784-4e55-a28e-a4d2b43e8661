def calc_dyn_val(unfold_area, machine_area, min_size, max_area_ratio, adjust_factor):
    """
    Dynamic Area-based Imposition Module Calculation Algorithm (DAIMCA)

    根据成品展开面积、设备加工面积、最小加工尺寸、最大面积利用率和模数调整系数计算动态反馈值。

    Args:
        unfold_area: 成品展开面积。
        machine_area: 设备加工面积。
        min_size: 最小加工尺寸，用于限制单个产品的最小面积。
        max_area_ratio: 最大面积利用率 (0-1之间)，用于计算条件 2 的上限，超过此比例则无法拼版。
        adjust_factor: 模数调整系数 (0-1)，用于调整动态反馈值。

    Returns:
        反馈值 (保留三位小数)。

    Raises:
        ValueError: 如果 最大面积利用率 或 模数调整系数 不在 0-1 之间。
        ValueError: 如果 最小加工尺寸 小于等于 0。
        ValueError: 如果 成品展开面积 大于等于 设备加工面积。
    """

    if not 0 <= max_area_ratio <= 1:
        raise ValueError("最大面积利用率 必须在 0-1 之间")
    if not 0 <= adjust_factor <= 1:
        raise ValueError("模数调整系数 必须在 0-1 之间")
    if min_size <= 0:
        raise ValueError("最小加工尺寸 必须大于零")
    if unfold_area >= machine_area:
        raise ValueError("成品展开面积 必须小于 设备加工面积")

    # 计算最大尺寸限制 (设备加工面积 * 最大面积利用率)
    max_size_limit = machine_area * max_area_ratio
    print(f"最大尺寸限制: {max_size_limit:.3f}")

    # 条件判断和反馈值计算
    if unfold_area <= min_size:  # 成品展开面积小于等于最小加工尺寸
        feedback_val = machine_area / min_size * adjust_factor
        feedback_val = max(1, feedback_val)  # 确保反馈值不小于 1
        print(f"满足条件 1: 成品展开面积({unfold_area:.3f}) <= 最小加工尺寸({min_size:.3f})")
        print(f"反馈值: {feedback_val:.3f}")
        return round(feedback_val, 3)

    elif min_size < unfold_area < max_size_limit:  # 成品展开面积大于最小加工尺寸，且小于设备加工面积 * 最大面积利用率
        feedback_val = machine_area / unfold_area * adjust_factor
        feedback_val = max(1, feedback_val)  # 确保反馈值不小于 1
        print(f"满足条件 2: 最小加工尺寸({min_size:.3f}) < 成品展开面积({unfold_area:.3f}) < 上限({max_size_limit:.3f})")
        print(f"反馈值: {feedback_val:.3f}")
        return round(feedback_val, 3)

    else:  # 成品展开面积大于等于设备加工面积 * 最大面积利用率
        print(f"满足条件 3: 成品展开面积({unfold_area:.3f}) >= 上限({max_size_limit:.3f})")
        print(f"反馈值: {1.000:.3f}")
        return 1.000

# 输入值：
unfold_area = 0.06  # 成品展开面积

# 设定值：
min_size = 0.08  # 最小尺寸，用于设定最小起始尺寸
machine_area = 0.7  # 设备加工面积，用于设定设备最大加工尺寸

max_area_ratio = 0.85  # 最大面积比，用于设定最大尺寸限制
adjust_factor = 0.6  # 模数调整系数

try:
    result = calc_dyn_val(unfold_area, machine_area, min_size, max_area_ratio, adjust_factor)

    print(f"\n重要参数:")
    print(f"最小尺寸: {min_size:.3f}")
    print(f"最大面积比: {max_area_ratio}")
    print(f"模数调整系数: {adjust_factor}")
    print(f"最终反馈值: {result:.3f}")

except ValueError as e:
    print(f"错误: {e}")