def calculate_practical_module_count(product_unfolded_area, machine_processing_area, set_total_module_count, safety_factor):
  """
  根据成品展开面积、设备加工面积和设定总模数计算实用模数。

  实用模数指的是在一个设备加工面积内实际排放的产品数量，
  它受成品展开面积、设备加工面积、设定总模数和安全系数的影响。

  Args:
    product_unfolded_area: 成品展开面积。
    machine_processing_area: 设备加工面积。
    set_total_module_count: 设定总模数。
    safety_factor: 安全系数，用于防止用料过度。取值范围应在 0 到 1 之间。

  Returns:
    实用模数。

  Raises:
    ValueError: 当安全系数不在 0 到 1 之间时，抛出 ValueError 异常。
  """

  # 限制安全系数的取值范围
  if not 0 <= safety_factor <= 1:
    raise ValueError("安全系数的取值范围必须在 0 到 1 之间")

  area_ratio = product_unfolded_area / machine_processing_area

  # 条件 1：
  if area_ratio <= 1 / set_total_module_count:
    practical_module_count = set_total_module_count * safety_factor
    return max(1, practical_module_count)  # 保证实用模数至少为 1

  # 条件 2：
  elif area_ratio < safety_factor:  
    practical_module_count = set_total_module_count * (1 - area_ratio) * safety_factor
    return max(1, practical_module_count)  # 保证实用模数至少为 1

  # 条件 3：
  else:  # area_ratio >= safety_factor
    return 1


# 示例数据
product_unfolded_area = 0.10
machine_processing_area = 0.700
set_total_module_count = 8
safety_factor = 0.8

# 计算实用模数
practical_module_count = calculate_practical_module_count(
    product_unfolded_area, 
    machine_processing_area, 
    set_total_module_count, 
    safety_factor
)

# 输出结果
print("实用模数：", practical_module_count)  # 输出结果：4.2