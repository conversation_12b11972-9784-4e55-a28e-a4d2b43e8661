def calculate_value(product_unfolded_area, machine_processing_area, practical_module_count, set_total_module_count, size_ratio_threshold):
  """
  根据成品展开面积、设备加工面积、实用模数、设定总模数和尺寸比例阈值计算反馈值或参考值。

  此函数根据以下条件计算反馈值或参考值：
  1. 当 “成品展开面积” 小于等于 (“设备加工面积” / “设定总模数”) 时，
     反馈值 = 实用模数。
  2. 当 “成品展开面积” 大于 (“设备加工面积” / “设定总模数”)，
     且小于 “设备加工面积” * ((实用模数) / “设定总模数” * 尺寸比例阈值) 时，
     参考值 = 实用模数 * (1 - 成品展开尺寸 / 设备加工面积)。
  3. 当 “成品展开面积” 大于等于 “设备加工面积” * ((实用模数) / “设定总模数” * 尺寸比例阈值) 时，
     反馈值 = 1。

  Args:
    product_unfolded_area: 成品展开面积。
    machine_processing_area: 设备加工面积。
    practical_module_count: 实用模数。
    set_total_module_count: 设定总模数。
    size_ratio_threshold: 尺寸比例阈值 (0-1之间)。

  Returns:
    反馈值或参考值。

  Raises:
    ValueError: 如果 size_ratio_threshold 不在 0-1 之间。
  """

  if not 0 <= size_ratio_threshold <= 1:
    raise ValueError("尺寸比例阈值 size_ratio_threshold 必须在 0-1 之间")

  # 1. 计算最小起始尺寸 (设备加工面积 / 设定总模数)
  min_start_size = machine_processing_area / set_total_module_count
  print(f"最小起始尺寸: {round(min_start_size, 3):.3f}")

  # 2. 条件判断
  if product_unfolded_area <= min_start_size:
    # 条件 1: 成品展开面积 <= 最小起始尺寸
    print(f"满足条件 1: 成品展开面积({product_unfolded_area:.3f}) <= 最小起始尺寸({min_start_size:.3f})")
    return practical_module_count
  elif product_unfolded_area < machine_processing_area * (practical_module_count / set_total_module_count * size_ratio_threshold):
    # 条件 2: 最小起始尺寸 < 成品展开面积 < 设备加工面积 * (实用模数 / 设定总模数 * 尺寸比例阈值)
    print(f"满足条件 2: 最小起始尺寸({min_start_size:.3f}) < 成品展开面积({product_unfolded_area:.3f}) < 上限({machine_processing_area * (practical_module_count / set_total_module_count * size_ratio_threshold):.3f})")
    reference_value = practical_module_count * (1 - product_unfolded_area / machine_processing_area)
    return reference_value
  else:
    # 条件 3: 成品展开面积 >= 设备加工面积 * (实用模数 / 设定总模数 * 尺寸比例阈值)
    print(f"满足条件 3: 成品展开面积({product_unfolded_area:.3f}) >= 上限({machine_processing_area * (practical_module_count / set_total_module_count * size_ratio_threshold):.3f})")
    return 1

# 示例
product_unfolded_area = 0.1  # 成品展开面积
machine_processing_area = 0.7  # 设备加工面积
practical_module_count = 6  # 实用模数
set_total_module_count = 8  # 设定总模数
size_ratio_threshold = 0.8 # 尺寸比例阈值

try:
  result = calculate_value(product_unfolded_area, machine_processing_area, practical_module_count, set_total_module_count, size_ratio_threshold)

  print(f"实用模数: {practical_module_count}")
  print(f"设定总模数: {set_total_module_count}")
  print(f"尺寸比例阈值: {size_ratio_threshold}")

  if isinstance(result, float):
      if result == 1:
          print(f"反馈值: {result}")
      else:
          print(f"参考值: {round(result, 3):.3f}")
  else:
      print(f"反馈值: {result}")

except ValueError as e:
  print(f"错误: {e}")