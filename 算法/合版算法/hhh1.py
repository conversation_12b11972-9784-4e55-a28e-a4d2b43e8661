def calculate_dynamic_value(成品展开面积, 设备加工面积, 最小加工尺寸, 最大面积利用率, 模数调整系数):
    """
    根据成品展开面积、设备加工面积、最小加工尺寸、最大面积利用率和模数调整系数计算动态反馈值。

    Args:
        成品展开面积: 成品展开面积。
        设备加工面积: 设备加工面积。
        最小加工尺寸: 最小加工尺寸，用于限制单个产品的最小面积。
        最大面积利用率: 最大面积利用率 (0-1之间)，用于计算条件 2 的上限，超过此比例则无法拼版。
        模数调整系数: 模数调整系数 (0-1)，用于调整动态反馈值。

    Returns:
        反馈值 (保留三位小数)。

    Raises:
        ValueError: 如果 最大面积利用率 或 模数调整系数 不在 0-1 之间。
        ValueError: 如果 最小加工尺寸 小于等于 0。
        ValueError: 如果 成品展开面积 大于等于 设备加工面积。
    """

    if not 0 <= 最大面积利用率 <= 1:
        raise ValueError("最大面积利用率 必须在 0-1 之间")
    if not 0 <= 模数调整系数 <= 1:
        raise ValueError("模数调整系数 必须在 0-1 之间")
    if 最小加工尺寸 <= 0:
        raise ValueError("最小加工尺寸 必须大于零")
    if 成品展开面积 >= 设备加工面积:
        raise ValueError("成品展开面积 必须小于 设备加工面积")

    # 计算最大尺寸限制 (设备加工面积 * 最大面积利用率)
    max_size_limit = 设备加工面积 * 最大面积利用率
    print(f"最大尺寸限制: {max_size_limit:.3f}")

    # 条件判断和反馈值计算
    if 成品展开面积 <= 最小加工尺寸:  # 成品展开面积小于等于最小加工尺寸
        feedback_value = 设备加工面积 / 最小加工尺寸 * 模数调整系数
        feedback_value = max(1, feedback_value)  # 确保反馈值不小于 1
        print(f"满足条件 1: 成品展开面积({成品展开面积:.3f}) <= 最小加工尺寸({最小加工尺寸:.3f})")
        print(f"反馈值: {feedback_value:.3f}")
        return round(feedback_value, 3)

    elif 最小加工尺寸 < 成品展开面积 < max_size_limit:  # 成品展开面积大于最小加工尺寸，且小于设备加工面积 * 最大面积利用率
        feedback_value = 设备加工面积 / 成品展开面积 * 模数调整系数
        feedback_value = max(1, feedback_value)  # 确保反馈值不小于 1
        print(f"满足条件 2: 最小加工尺寸({最小加工尺寸:.3f}) < 成品展开面积({成品展开面积:.3f}) < 上限({max_size_limit:.3f})")
        print(f"反馈值: {feedback_value:.3f}")
        return round(feedback_value, 3)

    else:  # 成品展开面积大于等于设备加工面积 * 最大面积利用率
        print(f"满足条件 3: 成品展开面积({成品展开面积:.3f}) >= 上限({max_size_limit:.3f})")
        print(f"反馈值: {1.000:.3f}")
        return 1.000

# 输入值：
product_unfolded_area = 0.06  # 成品展开面积

# 设定值：
min_size = 0.07  # 最小尺寸，用于设定最小起始尺寸
machine_processing_area = 0.7  # 设备加工面积，用于设定设备最大加工尺寸

max_area_ratio = 0.85  # 最大面积比，用于设定最大尺寸限制
module_count_adjustment_factor = 0.6  # 模数调整系数

try:
    result = calculate_dynamic_value(product_unfolded_area, machine_processing_area, min_size, max_area_ratio, module_count_adjustment_factor)

    print(f"\n重要参数:")
    print(f"最小尺寸: {min_size:.3f}")
    print(f"最大面积比: {max_area_ratio}")
    print(f"模数调整系数: {module_count_adjustment_factor}")
    print(f"最终反馈值: {result:.3f}")

except ValueError as e:
    print(f"错误: {e}")