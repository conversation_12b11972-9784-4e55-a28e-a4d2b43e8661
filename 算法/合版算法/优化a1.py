import math

def calculate_feedback_value(product_unfolded_area, machine_processing_area, base_module_count, r, adjustment_factor=0.5):
  """
  根据成品展开面积、设备加工面积、基准模数和模数比例因子计算反馈值或参考值。

  此函数实现了分段函数，根据成品展开面积的不同情况返回不同的值。
  引入了模数比例因子 r 来同时调节实用模数和设定总模数之间的关系。

  Args:
    product_unfolded_area: 成品展开面积，即产品完全展开后的面积，例如包装盒展开后的平面面积。
    machine_processing_area: 设备加工面积，即设备一次可以加工的最大面积。
    base_module_count: 基准模数，一个理想的、经验的或初步估算的模数。
    r: 模数比例因子 (0 < r <= 1)，表示实用模数与基准模数的比例。
    adjustment_factor: 调节因子，用于控制设定总模数相对于实用模数的增长幅度，默认为 0.5。

  Returns:
    反馈值或参考值，用于优化拼版方案和生产效率。

  Raises:
    ValueError: 如果参数输入不合法。
  """

  # 参数校验
  if not (0 < product_unfolded_area < machine_processing_area):
    raise ValueError("产品展开面积必须大于0且小于设备加工面积")
  if not (0 < r <= 1):
    raise ValueError("模数比例因子 r 必须在 (0, 1] 范围内")
  if base_module_count <= 0:
    raise ValueError("基准模数 base_module_count 必须大于 0")
  if adjustment_factor < 0:
    raise ValueError("调节因子 adjustment_factor 不能小于 0")

  # 使用 r 调节实用模数和设定总模数
  practical_module_count = r * base_module_count
  set_total_module_count = base_module_count + (1 - r) * base_module_count * adjustment_factor

  # 确保设定总模数不小于实用模数
  set_total_module_count = max(set_total_module_count, practical_module_count)

  # 优化点 1：提前计算常用值，避免重复计算
  theoretical_unit_area = machine_processing_area / set_total_module_count

  # 条件 1：
  if product_unfolded_area <= theoretical_unit_area:
    return practical_module_count

  # 条件 2：
  elif product_unfolded_area < (practical_module_count + 1) * theoretical_unit_area:
    # 优化点 2：简化公式，提高可读性
    return practical_module_count - (product_unfolded_area / theoretical_unit_area) * (practical_module_count / set_total_module_count)

  # 条件 3：
  else:
    return 1

# 示例：计算单个数据的反馈值
product_unfolded_area = 0.50  # 成品展开面积
machine_processing_area = 0.700  # 设备加工面积
base_module_count = 8  # 基准模数
r = 0.9  # 模数比例因子

try:
  feedback = calculate_feedback_value(product_unfolded_area, machine_processing_area, base_module_count, r)
  print(f"产品展开面积: {product_unfolded_area}")
  print(f"反馈值: {round(feedback, 3):.3f}")

  # 计算并打印实用模数和设定总模数
  practical_module_count = r * base_module_count
  set_total_module_count = base_module_count + (1 - r) * base_module_count * 0.5
  print(f"实用模数: {round(practical_module_count,1)}")
  print(f"设定总模数: {round(set_total_module_count,1)}")

except ValueError as e:
  print(f"计算出错: {e}")