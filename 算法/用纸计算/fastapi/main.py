# main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any

# 导入原始的计算函数
# 假设用纸算法.py在用纸计算目录下，与fastapi目录同级
import sys
import os

# 获取当前文件所在的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取用纸计算目录的路径
paper_calculation_dir = os.path.join(current_dir, "..")
# 将用纸计算目录添加到sys.path
sys.path.append(paper_calculation_dir)

try:
    from 用纸算法 import calculate_layout_and_paper_cost, calculate_spoilage
except ImportError:
    # 如果直接导入失败，尝试使用文件路径导入
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from 用纸算法 import calculate_layout_and_paper_cost, calculate_spoilage


app = FastAPI()

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
)

class SpoilageRules(BaseModel):
    base_quantity: int
    base_spoilage: int
    increment_quantity: int
    increment_spoilage: int

class PaperCalculationInput(BaseModel):
    equipment_length: float
    equipment_width: float
    product_length: float
    product_width: float
    bleed: float
    order_quantity: int
    unit_price_per_square_meter: float
    spoilage_rules: SpoilageRules
    margin: float

@app.post("/calculate_paper_cost/")
def calculate_paper_cost_api(data: PaperCalculationInput):
    """
    计算排版、所需纸张尺寸和用纸成本的API。
    """
    spoilage_rules_dict = data.spoilage_rules.model_dump() # 使用 model_dump() 将 Pydantic 模型转换为字典

    result = calculate_layout_and_paper_cost(
        data.equipment_length,
        data.equipment_width,
        data.product_length,
        data.product_width,
        data.bleed,
        data.order_quantity,
        data.unit_price_per_square_meter,
        spoilage_rules_dict,
        data.margin
    )
    return {"result": result}

# 可选：添加一个简单的根路由
@app.get("/")
def read_root():
    return {"message": "用纸计算 FastAPI 服务"}
